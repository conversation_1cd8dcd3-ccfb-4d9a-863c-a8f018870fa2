<div class="flex grow flex-col px-6 py-3 bg-white border-2 border-gray-300 rounded-lg dark:border-gray-600 min-h-32 md:min-h-[16rem] h-fit">
    <p class="font-bold text-2xl mb-1"><PERSON><PERSON><PERSON> <PERSON><PERSON>:</p>
    <hr>
    <div class="flex flex-col grow gap-y-2 px-1 lg:px-2 xl:px-6 py-3">
        {% for t in taskBoxes.autor.tasks %}
            <div class="flex justify-between">
                <a href="{{ path('app_poznamka_detail', {id: t.id}) }}" class="text-lg truncate text-blue-500">{{ t.obsah | get_text_from_delta }}</a>
                <p class="text-lg {% if t.termin|date('U') < "now"|date('U') %}text-red-500{% endif %}">{{ t.termin|date('d.m.') }}</p>
            </div>
        {% endfor %}
        {% if taskBoxes.autor.tasks|length != 0 %}<hr>{% endif %}
        <div class="flex justify-between">
            <p class="text-lg">Celkem</p>
            <p class="text-lg">{{ taskBoxes.autor.count }}</p>
        </div>
    </div>
</div>
