{% if puCounts is null %}
      {{ include('dashboard/_placeholder_small.html.twig') }}
{% else %}
    <div class="flex flex-col px-6 py-3 bg-white border-2 border-gray-300 rounded-lg dark:border-gray-600 min-h-32 md:min-h-[16rem] h-fit">
        <p class="font-bold text-2xl mb-1">Pojistné u<PERSON>:</p>
        <hr>
        <div class="flex flex-col grow gap-y-2 px-1 lg:px-2 xl:px-6 py-3">
            {% for category, count in puCounts %}
                <div class="flex justify-between">
                    <p class="text-lg">{{ category }}</p>
                    <p class="text-lg">{{ count }}</p>
                </div>
            {% endfor %}
        </div>
    </div>
{% endif %}
