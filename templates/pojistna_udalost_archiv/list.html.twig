{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block canvas_content %}
  <section class="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5 h-screen">
    <div class="px-4 mx-auto max-w-screen-2xl lg:px-12">
      <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
        {# counter section #}

        <div class="px-4 divide-y dark:divide-gray-700">
          <div class="flex flex-col py-3 space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:space-x-4">
            <div class="flex items-center flex-1 space-x-4">
              <h5>
                <span class="text-gray-500">Celkem událostí:</span>
                <span class="dark:text-white">{{ pagination.getTotalItemCount }}</span>
              </h5>
            </div>
          </div>

          {# controls section #}

          <div class="flex flex-col items-stretch justify-between py-4 space-y-3 md:flex-row md:items-center md:space-y-0">
            <form class="w-full md:max-w-sm flex-1 md:mr-4">
              <label for="default-search" class="text-sm font-medium text-gray-900 sr-only dark:text-white">Hledat</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <svg aria-hidden="true" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input type="search" id="default-search" class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Hledat..." required="" />
                <button type="submit" class="text-white absolute right-0 bottom-0 top-0 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-r-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">Hledat</button>
              </div>
            </form>

            <div>
              <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown" type="button" class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" />
                </svg>Filtrovat<svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                </svg>
              </button>

              <!-- Dropdown menu -->
              <div id="filterDropdown" class="z-10 hidden p-3 bg-white rounded-lg shadow w-72 dark:bg-gray-700 overflow-visible">
                <div class="mb-4 border-b border-gray-200 dark:border-gray-600">
                  <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="myTab" data-tabs-toggle="#myTabContent" role="tablist">
                    <li class="mr-1" role="presentation">
                      <button class="inline-block pb-2 pr-1" id="date-tab" data-tabs-target="#date" type="button" role="tab" aria-controls="date" aria-selected="false">Datum</button>
                    </li>
                    <li class="mr-1" role="presentation">
                      <button class="inline-block pb-2 pr-1" id="category-tab" data-tabs-target="#category" type="button" role="tab" aria-controls="category" aria-selected="false">Kategorie</button>
                    </li>
                    <li class="mr-1" role="presentation">
                      <button class="inline-block pb-2 pr-1" id="ruzne-tab" data-tabs-target="#ruzne" type="button" role="tab" aria-controls="ruzne" aria-selected="false">Různé</button>
                    </li>
                  </ul>
                </div>

                <div id="myTabContent">
                  <div class="grid grid-cols-2 gap-4" id="date" role="tabpanel" aria-labelledby="date-tab">
                    <div class="flex items-center justify-between col-span-2 space-x-3">
                      <div class="w-full">
                        <label for="min-experience-input" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Od</label>

                        <input type="date" id="date-from" value="300" min="1" max="10000" class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 sm:text-xs focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="" required />
                      </div>

                      <div class="w-full">
                        <label for="date-to" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Do</label>

                        <input type="date" id="max-experience-input" value="3500" min="1" max="10000" class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 sm:text-xs focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="" required />
                      </div>
                    </div>
                  </div>
                  <div class="space-y-2" id="category" role="tabpanel" aria-labelledby="category-tab">
                    <div class="flex items-center">
                      <input id="worldwide" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />

                      <label for="worldwide" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Majetek</label>
                    </div>
                    <div class="flex items-center">
                      <input id="america" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />

                      <label for="america" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Odpovědnost</label>
                    </div>
                    <div class="flex items-center">
                      <input id="europe" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />

                      <label for="europe" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Auto - Pov</label>
                    </div>
                    <div class="flex items-center">
                      <input id="asia" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" />

                      <label for="asia" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Auto - Hav</label>
                    </div>
                  </div>
                  <div id="ruzne" role="tabpanel" aria-labelledby="ruzne-tab">
                    <h6 class="mt-4 mb-2 text-sm font-medium text-gray-900 dark:text-white">Podle likvidátora</h6>
                    <select id="include" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                      <option value="" selected disabled>Vyber likvidátora</option>
                      <option>první jméno</option>
                      <option>druhé jméno</option>
                      <option>třetí jméno</option>
                      <option>zbytek jmen ze seznamu likvidátorů</option>
                    </select>
                    <h6 class="mt-4 mb-2 text-sm font-medium text-gray-900 dark:text-white">Podle místa</h6>
                    <select id="include" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                      <option value="" selected disabled>Vyber místo</option>
                      <option>Kdekoliv</option>
                      <option>Plzeň</option>
                      <option>Klatovy</option>
                      <option>Karlovy Vary</option>
                    </select>
                    <h6 class="mt-4 mb-2 text-sm font-medium text-gray-900 dark:text-white">Podle Zadavatele</h6>
                    <select id="include" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                      <option value="" selected disabled>Vyber zadavatele</option>
                      <option>ČSOB</option>
                      <option>Direct</option>
                      <option>Komerční pojitovna</option>
                      <option>Další ze seznamu zadavatelů</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {# table #}

        <div class="overflow-x-auto">
          <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
              <tr>
                <th scope="col" class="px-4 py-3">Zadavatel</th>
                <th scope="col" class="px-4 py-3">Číslo PU</th>
                <th scope="col" class="px-4 py-3">Místo PU</th>
                <th scope="col" class="px-4 py-3">Účastník Jméno</th>
                <th scope="col" class="px-4 py-3">Účastník Příjmení</th>
                <th scope="col" class="px-4 py-3">Účastník Tel</th>
                <th scope="col" class="px-4 py-3">Kategorie</th>
                <th scope="col" class="px-4 py-3">Stav PU</th>
                <th scope="col" class="px-4 py-3">Akce</th>
              </tr>
            </thead>
            <tbody>
              {% for one_pou in pagination %}
                <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <td class="px-4 py-2">{{ one_pou.zadavatel.nazevPojistovny }}</td>
                  <td scope="row" class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">{{ one_pou.cisloPojistnaUdalost }}</td>
                  <td class="px-4 py-2">nejake misto</td>
                  <td class="px-4 py-2">ucastnik - jmeno</td>
                  <td class="px-4 py-2">ucastnik - prijmeni</td>
                  <td class="px-4 py-2">ucastnik - tel</td>
                  <td class="px-4 py-2">{{ one_pou.kategorie }}</td>
                  <td class="px-4 py-2">{{ one_pou.getStavLikvidaceLabel() }}</td>

                  <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                      
                        <a href="{{ path('app_pojistna_udalost_change_archive', {'id': one_pou.id}) }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                            Vyjmout z Archivu
                        </a>
                      
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        {# navigation below table #}
        <nav class="flex flex-col items-start justify-between p-4 space-y-3 md:flex-row md:items-center md:space-y-0" aria-label="Table navigation">
          <div class="flex items-center space-x-3">
            <label for="rows" class="text-xs font-normal text-gray-500 dark:text-gray-400">Rows per page</label><select id="rows" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block py-1.5 pl-3.5 pr-6 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
              <option value="25">25</option>
              <option selected="" value="50">50</option>
              <option value="100">100</option>
            </select>
            <div class="text-xs font-normal text-gray-500 dark:text-gray-400">
              <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getPaginationData.firstItemNumber }} - {{ pagination.getPaginationData.lastItemNumber }}</span>
              of
              <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getTotalItemCount }}</span>
            </div>
          </div>
          {# display navigation #}
          <div class="navigation">{{ knp_pagination_render(pagination) }}</div>
        </nav>
      </div>
    </div>
  </section>
{% endblock %}
