{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block title %}Seznam souborů
{% endblock %}

{% block canvas_content %}
	<div class="container mx-auto px-4 py-8 min-h-screen h-full">
		<div class="bg-white shadow-md rounded-lg overflow-hidden">
			<div class="p-4 bg-gray-50 border-b">
				<h1 class="text-2xl font-bold text-gray-800">Seznam souborů ve složce:
					{{ slozka.nazev }}</h1>
				<h3>
					Pojistná událost č.
					{{ slozka.pojistnaUdalost.cisloPojistnaUdalost }}</h3>
			</div>

			{% if soubory|length > 0 %}
				<div class="overflow-x-auto" data-controller="soubor--deletemodal">
					<table class="w-full text-sm text-left text-gray-500">
						<thead class="text-xs text-gray-700 uppercase bg-gray-50">
							<tr>
								<th scope="col" class="px-6 py-3">Název souboru</th>
								<th scope="col" class="px-6 py-3">Vyt<PERSON>řeno</th>
								<th scope="col" class="px-6 py-3">Akce</th>
							</tr>
						</thead>
						<tbody>
							{% for soubor in soubory %}
								<tr class="bg-white border-b hover:bg-gray-50">
									<td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
										<div class="flex gap-x-2 items-center">
											{% if soubor.image %}
												<img src="{{ path('app_soubor_read',{id:soubor.id}) }}" alt="{{ soubor.originalFilename }}" class="h-12 max-w-24 object-cover rounded-sm">
											{% endif %}
											<a href="{{ path('app_soubor_read',{id:soubor.id}) }}" class="font-medium text-blue-600 hover:underline" target="_blank">
												{{ soubor.originalFilename }}
											</a>
										</div>
									</td>
									<td class="px-6 py-4">
										{% if soubor.createdAt %}
											{{ soubor.createdAt|date('d.m.Y H:i') }}
										{% endif %}
									</td>
									<td class="px-6 py-4">
										<a href="#" class="bg-red-100 hover:bg-red-200 text-red-800 text-xs font-semibold me-2 px-2.5 py-0.5 rounded-sm dark:bg-gray-700 dark:text-red-400 border border-red-400 inline-flex items-center justify-center hover:underline" data-action="click->soubor--deletemodal#open" data-delete-url="{{ path('app_soubor_delete', { id: soubor.id }) }}" data-filename="{{ soubor.originalFilename }}">Smazat</a>
									</td>
								</tr>
							{% endfor %}
						</tbody>
					</table>
					{{ include('soubor/modal/_delete_soubor_modal.html.twig') }}
				</div>
			{% else %}
				<div class="p-6 text-center">
					<p class="text-gray-500">Žádné soubory nebyly nalezeny v této složce.</p>
				</div>
			{% endif %}

			<div class="p-4 bg-gray-50 border-t flex justify-between">
				<a href=" {{ path('app_pojistna_udalost_read',{ id: slozka.pojistnaUdalost.id }) }}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700">
					<svg class="w-3.5 h-3.5 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 14 10">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5H1m0 0 4 4M1 5l4-4"/>
					</svg>
					Zpět
				</a>
			</div>
		</div>
	</div>
{% endblock %}
