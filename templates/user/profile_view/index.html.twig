{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block title %}
  InIn - M<PERSON><PERSON> profil
{% endblock %}

{% block canvas_content %}
  <section class="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5 h-screen">
    <div class="py-8 px-4 mx-auto max-w-2xl lg:py-16 bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
        <h4 class="text-xl text-center text-gray-900 font-bold mb-4"><PERSON><PERSON><PERSON> profil</h4>

        <div class="flex items-center mb-4 sm:mb-5">
          <span class="mr-4">
            <svg class="w-8 h-8 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M12 20a7.966 7.966 0 0 1-5.002-1.756l.002.001v-.683c0-1.794 1.492-3.25 3.333-3.25h3.334c1.84 0 3.333 1.456 3.333 3.25v.683A7.966 7.966 0 0 1 12 20ZM2 12C2 6.477 6.477 2 12 2s10 4.477 10 10c0 5.5-4.44 9.963-9.932 10h-.138C6.438 21.962 2 17.5 2 12Zm10-5c-1.84 0-3.333 1.455-3.333 3.25S10.159 13.5 12 13.5c1.84 0 3.333-1.455 3.333-3.25S13.841 7 12 7Z" clip-rule="evenodd" />
            </svg>
          </span>
          <div>
            <h4 id="drawer-label" class="mb-1 text-xl font-bold leading-none text-gray-900 dark:text-white">{{ currentUser.name }}&nbsp;{{ currentUser.surname }}</h4>
            <p class="text-gray-500 dark:text-gray-400">{{ currentUser.email }}</p>
          </div>
        </div>
        <div class="py-3 border-b">
          <span class="font-bold mr-2">Role:</span>
          {% if currentUser.isAdministrator() %}
            {{ include('components/user/list/_administrator_role.html.twig', { user_role: currentUser.getRoleString() }) }}
          {% endif %}

          {% if currentUser.isLikvidator() %}
            {{ include('components/user/list/_likvidator_role.html.twig', { user_role: currentUser.getRoleString() }) }}
          {% endif %}
        </div>
        <div class="py-3 border-b">         
          <span class="mr-2 mb-2 font-semibold leading-none text-gray-900 dark:text-white">Jméno:</span>
          <span class="font-light text-gray-500 dark:text-gray-400">{{ currentUser.name }}&nbsp{{ currentUser.surname }}</span>
        </div>
        <div class="py-3 border-b">
          <span class="mr-2 mb-2 font-semibold leading-none text-gray-900 dark:text-white">Email:</span>
          <span class="font-light text-gray-500 dark:text-gray-400">{{ currentUser.email }}</span>
        </div>
        <div class="py-3 border-b">
          <span class="mr-2 mb-2 font-semibold leading-none text-gray-900 dark:text-white">Telefon:</span>
          <span class="font-light text-gray-500 dark:text-gray-400">{{ currentUser.telephone }}</span>
        </div>
        <div class="py-3 border-b">
          <span class="mr-2 mb-2 font-semibold leading-none text-gray-900 dark:text-white">Poslední přihlášení:</span>
          <span class="mb-4 font-light text-gray-500 dark:text-gray-400 sm:mb-5">{{ currentUser.getLastLoginString() }}</span>
        </div>
      
        <div class="w-1/2">
            <a href="{{ path('user_profile_edit') }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                <svg class="w-5" fill="currentColor" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 494.936 494.936" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M389.844,182.85c-6.743,0-12.21,5.467-12.21,12.21v222.968c0,23.562-19.174,42.735-42.736,42.735H67.157 c-23.562,0-42.736-19.174-42.736-42.735V150.285c0-23.562,19.174-42.735,42.736-42.735h267.741c6.743,0,12.21-5.467,12.21-12.21 s-5.467-12.21-12.21-12.21H67.157C30.126,83.13,0,113.255,0,150.285v267.743c0,37.029,30.126,67.155,67.157,67.155h267.741 c37.03,0,67.156-30.126,67.156-67.155V195.061C402.054,188.318,396.587,182.85,389.844,182.85z"></path> <path d="M483.876,20.791c-14.72-14.72-38.669-14.714-53.377,0L221.352,229.944c-0.28,0.28-3.434,3.559-4.251,5.396l-28.963,65.069 c-2.057,4.619-1.056,10.027,2.521,13.6c2.337,2.336,5.461,3.576,8.639,3.576c1.675,0,3.362-0.346,4.96-1.057l65.07-28.963 c1.83-0.815,5.114-3.97,5.396-4.25L483.876,74.169c7.131-7.131,11.06-16.61,11.06-26.692 C494.936,37.396,491.007,27.915,483.876,20.791z M466.61,56.897L257.457,266.05c-0.035,0.036-0.055,0.078-0.089,0.107 l-33.989,15.131L238.51,247.3c0.03-0.036,0.071-0.055,0.107-0.09L447.765,38.058c5.038-5.039,13.819-5.033,18.846,0.005 c2.518,2.51,3.905,5.855,3.905,9.414C470.516,51.036,469.127,54.38,466.61,56.897z"></path> </g> </g> </g></svg>  
                <path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" />
              </svg> Upravit profil uživatele
            </a>
        </div>
      
    </div>
  </section>
{% endblock %}
