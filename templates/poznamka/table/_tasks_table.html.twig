{#
  Parametry:
  - poznamky: kole<PERSON><PERSON> poznámek k zobrazení
  - title: nad<PERSON> se<PERSON>ce (volitelný)
  - title_icon: HTML kód i<PERSON> pro nadpis (volitelný)
  - row_classes: CSS třídy pro řádky tabulky
  - container_classes: dodatečné CSS třídy pro obalu<PERSON><PERSON><PERSON><PERSON> kontejner
  - empty_message: z<PERSON><PERSON><PERSON><PERSON>, kter<PERSON> se zobrazí, pokud nejsou žádné poznámky (volitelný)
#}

<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-5 border border-gray-200 dark:border-gray-700 flex flex-col flex-1 {{ container_classes|default('') }}">
	{% if poznamky|length > 0 %}
		{% if title is defined and title %}
			<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
				{% if title_icon is defined and title_icon %}
					{{ title_icon|raw }}
				{% endif %}
				{{ title }}
			</h3>
		{% endif %}
		<div class="relative overflow-x-auto">
			<table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
				<thead>
					<tr class="bg-gray-50 dark:bg-gray-700">
						<th scope="col" class="px-6 py-3 font-medium">Termín vyřízení</th>
						<th scope="col" class="px-6 py-3 font-medium">Datum zadání</th>
						<th scope="col" class="px-6 py-3 font-medium">Úkol</th>
						<th scope="col" class="px-6 py-3 font-medium"></th>
					</tr>
				</thead>
				<tbody>
					{% for poznamka in poznamky %}
						<tr class="{{ row_classes }}" data-controller="poznamka--toggle-content-quill">
							<td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
								{% if poznamka.termin %}
									{{ poznamka.termin|date('d.m.Y') }}
								{% else %}
									-
								{% endif %}
							</td>
							<td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
								<a href="{{ path('app_poznamka_detail', { id: poznamka.id, taskslist: 1 }) }}" class="hover:underline">
									{{ poznamka.casVytvoreno|date('d.m.Y') }}
								</a>
							</td>
							<td class="px-6 py-4">
								<div class="flex items-center flex-wrap gap-2">
									<a href="{{ path('app_poznamka_detail', { id: poznamka.id, taskslist: 1 }) }}" class="hover:underline">
										{{ poznamka.headline|default('bez nadpisu') }}
									</a>
								</div>
								<div data-poznamka--toggle-content-quill-target="content" class="hidden poznamka-content mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md text-sm dark:bg-gray-700 dark:border-gray-600">
									<div data-poznamka--toggle-content-quill-target="quillContainer" data-id="{{ poznamka.id }}" data-content="{{ poznamka.obsah|escape('html_attr') }}" class="prose max-w-none dark:prose-invert"></div>
								</div>
							</td>
							<td class="px-6 py-4">
								<div class="flex flex-row justify-end">
									{% if is_granted('POZNAMKA_RESOLVE', poznamka) %}
										{% if poznamka.isTask() and not poznamka.vyreseno %}
											<button data-poznamka--resolve-task-target="resolveButton" data-action="poznamka--resolve-task#openModal" data-poznamka-id="{{ poznamka.id }}" data-poznamka-token="{{ csrf_token('resolve' ~ poznamka.id) }}" data-poznamka-resolveurl="{{ path('app_poznamka_resolve', { id: poznamka.id }) }}">
												<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24" stroke="currentColor">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
												</svg>
											</button>
										{% endif %}
									{% endif %}

									<button type="button" data-action="poznamka--toggle-content-quill#toggle" class="text-gray-500 hover:text-blue-500">
										{% include 'components/icons/eye-dark.svg.twig' %}
									</button>


									{% if ( is_granted('POZNAMKA_DATE_EDIT', poznamka) ) and not pou.uzavreno %}
										<a href="{{ path('app_poznamka_edit', { id: poznamka.id, returnRoute: 'app_poznamka_task_list' }) }}">
											{% include 'components/icons/pen.svg.twig' %}
										</a>
									{% endif %}

								</div>
							</td>
						</tr>
					{% endfor %}
				</tbody>
			</table>
		</div>
	{% else %}
		{% if empty_message is defined %}
			<div class="text-center py-4 text-gray-500 dark:text-gray-400">
				{{ empty_message }}
			</div>
		{% endif %}
	{% endif %}
</div>
