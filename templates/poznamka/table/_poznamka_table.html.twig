{#
  Parametry:
  - poznamky: kole<PERSON><PERSON> poznámek k zobrazení
  - title: nad<PERSON> sekce (volitelný)
  - title_icon: HTML kód i<PERSON> pro nadpis (volitelný)
  - row_classes: CSS třídy pro řádky tabulky
  - container_classes: dodatečné CSS třídy pro obalu<PERSON><PERSON><PERSON><PERSON> kontejner
  - empty_message: z<PERSON>r<PERSON><PERSON>, která se zobrazí, pokud nejsou ž<PERSON>dn<PERSON> poznámky (volitelný)
#}

<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-5 border border-gray-200 dark:border-gray-700 flex flex-col flex-1 {{ container_classes|default('') }}">
	{% if poznamky|length > 0 %}
		{% if title is defined and title %}
			<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
				{% if title_icon is defined and title_icon %}
					{{ title_icon|raw }}
				{% endif %}
				{{ title }}
			</h3>
		{% endif %}
		<div class="relative overflow-x-auto">
			<table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
				<tbody>
					{% for poznamka in poznamky %}
						<tr class="{{ row_classes }}" data-controller="poznamka--toggle-content-quill">
							<td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
								<a href="{{ path('app_poznamka_detail', { id: poznamka.id }) }}" class="hover:underline">
									{{ poznamka.casVytvoreno|date('d.m.Y') }}
									-
									{{ poznamka.autor.getFullName() }}
								</a>
							</td>
							<td class="px-6 py-4">
								<div class="flex flex-row justify-end">
									{% if poznamka.isTask() %}
										{% include 'components/icons/clipboard-list.svg.twig' %}
										{% if poznamka.vyreseno %}
											<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
												<svg class="w-3 h-3 inline-block mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24" stroke="currentColor">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
												</svg>
											</span>
										{% endif %}
									{% endif %}
								</div>
							</td>
							<td class="px-6 py-4">
								<div class="flex items-center flex-wrap gap-2">
									{% if poznamka.pinned %}
										<span class="bg-amber-100 text-amber-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-amber-900 dark:text-amber-300">
											<svg class="w-3 h-3 inline-block mr-1" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewbox="0 0 24 24">
												<path d="M20.235 14.61c-.375-1.745-2.342-3.506-4.01-4.125l-.544-4.948 1.495-2.242a.763.763 0 00.037-.787.762.762 0 00-.675-.403h-9.14a.765.765 0 00-.638 1.191l1.498 2.247-.484 4.943c-1.668.62-3.633 2.38-4.004 4.116a.726.726 0 00.132.594.73.73 0 00.535.294c1.925.306 3.82-.347 5.006-1.558.39-.396.688-.838.892-1.309l.894.068a2.79 2.79 0 00.973 1.243c1.186 1.21 3.08 1.864 5.004 1.558a.728.728 0 00.535-.294.723.723 0 00.134-.59z"/>
											</svg>
											Připnuto
										</span>
									{% endif %}
									<a href="{{ path('app_poznamka_detail', { id: poznamka.id }) }}" class="hover:underline">
										{{ poznamka.headline|default('bez nadpisu') }}
									</a>
								</div>
								<div data-poznamka--toggle-content-quill-target="content" class="hidden poznamka-content mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md text-sm dark:bg-gray-700 dark:border-gray-600">
									<div data-poznamka--toggle-content-quill-target="quillContainer" data-id="{{ poznamka.id }}" data-content="{{ poznamka.obsah|escape('html_attr') }}" class="prose max-w-none dark:prose-invert"></div>
								</div>
							</td>
							<td class="px-6 py-4">
								<div class="flex flex-row justify-end">
									{% if is_granted('POZNAMKA_RESOLVE', poznamka) %}
										{% if poznamka.isTask() and not poznamka.vyreseno %}
											<button data-poznamka--resolve-task-target="resolveButton" data-action="poznamka--resolve-task#openModal" data-poznamka-id="{{ poznamka.id }}" data-poznamka-token="{{ csrf_token('resolve' ~ poznamka.id) }}" data-poznamka-resolveurl="{{ path('app_poznamka_resolve', { id: poznamka.id }) }}">
												<svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24" stroke="currentColor">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
												</svg>
											</button>
										{% endif %}
									{% endif %}

									<button type="button" data-action="poznamka--toggle-content-quill#toggle" class="text-gray-500 hover:text-blue-500">
										{% include 'components/icons/eye-dark.svg.twig' %}
									</button>

									{% if ( is_granted('POZNAMKA_DATE_EDIT', poznamka) and not pou.uzavreno ) %}
										<a href="{{ path('app_poznamka_edit', { id: poznamka.id, returnRoute: 'app_poznamka_list' }) }}">
											{% include 'components/icons/pen.svg.twig' %}
										</a>
									{% endif %}

								</div>
							</td>
						</tr>
					{% endfor %}
				</tbody>
			</table>
		</div>
	{% else %}
		{% if empty_message is defined %}
			<div class="text-center py-4 text-gray-500 dark:text-gray-400">
				{{ empty_message }}
			</div>
		{% endif %}
	{% endif %}
</div>
