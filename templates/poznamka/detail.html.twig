{# templates/poznamka/detail.html.twig #}

{% extends 'page_templates/single_pu_dashboard.html.twig' %}

{% block title %}
	InIn - Detail <PERSON>
{% endblock %}

{% block canvas_content %}

	{% if from_tasklist is not defined %}
		{% set from_tasklist = 0 %}
	{% endif %}
	{% set route = 'app_poznamka_list' %}
	{% set route_string = 'Zpět na poznámky' %}
	{% if from_tasklist == 1 %}
		{% set route = 'app_poznamka_task_list' %}
		{% set route_string = 'Zp<PERSON>t na úkoly' %}
	{% endif %}
	<section class="bg-white dark:bg-gray-900 min-h-screen h-full">
		<div
			id="poznamka_detail" class="py-8 px-4 mx-auto max-w-3xl lg:py-16">
			<!-- Back button -->
			<div class="mb-6">
				<a href="{{ path(route, { id: pou.id }) }}" class="inline-flex items-center text-gray-700 hover:text-primary-700 dark:text-gray-300 dark:hover:text-white">
					<svg class="w-5 h-5 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12l4-4m-4 4 4 4"/>
					</svg>

					{{ route_string }}
				</a>
			</div>

			<!-- Main card -->
			<div
				class="bg-white border border-gray-200 rounded-lg shadow-md dark:bg-gray-800 dark:border-gray-700 overflow-hidden">
				<!-- Card header with title and actions -->
				<div class="relative p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
					<div class="flex justify-between items-center">
						<h2 class="text-2xl font-bold text-gray-900 dark:text-white">
							{% if poznamka.task %}
								<span class="mr-2">
									<svg class="w-6 h-6 inline-block text-primary-700 dark:text-primary-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 12H3m9-9v18m9-9h-8"/>
									</svg>
								</span>
								Úkol
							{% else %}
								<span class="mr-2">
									<svg class="w-6 h-6 inline-block text-primary-700 dark:text-primary-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7.05 11.293a1 1 0 0 0-1.414 0L3.343 13.586a1 1 0 0 0 0 1.414l2.829 2.829a1 1 0 0 0 1.414 0l2.829-2.829a1 1 0 0 0 0-1.414l-2.829-2.829a1 1 0 0 0-1.414 0Z"/>
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.639 8.36a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414l-2.829 2.829a1 1 0 0 1-1.414 0l-2.829-2.829a1 1 0 0 1 0-1.414l2.829-2.829Z"/>
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.692 4.293a1 1 0 0 0-1.414 0L11.95 6.586a1 1 0 0 0 0 1.414l2.829 2.829a1 1 0 0 0 1.414 0l2.829-2.829a1 1 0 0 0 0-1.414l-2.829-2.829Z"/>
									</svg>
								</span>
								Poznámka
							{% endif %}
						</h2>

						<!-- Action buttons -->
						<div class="flex space-x-2">
							{% if poznamka.autor.id == app.user.id and not poznamka.pojistnaUdalost.uzavreno %}
								<div data-controller="poznamka--delete">
									<a href="{{ path('app_poznamka_edit', { id: poznamka.id }) }}" class="text-blue-700 hover:text-blue-800 dark:text-blue-500 dark:hover:text-blue-400 p-2 rounded-lg" title="Upravit">
										<svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
											<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"/>
										</svg>
									</a>
									<a href="#" data-action="poznamka--delete#deletePoznamkaModalDialog" data-poznamka-id="{{ poznamka.id }}" data-poznamka-token="{{ csrf_token('delete' ~ poznamka.id) }}" data-poznamka-deleteurl="{{ path('app_poznamka_delete', { id: poznamka.id }) }}" data-modal-target="deleteModalPoznamka" data-modal-toggle="deleteModalPoznamka" class="text-red-700 hover:text-red-800 dark:text-red-500 dark:hover:text-red-400 p-2 rounded-lg" title="Smazat">
										<svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
											<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"/>
										</svg>
									</a>
									{{ include('pojistna_udalost/modal/_poznamka_delete_modal.html.twig') }}
								</div>
							{% endif %}
						</div>
					</div>

					<!-- Status badges -->
					<div class="mt-3 flex flex-wrap gap-2">
						{% if poznamka.vyreseno %}
							<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300">
								<svg class="w-3 h-3 inline-block mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
									<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 11.917 9.724 16.5 19 7.5"/>
								</svg>
								Vyřešeno
							</span>
						{% endif %}

						{% if poznamka.pinned %}
							<span class="bg-amber-100 text-amber-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-amber-900 dark:text-amber-300">
								<svg class="w-3 h-3 inline-block mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
									<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m15 11.25-3-1.5v-6m0 0L9 2.25l6 1.5-3 1.5Z"/>
									<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11.25v10.5m-6-10.5 5.25-3 6.75 3"/>
								</svg>
								Připnuto
							</span>
						{% endif %}

						{% if poznamka.notifyZadavatel %}
							<span class="bg-teal-100 text-teal-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-teal-900 dark:text-teal-300">
								<svg class="w-3 h-3 inline-block mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
									<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7.5 14.25v1.5m9-1.5v1.5M8.4 21h7.2c1.68 0 2.52 0 3.162-.327a3 3 0 0 0 1.311-1.311C20.4 18.72 20.4 17.88 20.4 16.2v-1.95c0-2.408 0-3.612-.436-4.65a5.25 5.25 0 0 0-2.564-2.564C16.362 6.6 15.158 6.6 12.75 6.6h-1.5c-2.408 0-3.612 0-4.65.436a5.25 5.25 0 0 0-2.564 2.564C3.6 10.638 3.6 11.842 3.6 14.25v1.95c0 1.68 0 2.52.327 3.162a3 3 0 0 0 1.311 1.311C5.88 21 6.72 21 8.4 21Z"/>
									<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.6v-3M8.4 3.6h7.2"/>
								</svg>
								Notifikace zadavatele
							</span>
						{% endif %}

						{% if poznamka.vytvorenoSystemem %}
							<span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-purple-900 dark:text-purple-300">Systémové</span>
						{% endif %}

						{% set isNotResolvedtask = (poznamka.task) and (not poznamka.vyreseno)%}

						{% if isNotResolvedtask %}
							{% set diffDays = poznamka.terminDiffDays(poznamka.termin) %}
							{% if diffDays > 0 %}
								<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
									<svg class="w-3 h-3 inline-block mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
									</svg>
									Zbývá
									{{ diffDays }}
									{{ diffDays == 1 ? 'den' : 'dní' }}
								</span>
							{% elseif diffDays < 0 %}
								<span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-red-900 dark:text-red-300">
									<svg class="w-3 h-3 inline-block mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
									</svg>
									Po termínu
									{{ diffDays|abs }}
									{{ diffDays == -1 ? 'den' : 'dní' }}
								</span>
							{% else %}
								<span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900 dark:text-yellow-300">
									<svg class="w-3 h-3 inline-block mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
									</svg>
									Termín dnes
								</span>
							{% endif %}
						{% endif %}

						{% if poznamka.hlidatSplneni %}
							<span class="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-indigo-900 dark:text-indigo-300">
								<svg class="w-3 h-3 inline-block mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
									<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
								</svg>
								Hlídání splnění
							</span>
						{% endif %}
					</div>
				</div>

				<!-- Card content -->
				<div
					class="p-6">
					<!-- Headline section -->
					{% if poznamka.headline %}
						<div class="mb-6">
							<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Nadpis</h3>
							<div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
								<p class="text-gray-900 dark:text-white text-lg font-medium">{{ poznamka.headline }}</p>
							</div>
						</div>
					{% endif %}

					<!-- Content section -->
					<div class="mb-8">
						<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Obsah</h3>
						<div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
							{% if poznamka.obsah %}
								<div data-controller="quill--view" class="prose max-w-none dark:prose-invert">
									<div data-quill--view-target="container" data-content={{ poznamka.obsah|escape('html_attr') }}></div>
								</div>
								<div class="hidden">{{ poznamka.obsah }}</div>
							{% else %}
								<p class="text-gray-500 dark:text-gray-400 italic">Žádný obsah</p>
							{% endif %}
						</div>
					</div>

					<!-- Details section -->
					<div
						class="grid grid-cols-1 md:grid-cols-2 gap-6">
						<!-- Left column -->
						<div>
							<dl>
								<h3 class="text-lg font-semibold text-gray-900 dark:text-white">Autor</h3>
								<div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">

									<dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ poznamka.autor.name }}
										{{ poznamka.autor.surname }}</dd>
								</div>
							</dl>
						</div>

						<!-- Right column -->
						{% set datum_vytvoreni_text = 'Datum vytvoření' %}
						{% if poznamka.task() %}
							{% set datum_vytvoreni_text = 'Datum zadání' %}
						{% endif %}
						<div>
							<dl>
								<h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ datum_vytvoreni_text }}</h3>
								<div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
									<dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ poznamka.casVytvoreno ? poznamka.casVytvoreno|date('d.m.Y H:i') : '' }}</dd>
								</div>
							</dl>
						</div>
					</div>

					<div
						class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-2">
						<!-- Left column -->
						<div>
							<dl>
								{% if poznamka.resitel %}
									<h3 class="text-lg font-semibold text-gray-900 dark:text-white">Řešitel</h3>
									<div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
										<dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ poznamka.resitel.name }}
											{{ poznamka.resitel.surname }}</dd>
									</div>
								{% endif %}
							</dl>
						</div>

						<!-- Right column -->
						<div>
							<dl>
								{% if poznamka.termin %}
									<h3 class="text-lg font-semibold text-gray-900 dark:text-white">Termín vyřízení</h3>
									<div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
										<dd class="mt-1 text-sm text-gray-900 dark:text-white">
											{{ poznamka.termin|date('d.m.Y') }}
											{% if isNotResolvedtask %}
												{% set diffDays = poznamka.terminDiffDays(poznamka.termin) %}
												{% if diffDays > 0 %}
													<span class="text-blue-600 dark:text-blue-400">(zbývá
														{{ diffDays }}
														{{ diffDays == 1 ? 'den' : 'dní' }})</span>
												{% elseif diffDays < 0 %}
													<span class="text-red-600 dark:text-red-400">(po termínu
														{{ diffDays|abs }}
														{{ diffDays == -1 ? 'den' : 'dní' }})</span>
												{% else %}
													<span class="text-yellow-600 dark:text-yellow-400">(termín dnes)</span>
												{% endif %}
											{% endif %}
										</dd>
									</div>
								{% endif %}
							</dl>
						</div>
					</div>

					<div
						class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-2">
						<!-- Left column -->
						<div></div>
						<!-- Right column -->
						<div>
							<dl>
								{% if poznamka.casVyreseni %}
									<h3 class="text-lg font-semibold text-gray-900 dark:text-white">Vyřízeno</h3>
									<dl class="grid grid-cols-1 gap-4">
										<div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
											<dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ poznamka.casVyreseni|date('d.m.Y H:i') }}</dd>
										</div>

									</dl>
								{% endif %}
							</dl>

						</div>
					</div>


					<!-- Files section -->
					<div class="mt-8">
						<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Soubory ({{ poznamka.soubory|length|default(0) }})</h3>
						{% if poznamka.soubory|length > 0 %}
							<div class="overflow-x-auto bg-gray-50 dark:bg-gray-700 p-4 rounded-lg" data-controller="poznamka--deletesoubor">
								<table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
									<thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-600 dark:text-gray-300">
										<tr>
											<th scope="col" class="px-6 py-3">Název souboru</th>
											<th scope="col" class="px-6 py-3">Vytvořeno</th>
											<th scope="col" class="px-6 py-3">Akce</th>
										</tr>
									</thead>
									<tbody>
										{% for soubor in poznamka.soubory %}
											<tr class="bg-white dark:bg-gray-700 border-b dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600">
												<td class="px-6 py-4 font-medium text-gray-900 dark:text-white whitespace-nowrap">
													<div class="flex gap-x-2 items-center">
														{% if soubor.image %}
															<img src="{{ path('app_note_soubor_read',{id:soubor.id}) }}" alt="{{ soubor.originalFilename }}" class="h-12 max-w-24 object-cover rounded-sm">
														{% endif %}
														<a href="{{ path('app_note_soubor_read',{id:soubor.id}) }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline" target="_blank">
															{{ soubor.originalFilename }}
														</a>
													</div>
												</td>
												<td class="px-6 py-4">
													{% if soubor.createdAt %}
														{{ soubor.createdAt|date('d.m.Y H:i') }}
													{% endif %}
												</td>
												<td class="px-6 py-4">
													<div class="flex space-x-2">
														{% if not poznamka.pojistnaUdalost.uzavreno %}
															<a href="#" data-action="poznamka--deletesoubor#open" data-delete-url="{{ path('app_note_soubor_delete', { id: soubor.id }) }}" data-filename="{{ soubor.originalFilename }}" class="text-red-700 hover:text-red-800 dark:text-red-500 dark:hover:text-red-400 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600" title="Smazat">
																<svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24">
																	<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 7h14m-9 3v8m4-8v8M10 3h4a1 1 0 0 1 1 1v3H9V4a1 1 0 0 1 1-1ZM6 7h12v13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V7Z"/>
																</svg>
															</a>
														{% endif %}
													</div>
												</td>
											</tr>
										{% endfor %}
									</tbody>
								</table>
								{{ include('poznamka/modal/_delete_soubor_modal.html.twig') }}
							</div>
						{% else %}
							<div class="p-4 text-center bg-gray-50 dark:bg-gray-700 rounded-lg">
								<p class="text-gray-500 dark:text-gray-400">K této poznámce nejsou přiloženy žádné soubory.</p>
							</div>
						{% endif %}
					</div>

				</div>


				<!-- Card footer with actions -->
				<div class="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
					<div class="flex flex-wrap gap-3 justify-between items-center">
						<a href="{{ path (route, { id: pou.id }) }}" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
							{{ route_string}}
						</a>

						{% if is_granted('POZNAMKA_RESOLVE', poznamka) %}
							{% if poznamka.termin is not null and poznamka.casVyreseni is null and not poznamka.pojistnaUdalost.uzavreno %}
								<div data-controller="poznamka--resolve-task">
									<button type="button" data-action="poznamka--resolve-task#openModal" data-poznamka--resolve-task-target="resolveButton" data-poznamka-id="{{ poznamka.id }}" data-poznamka-token="{{ csrf_token('resolve' ~ poznamka.id) }}" data-poznamka-resolveurl="{{ path('app_poznamka_resolve', { id: poznamka.id }) }}" class="text-white flex items-center justify-center px-5 py-2.5 text-sm font-medium rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
										<svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewbox="0 0 24 24" stroke="currentColor">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
										</svg>
										Označit jako vyřešené
									</button>

									<!-- Vložení modálního okna -->
									{{ include('poznamka/modal/_resolve_task_modal.html.twig') }}
								</div>
							{% endif %}
						{% endif %}
					</div>
				</div>

			</div>
		</div>
	</div>
</section>{% endblock %}
