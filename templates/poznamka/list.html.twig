{% extends 'page_templates/single_pu_dashboard.html.twig' %}
{% block title %}
	InIn - <PERSON><PERSON><PERSON><PERSON> poznámek a ú<PERSON>ů
{% endblock %}

{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full">
		<div class="py-8 px-4 mx-auto max-w-6xl lg:py-16 min-h-screen">
			{{ include('pojistna_udalost/components/_pu_header_number.html.twig',{pou:pou}) }}

			<div class="flex flex-row justify-between">
				<div>
					{% if not pou.uzavreno %}
						<div class="flex flex-col items-stretch justify-between py-4 space-y-3 md:flex-row md:items-center md:space-y-0">
							<a href="{{ path('app_poznamka_new', { id: pou.id }) }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
								<svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
									<path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
								</svg>Přidat novou poznámku
							</a>
						</div>
					{% endif %}

				</div>
				<div class="flex flex-row py-4" data-controller="poznamka--toggle-content-quill">
					<div class="pr-2">
						<button type="button" data-action="poznamka--toggle-content-quill#toggleAll" class="text-gray-500 hover:text-blue-500">
							{% include 'components/icons/eye-dark.svg.twig' %}
						</button>
					</div>
					<div class="min-h-7 mb-4">
						<div class="float-left">
							<a href="{{ path('app_poznamka_list', { id: pou.id, sortOrder: 'desc' }) }}">
								{% include 'components/icons/arrow-up.svg.twig' %}
							</a>
						</div>
						<div class="float-left">
							<a href="{{ path('app_poznamka_list', { id: pou.id, sortOrder: 'asc' }) }}">
								{% include 'components/icons/arrow-down.svg.twig' %}
							</a>
						</div>
					</div>
				</div>
			</div>

			<div data-controller="poznamka--resolve-task">

			<!-- Připnuté poznámky -->
			{% if pinnedPoznamky|length > 0 %}
				{% set pin_icon %}
				<svg class="w-5 h-5 mr-2 text-amber-500" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewbox="0 0 24 24">
					<path d="M20.235 14.61c-.375-1.745-2.342-3.506-4.01-4.125l-.544-4.948 1.495-2.242a.763.763 0 00.037-.787.762.762 0 00-.675-.403h-9.14a.765.765 0 00-.638 1.191l1.498 2.247-.484 4.943c-1.668.62-3.633 2.38-4.004 4.116a.726.726 0 00.132.594.73.73 0 00.535.294c1.925.306 3.82-.347 5.006-1.558.39-.396.688-.838.892-1.309l.894.068a2.79 2.79 0 00.973 1.243c1.186 1.21 3.08 1.864 5.004 1.558a.728.728 0 00.535-.294.723.723 0 00.134-.59z"/>
				</svg>
				{% endset %}

				{% include 'poznamka/table/_poznamka_table.html.twig' with {
					'poznamky': pinnedPoznamky,
					'title': 'Připnuté poznámky',
					'title_icon': pin_icon,
					'row_classes': 'bg-amber-50 dark:bg-amber-900/20 border-b border-amber-100 dark:border-amber-800 hover:bg-amber-100 dark:hover:bg-amber-900/30',
					'container_classes': 'mb-6'
				} %}

			{% endif %}

			<!-- Ostatní poznámky -->
			{% include 'poznamka/table/_poznamka_table.html.twig' with {
				'poznamky': unpinnedPoznamky,
				'title': pinnedPoznamky|length > 0 ? 'Ostatní poznámky' : 'Poznámky',
				'row_classes': 'bg-white dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700',
				'empty_message': 'Žádné poznámky k zobrazení'
			} %}

			<!-- Vložení modálního okna -->
			{{ include('poznamka/modal/_resolve_task_modal.html.twig') }}
			</div>

		</div>
	</section>
{% endblock %}
