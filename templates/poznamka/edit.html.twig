{% extends 'page_templates/single_pu_dashboard.html.twig' %}

{% block title %}
	InIn - Editace poznámky
{% endblock %}

{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full min-h-screen">
		<div id="poznamka_ukol" class="py-8 px-4 mx-auto max-w-2xl lg:py-16" data-controller="poznamka--edit">
			<h2 class="mb-2 text-xl font-bold text-gray-900 dark:text-white">Editace komentáře nebo úkolu</h2>
			<h4 class="mb-6 font-bold text-gray-900 dark:text-white">k PU č.
				{{ cislo_pu }}</h4>
			{{ form_start(form, {'attr': {'data-poznamka--edit-target': 'form'}}) }}

			<div data-controller="poznamka--toggle-task" class="flex-1">
				<div class="flex flex-wrap gap-4 mb-4">
					<div class="flex-1">
						{{ form_row(form.withTask) }}
					</div>
					<div class="flex-1">
						{{ form_row(form.notifyZadavatel, { label: 'Informovat pojišťovnu' }) }}
					</div>
					<div class="flex-1">
						{{ form_row(form.pinned, { label: 'Připnout' }) }}
					</div>
				</div>

				<div id="toggle_task" style="display: {{ form.withTask.vars.value ? 'block' : 'none' }}" data-poznamka--toggle-task-target="toggleDiv">
					<div class="flex flex-col md:flex-row gap-4 mb-4">
						<div class="flex-1">
							{{ form_row(form.termin, { label: 'Termín' }) }}
						</div>
						<div class="flex-1">
							{{ form_row(form.resitel, { label: 'Řešitel' }) }}
						</div>
					</div>
				</div>
			</div>

			{{ form_row(form.headline, { label: 'Nadpis' }) }}

			<div class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Obsah</div>
			<div id="editorquill" class="min-h-36"></div>
			<div class="hidden">
				{{ form_row(form.obsah, { label: 'Obsah', attr: { 'data-poznamka--edit-target': 'textareaObsah' } }) }}
			</div>

			{{ form_row(form.soubory, { label: 'Soubory' }) }}
			<div class="mt-1 text-sm text-gray-500 dark:text-gray-300" id="user_avatar_help">Můžete přiložit rovnou více souborů najednou</div>

			<div class="flex gap-2">
				<button type="submit" class="inline-flex items-center px-5 py-2.5 my-4 sm:mt-6 text-sm font-medium text-center text-white bg-primary-700 rounded-lg focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-900 hover:bg-primary-800">Uložit komentář/úkol pro PU č.
					{{ cislo_pu }}</button>

				<a href="{{ path('app_poznamka_list', { id: pou.id }) }}" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 my-4 sm:mt-6 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">Zpět na poznámky pro pojistnou událost</a>
			</div>

			{{ form_end(form) }}
		</div>
	</section>
{% endblock %}
