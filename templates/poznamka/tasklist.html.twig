{% extends 'page_templates/single_pu_dashboard.html.twig' %}
{% block title %}
	InIn - p<PERSON><PERSON><PERSON> ne<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
{% endblock %}

{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full">
		<div class="py-8 px-4 mx-auto max-w-6xl lg:py-16 min-h-screen">
			{{ include('pojistna_udalost/components/_pu_header_number.html.twig',{pou:pou}) }}

			<div class="flex flex-row justify-between">
				<div>

				</div>
				<div class="flex flex-row py-4" data-controller="poznamka--toggle-content-quill">
					<div class="pr-2">
						<button type="button" data-action="poznamka--toggle-content-quill#toggleAll" class="text-gray-500 hover:text-blue-500">
							{% include 'components/icons/eye-dark.svg.twig' %}
						</button>
					</div>
					<div class="min-h-7 mb-4">
						<div class="float-left">
							<a href="{{ path('app_poznamka_task_list', { id: pou.id, sortOrder: 'desc' }) }}">
								{% include 'components/icons/arrow-up.svg.twig' %}
							</a>
						</div>
						<div class="float-left">
							<a href="{{ path('app_poznamka_task_list', { id: pou.id, sortOrder: 'asc' }) }}">
								{% include 'components/icons/arrow-down.svg.twig' %}
							</a>
						</div>
					</div>
				</div>
			</div>

			<div data-controller="poznamka--resolve-task">
			<!-- Ostatní poznámky -->
			{% include 'poznamka/table/_tasks_table.html.twig' with {
				'poznamky': tasks,
				'title': 'Nevyřešené úkoly',
				'row_classes': 'bg-white dark:bg-gray-800 border-b dark:border-gray-700 border-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700',
				'empty_message': 'Žádné nevyřešené úkoly k zobrazení'
			} %}

			<!-- Vložení modálního okna -->
			{{ include('poznamka/modal/_resolve_task_modal.html.twig') }}
			</div>

		</div>
	</section>
{% endblock %}