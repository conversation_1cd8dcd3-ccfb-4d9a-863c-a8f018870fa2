{% extends 'base.html.twig' %}

{% block title %}
  Log in!
{% endblock %}

{% block body %}
  <section class="bg-gray-50 dark:bg-gray-900">
    <div class="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
      <a href="{{ path('app_dashboard') }}" class="flex flex-col items-center mb-6 text-2xl font-semibold text-gray-900 dark:text-white">
        <div>
          <img class="max-w-20 mr-2" src="{{ asset('images/inin-logo-minimized.jpg') }}" alt="logo" />
        </div>Insurance Inspection
      </a>
      <div class="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
        {% if error %}
          <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
            <span class="font-medium">Error:</span>
            {{ error.messageKey|trans(error.messageData, 'security') }}
          </div>
        {% endif %}

        {% if app.user %}
          <div>
            <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
              <span class="font-medium">Info:</span>
              Už jste přihlášený {{ app.user.userIdentifier }},
              <a href="{{ path('app_logout') }}">Logout</a>
            </div>
          </div>
          <div class="mb-3"></div>
        {% endif %}

        <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1 class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">Přihlaste se</h1>
          <form class="space-y-4 md:space-y-6" method="post">
            <div>
              <label for="inputEmail" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Váš email</label>
              <input type="email" name="email" id="inputEmail" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="<EMAIL>" required autofocus />
            </div>
            <div>
              <label for="inputPassword" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Heslo</label>
              <input type="password" name="password" id="inputPassword" placeholder="••••••••" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required="" />
            </div>
            <div class="flex items-center justify-between">
              <a href="{{ path('app_forgot_password_request') }}" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">Zapomněli jste heslo?</a>
            </div>
            <input type="hidden" name="_csrf_token" value="{{ csrf_token('logincode') }}" />
            <button type="submit" class="w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">Přihlásit se</button>
          </form>
        </div>
      </div>
    </div>
  </section>
{% endblock %}
