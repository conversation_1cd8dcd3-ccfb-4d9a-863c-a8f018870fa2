{# possible fix for different usage of variable fo rpojistna udalost in app #}
{% if (pou is not defined) and (pojistna_udalost is defined)  %}
	{% set pou = pojistna_udalost %}
{% endif %}
<aside class="fixed top-0 left-0 z-40 w-64 h-screen pt-14 transition-transform -translate-x-full bg-white border-r border-gray-200 md:translate-x-0 dark:bg-gray-800 dark:border-gray-700" aria-label="Sidenav" id="drawer-navigation">
	<div class="overflow-y-auto py-5 px-3 h-full bg-white dark:bg-gray-800">
		<ul class="space-y-2">
			<li>
				<a href="{{ path('app_dashboard') }}" class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
					{% include 'components/icons/chalkboard.svg.twig' %}
					<span class="ml-3">Nástěnka</span>
				</a>
			</li>

			<li>
				<a href="{{ path('app_zprava_list') }}" class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group">
					{% include 'components/icons/messages.svg.twig' %}
					<span class="flex-1 ml-3 whitespace-nowrap">Zprávy</span>
					<span class="inline-flex justify-center items-center w-5 h-5 text-xs font-semibold rounded-full text-primary-800 bg-primary-100 dark:bg-primary-200 dark:text-primary-800">{{ unread_count(app.user) }}</span>
				</a>
			</li>
		</ul>
		<ul class="pt-5 mt-5 space-y-2 border-t border-gray-200 dark:border-gray-700">
			<li>
				<a href="#" class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group">

					{% include 'components/icons/calendar.svg.twig' %}
					<span class="ml-3">Kalendář</span>
				</a>
			</li>
			<li>
				<a href="{{ path('app_pojistna_udalost_list') }}" class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group">
					{% include 'components/icons/file-lines.svg.twig' %}
					<span class="ml-3">Pojistné události</span>
				</a>
			</li>
			<li>
				<ul class="py-5 my-5 space-y-2 border border-gray-200 dark:border-gray-700">
					<li>
						<a href="{{ path('app_pojistna_udalost_read',{id:pou.id}) }}" class="flex rounded p-2 bg-gray-200 hover:underline">PU:
							{{ pou.cisloPojistnaUdalost }}</a>
					</li>
					{% set activepoznamky = '' %}
					{% if submenu_page|default('') == 'poznamky' %}
						{% set activepoznamky = 'bg-primary-100' %}
					{% endif %}
					<li>
						<a href="{{ path('app_poznamka_list',{id:pou.id}) }}" class="flex items-center p-2 text-base font-normal text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group {{ activepoznamky }}">
							<div class="min-w-[24px]">
								{% include 'components/icons/caret-right.svg.twig' %}
							</div>
							<span class="ml-3">Poznámky</span>
						</a>
					</li>
					{% set activeukoly = '' %}
					{% if submenu_page|default('') == 'ukoly' %}
						{% set activeukoly = 'bg-primary-100' %}
					{% endif %}
					<li>
						<a href="{{ path('app_poznamka_task_list',{id:pou.id}) }}" class="flex items-center p-2 text-base font-normal text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group {{ activeukoly }}">
							<div class="min-w-[24px]">
								{% include 'components/icons/caret-right.svg.twig' %}
							</div>
							<span class="ml-3">Úkoly</span>
						</a>
					</li>
					{% set active_info_pu = '' %}
					{% if submenu_page|default('') == 'info_pu' %}
						{% set active_info_pu = 'bg-primary-100' %}
					{% endif %}
					<li>
						<a href="{{ path('app_pojistna_udalost_additional_pu_info',{id:pou.id}) }}" class="flex items-center p-2 text-base font-normal text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group {{ active_info_pu }}">
							<div class="min-w-[24px]">
								{% include 'components/icons/caret-right.svg.twig' %}
							</div>
							<span class="ml-3">Informace o PU</span>
						</a>
					</li>
					{% set active_infops = '' %}
					{% if submenu_page|default('') == 'info_ps' %}
						{% set active_infops = 'bg-primary-100' %}
					{% endif %}
					<li>
						<a href="{{ path('app_pojistna_smlouva_info',{id:pou.id}) }}" class="flex items-center p-2 text-base font-normal text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group {{ active_infops }}">
							<div class="min-w-[24px]">
								{% include 'components/icons/caret-right.svg.twig' %}
							</div>
							<span class="ml-3">Informace o PS</span>
						</a>
					</li>
					<li>
						<a href="#" class="flex items-center p-2 text-base font-normal text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group">
							<div class="min-w-[24px]">
								{% include 'components/icons/caret-right.svg.twig' %}
							</div>
							<span class="ml-3">Fotodokumentace</span>
						</a>
					</li>
					{% set active_dokumenty = '' %}
					{% if submenu_page|default('') == 'dokumenty' %}
						{% set active_dokumenty = 'bg-primary-100' %}
					{% endif %}					
					<li>
						<a href="{{ path('app_dokumenty_index',{id:pou.id}) }}" class="flex items-center p-2 text-base font-normal text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group {{ active_dokumenty }}">
							<div class="min-w-[24px]">
								{% include 'components/icons/caret-right.svg.twig' %}
							</div>
							<span class="ml-3">Dokumenty</span>
						</a>
					</li>
					{% set active_vyzadane_dokumenty = '' %}
					{% if submenu_page|default('') == 'vyzadane_dokumenty' %}
						{% set active_vyzadane_dokumenty = 'bg-primary-100' %}
					{% endif %}
					<li>
						<a href="{{ path('app_vyzadany_dokument_list',{id:pou.id}) }}" class="flex items-center p-2 text-base font-normal text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group {{ active_vyzadane_dokumenty }}">
							<div class="min-w-[24px]">
								{% include 'components/icons/caret-right.svg.twig' %}
							</div>
							<span class="ml-3">Vyžádané dokumenty</span>
						</a>
					</li>
					{% set active_komunikace = '' %}
					{% if submenu_page|default('') == 'komunikace' %}
						{% set active_komunikace = 'bg-primary-100' %}
					{% endif %}
					<li>
						<a href="{{ path('app_komunikace_pu_list',{id:pou.id}) }}" class="flex items-center p-2 text-base font-normal text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group {{ active_komunikace }}">
							<div class="min-w-[24px]">
								{% include 'components/icons/caret-right.svg.twig' %}
							</div>

							<span class="ml-3">Komunikace s&nbsppojišťovnou</span>
						</a>
					</li>
					{% set active_ukonceni = '' %}
					{% if submenu_page|default('') == 'ukonceni' %}
						{% set active_ukonceni = 'bg-primary-100' %}
					{% endif %}
					<li>
						<a href="{{ path('app_pojistna_udalost_ukonceni',{id:pou.id}) }}" class="flex items-center p-2 text-base font-normal text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group {{ active_ukonceni }}">
							<div class="min-w-[24px]">
								{% include 'components/icons/caret-right.svg.twig' %}
							</div>
							<span class="ml-3">Ukončení PU</span>
						</a>
					</li>
				</ul>
			</li>

			{% if is_granted('ROLE_COM_ADMIN') %}
				<li>
					<a href="{{ path('zadavatel_list') }}" class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group">
						{% include 'components/icons/building.svg.twig' %}
						<span class="ml-3">Zadavatelé</span>
					</a>
				</li>
				<li>
					<a href="{{ path('app_user_list') }}" class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg transition duration-75 hover:bg-gray-100 dark:hover:bg-gray-700 dark:text-white group">
						{% include 'components/icons/user.svg.twig' %}
						<span class="ml-3">Uživatelé</span>
					</a>
				</li>
				<li>
					<button type="button" class="flex items-center p-2 w-full text-base font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700" aria-controls="dropdown-ciselniky" data-collapse-toggle="dropdown-ciselniky">
						{% include 'components/icons/file-lines-black.svg.twig' %}
						<span class="flex-1 ml-3 text-left whitespace-nowrap">Číselníky</span>
						{% include 'components/icons/chevron-down.svg.twig' %}
					</button>
					<ul id="dropdown-ciselniky" class="hidden py-2 space-y-2">
						<li>
							<a href="{{ path('app_ciselniky_pojistne_podminky_list') }}" class="flex items-center p-2 pl-11 w-full text-base font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700">Pojistné podmínky</a>
						</li>
						<li>
							<a href="{{ path('app_rizika_list')}}" class="flex items-center p-2 pl-11 w-full text-base font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700">Rizika</a>
						</li>
						<li>
							<a href="{{ path('app_user_role_list')}}" class="flex items-center p-2 pl-11 w-full text-base font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700">Uživatelské role</a>
						</li>
					</ul>
				</li>
			{% endif %}
			{% if is_granted('ROLE_COM_ADMIN') %}
				<li>
					<button type="button" class="flex items-center p-2 w-full text-base font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700" aria-controls="dropdown-archiv" data-collapse-toggle="dropdown-archiv">
						{% include 'components/icons/file-lines-black.svg.twig' %}
						<span class="flex-1 ml-3 text-left whitespace-nowrap">Archiv</span>
						{% include 'components/icons/chevron-down.svg.twig' %}
					</button>
					<ul id="dropdown-archiv" class="hidden py-2 space-y-2">
						<li>
							<a href="{{ path('app_pojistna_udalost_list_archiv') }}" class="flex items-center p-2 pl-11 w-full text-base font-medium text-gray-900 rounded-lg transition duration-75 group hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700">Pojistné události</a>
						</li>
					</ul>
				</li>
			{% endif %}
		</ul>
	</div>

</aside>
