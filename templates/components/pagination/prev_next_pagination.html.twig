{# tailwindcss Sliding pagination control implementation #}
{% if pageCount > 1 %}
    <div class="inline-block">

          <ul class="inline-flex items-stretch -space-x-px">
          {% if previous is defined %}
            <li>
              <a rel="prev" href="{{ path(route, query|merge({(pageParameterName): previous})) }}" class="flex text-sm w-20 items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">Předchozí</a>
            </li>
            {% else %}
            <li>
              <a rel="prev" href="#" class="flex text-sm w-20 items-center justify-center h-full py-1.5 px-3 ml-0 text-gray-200 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white pointer-events-none">Předchozí</a>
            </li>              
            {% endif %}
            {% if next is defined %}
            <li>
              <a rel="next" href="{{ path(route, query|merge({(pageParameterName): next})) }}" class="flex text-sm w-20 items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">Následující</a>
            </li>
            {% else %}
              <li>
              <a rel="next" href="#" class="flex text-sm w-20 items-center justify-center h-full py-1.5 px-3 leading-tight text-gray-200 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white pointer-events-none">Následující</a>
            </li>
            {% endif %}
          </ul>


    </div>
{% endif %}