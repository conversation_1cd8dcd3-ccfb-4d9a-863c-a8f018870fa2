{# Rename Modal #}
<div data-file-browser-target="renameModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Header -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Přejmenovat soubor
                </h3>
                <button data-action="click->file-browser#cancelRename" 
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Content -->
            <div class="mb-4">
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    Zadejte nový název pro soubor:
                </p>
                <p class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    <span data-file-browser-target="renameCurrentFileName">Aktuální název</span>
                </p>
                
                <div class="space-y-2">
                    <input type="text" 
                           data-file-browser-target="renameInput"
                           data-action="keydown->file-browser#handleRenameKeydown input->file-browser#validateRenameInput"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                           placeholder="Nový název souboru">
                    
                    <div data-file-browser-target="renameValidationMessage" class="hidden text-sm text-red-600 dark:text-red-400">
                        <!-- Validation messages will appear here -->
                    </div>
                    
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        Přípona souboru bude zachována automaticky. Můžete používat české znaky, mezery a závorky.
                    </p>
                </div>
            </div>
            
            <!-- Buttons -->
            <div class="flex justify-end space-x-3">
                <button data-action="click->file-browser#cancelRename"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500">
                    Zrušit
                </button>
                <button data-action="click->file-browser#confirmRename"
                        data-file-browser-target="renameConfirmButton"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    Přejmenovat
                </button>
            </div>
        </div>
    </div>
</div>
