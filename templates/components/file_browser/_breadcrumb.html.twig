<div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
    <div data-file-browser-target="breadcrumb">
        <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            {% for item in breadcrumb %}
                <li class="inline-flex items-center">
                    {% if not loop.first %}
                        <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                    {% endif %}
                    
                    {% if item.is_current %}
                        <span class="ms-1 text-sm font-semibold text-blue-600 dark:text-blue-400 md:ms-2 inline-flex items-center">
                            <svg class="w-4 h-4 text-blue-500 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M3 8a2 2 0 0 1 2-2h3.93a2 2 0 0 0 1.66-.9l.82-1.2A2 2 0 0 1 13.07 3H19a2 2 0 0 1 2 2v13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8Z"/>
                            </svg>
                            {{ item.nazev }}
                        </span>
                    {% else %}
                        <a href="#" 
                           data-action="click->file-browser#navigateToFolderFromEvent"
                           data-slozka-id="{{ item.slozka.id }}"
                           class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white transition-colors">
                            <svg class="w-4 h-4 text-yellow-500 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M3 8a2 2 0 0 1 2-2h3.93a2 2 0 0 0 1.66-.9l.82-1.2A2 2 0 0 1 13.07 3H19a2 2 0 0 1 2 2v13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8Z"/>
                            </svg>
                            {{ item.nazev }}
                        </a>
                    {% endif %}
                </li>
            {% endfor %}
        </ol>
        </nav>
    </div>
    
    <!-- Počet položek -->
    <div class="text-sm text-gray-500 dark:text-gray-400">
        {% set total_items = (soubory|length) + (podslozky|length) %}
        {% if total_items == 0 %}
            Prázdná složka
        {% elseif total_items == 1 %}
            1 položka
        {% elseif total_items < 5 %}
            {{ total_items }} položky
        {% else %}
            {{ total_items }} položek
        {% endif %}
    </div>
</div>
