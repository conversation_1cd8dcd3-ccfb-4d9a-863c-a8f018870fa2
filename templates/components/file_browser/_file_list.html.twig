{% if (soubory|length) > 0 or (podslozky|length) > 0 %}
    <div class="overflow-x-auto">
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-4 py-3 w-8">
                        <!-- Ikona -->
                    </th>
                    <th scope="col" class="px-4 py-3">
                        Název
                    </th>
                    <th scope="col" class="px-4 py-3 w-32">
                        <div class="flex items-center space-x-1">
                            <span>Upraveno</span>
                            <div class="flex flex-col ml-1">
                                <button data-action="click->file-browser#sortByCreatedAt" 
                                        data-order="desc"
                                        data-file-browser-target="sortDescButton"
                                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-0.5"
                                        title="Řadit od nejnověj<PERSON><PERSON><PERSON>">
                                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                                <button data-action="click->file-browser#sortByCreatedAt" 
                                        data-order="asc"
                                        data-file-browser-target="sortAscButton"
                                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-0.5"
                                        title="Řadit od nejstarších">
                                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <!-- Podsložky -->
                {% for podslozka in podslozky %}
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors duration-200 group"
                        data-action="dblclick->file-browser#navigateToFolderFromEvent contextmenu->file-browser#showFolderContextMenu"
                        data-slozka-id="{{ podslozka.id }}"
                        data-slozka-nazev="{{ podslozka.nazev }}">
                        <td class="px-4 py-4">
                            <svg class="w-5 h-5 text-yellow-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M3 8a2 2 0 0 1 2-2h3.93a2 2 0 0 0 1.66-.9l.82-1.2A2 2 0 0 1 13.07 3H19a2 2 0 0 1 2 2v13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8Z"/>
                            </svg>
                        </td>
                        <td class="px-4 py-4 font-medium text-gray-900 dark:text-white">
                            <div class="flex items-center">
                                <span>{{ podslozka.nazev }}</span>
                                <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(složka)</span>
                            </div>
                        </td>
                        <td class="px-4 py-4 text-gray-500 dark:text-gray-400">
                            --
                        </td>
                    </tr>
                {% endfor %}
                
                <!-- Soubory -->
                {% for soubor in soubory %}
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200 group"
                        data-soubor-id="{{ soubor.id }}"
                        data-action="contextmenu->file-browser#showContextMenu"
                        draggable="true">
                        <td class="px-4 py-4">
                            {% if 'pdf' in soubor.mimeType|lower %}
                                <svg class="w-5 h-5 text-red-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd" d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z" clip-rule="evenodd"/>
                                </svg>
                            {% elseif 'word' in soubor.mimeType|lower or 'document' in soubor.mimeType|lower %}
                                <svg class="w-5 h-5 text-blue-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd" d="M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5H9Zm2 0V2h7a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2Zm-1.02 4.804a1 1 0 1 0-1.96.392l1 5a1 1 0 0 0 1.838.319L12 15.61l1.143 1.905a1 1 0 0 0 1.838-.319l1-5a1 1 0 0 0-1.962-.392l-.492 2.463-.67-1.115a1 1 0 0 0-1.714 0l-.67 1.116-.492-2.464Z" clip-rule="evenodd"/>
                                </svg>
                            {% elseif 'image' in soubor.mimeType|lower %}
                                <svg class="w-5 h-5 text-green-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd" d="M13 10a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H14a1 1 0 0 1-1-1Z" clip-rule="evenodd"/>
                                    <path fill-rule="evenodd" d="M2 6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12c0 .556-.227 1.06-.593 1.422A.999.999 0 0 1 20.5 20H4a2.002 2.002 0 0 1-2-2V6Zm6.892 12 3.833-5.356-3.99-4.322a1 1 0 0 0-1.549.097L4 12.879V6h16v9.95l-3.257-3.619a1 1 0 0 0-1.557.088L11.2 18H8.892Z" clip-rule="evenodd"/>
                                </svg>
                            {% else %}
                                <svg class="w-5 h-5 text-gray-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd" d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22Z" clip-rule="evenodd"/>
                                    <path fill-rule="evenodd" d="M11 2H9v5a2 2 0 0 1-2 2H2v11a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2h-9Z" clip-rule="evenodd"/>
                                </svg>
                            {% endif %}
                        </td>
                        <td class="px-4 py-4 cursor-pointer"
                            data-action="click->file-browser#downloadFile"
                            data-soubor-id="{{ soubor.id }}">
                            <div class="font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                {{ soubor.originalFilename }}
                            </div>
                        </td>
                        <td class="px-4 py-4 text-gray-500 dark:text-gray-400">
                            {% if soubor.createdAt %}
                                {{ soubor.createdAt|date('d.m.Y H:i') }}
                            {% else %}
                                --
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <!-- Prázdná složka -->
    <div class="text-center py-12">
        <svg class="w-16 h-16 text-gray-400 mb-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h6l2 4m-8-4V3a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v9h2m8 0V9a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1m8-8v8a1 1 0 0 1-1 1H11a1 1 0 0 1-1-1V9"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Prázdná složka
        </h3>
        <p class="text-gray-500 dark:text-gray-400">
            Tato složka neobsahuje žádné soubory ani podsložky.
        </p>
    </div>
{% endif %}
