{#
File Browser Component

Povinné parametry:
- slozka: Slozka entity - slo<PERSON><PERSON>, j<PERSON><PERSON><PERSON> o<PERSON>ah se zobrazuje
- soubory: Array - pole entit Soubor pro zobrazení
- podslozky: Array - pole entit Slozka pro zobrazení podsložek
- breadcrumb: Array - breadcrumb data pro navigaci

Volitelné parametry:
- view_mode: String - 'list' (default) nebo 'grid'
- permissions: Array - oprávnění uživatele:
  - upload: boolean - povolení nahr<PERSON>ván<PERSON> souborů
  - delete: boolean - povolení mazání souborů
  - rename: boolean - povolení přejmenování souborů
  - create_folder: boolean - povolení vytváření složek

Příklad použití:
{{ include('components/file_browser/file_browser.html.twig', {
    slozka: slozka_entity,
    soubory: soubory_array,
    podslozky: podslozky_array,
    breadcrumb: breadcrumb_array,
    view_mode: 'list',
    permissions: {
        upload: true,
        delete: true,
        rename: true,
        create_folder: false
    }
}) }}
#}

{% set view_mode = view_mode|default('list') %}
{% set permissions = permissions|default({}) %}

<div data-controller="file-browser" 
     data-file-browser-slozka-id-value="{{ slozka.id }}"
     data-file-browser-view-mode-value="{{ view_mode }}"
     data-file-browser-folder-api-url-value="{{ path('api_soubor_folder_content', {id: slozka.id}) }}"
     data-file-browser-upload-api-url-value="{{ path('api_soubor_upload_to_folder', {id: slozka.id}) }}"
     data-file-browser-upload-url-pattern-value="{{ path('api_soubor_upload_to_folder', {id: '__FOLDER_ID__'}) }}"
     data-file-browser-download-url-pattern-value="{{ path('api_soubor_download', {id: '__ID__'}) }}"
     data-file-browser-delete-url-pattern-value="{{ path('api_soubor_delete', {id: '__ID__'}) }}"
     data-file-browser-rename-url-pattern-value="{{ path('api_soubor_rename', {id: '__ID__'}) }}"
     class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
     
    <!-- Header s breadcrumb -->
    {{ include('components/file_browser/_breadcrumb.html.twig', {
        breadcrumb: breadcrumb,
        slozka: slozka
    }) }}
    
    <!-- Toolbar -->
    {{ include('components/file_browser/_toolbar.html.twig', {
        slozka: slozka,
        permissions: permissions,
        view_mode: view_mode
    }) }}
    
    <!-- Upload zona -->
    {{ include('components/file_browser/_upload_zone.html.twig', {
        slozka: slozka
    }) }}
    
    <!-- Obsah složky -->
    <div data-file-browser-target="content" class="p-4">
        {% if view_mode == 'grid' %}
            {{ include('components/file_browser/_file_grid.html.twig', {
                soubory: soubory,
                podslozky: podslozky,
                slozka: slozka
            }) }}
        {% else %}
            {{ include('components/file_browser/_file_list.html.twig', {
                soubory: soubory,
                podslozky: podslozky,
                slozka: slozka
            }) }}
        {% endif %}
    </div>
    
    <!-- Loading overlay -->
    <div data-file-browser-target="loading" 
         class="hidden absolute inset-0 bg-white bg-opacity-75 dark:bg-gray-800 dark:bg-opacity-75 flex items-center justify-center rounded-lg">
        <div class="flex items-center space-x-2">
            <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm text-gray-600 dark:text-gray-400">Načítám...</span>
        </div>
    </div>
    
    <!-- Context menu -->
    {{ include('components/file_browser/_context_menu.html.twig') }}
    
    <!-- Delete confirmation modal -->
    {{ include('components/file_browser/_delete_confirmation_modal.html.twig') }}
    
    <!-- Rename modal -->
    {{ include('components/file_browser/_rename_modal.html.twig') }}
    
    <!-- Create folder modal -->
    {{ include('components/file_browser/_create_folder_modal.html.twig') }}
</div>
