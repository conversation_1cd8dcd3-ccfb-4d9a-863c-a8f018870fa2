<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4" data-controller="dashboard--small-section-row">
  {{ include('dashboard/_pojistna_udalost_counts.html.twig') }}
  {{ include('dashboard/_message_counts.html.twig') }}
  
  {% if user.likvidator %}
    {{ include('dashboard/_resitel_tasks.html.twig') }}
    {{ include('dashboard/_autor_tasks.html.twig') }}
  {% elseif user.administrator %}  
    {{ include('dashboard/_resitel_tasks.html.twig') }}
    {{ include('dashboard/_autor_tasks.html.twig') }}
    {{ include('dashboard/_all_tasks.html.twig') }}
    {{ include('dashboard/_overdue_tasks.html.twig') }}
  {% endif %}
</div>
{{ include('dashboard/_placeholder_large.html.twig') }}
<div class="grid grid-cols-2 gap-4 mb-4" data-controller="dashboard--small-section-row">
  {{ include('dashboard/_placeholder_small.html.twig') }}
  {{ include('dashboard/_placeholder_small.html.twig') }}
  {{ include('dashboard/_placeholder_small.html.twig') }}
  {{ include('dashboard/_placeholder_small.html.twig') }}
</div>
{{ include('dashboard/_placeholder_large.html.twig') }}
<div class="grid grid-cols-2 gap-4" data-controller="dashboard--small-section-row">
  {{ include('dashboard/_placeholder_small.html.twig') }}
  {{ include('dashboard/_placeholder_small.html.twig') }}
  {{ include('dashboard/_placeholder_small.html.twig') }}
  {{ include('dashboard/_placeholder_small.html.twig') }}
</div>
