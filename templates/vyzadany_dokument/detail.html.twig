{% extends 'page_templates/single_pu_dashboard.html.twig' %}
{% block title %}
	InIn - Detail vyžádaného dokumentu
{% endblock %}
{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full min-h-screen">
		<div id="vyzadany_dokument_detail" class="py-8 px-4 mx-auto max-w-2xl lg:py-16">
			<h2 class="mb-2 text-xl font-bold text-gray-900 dark:text-white">Detail vyžádaného dokumentu</h2>
			<h4 class="mb-6 font-bold text-gray-900 dark:text-white">k PU č.
				{{ pou.cisloPojistnaUdalost }}</h4>

			<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-5 border border-gray-200 dark:border-gray-700 flex flex-col flex-1">
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
					<div>
						<h5 class="text-sm font-semibold text-gray-500 dark:text-gray-400">Datum vytvoření</h5>
						<p class="text-base text-gray-900 dark:text-white">{{ vyzadany_dokument.createdAt|date('d.m.Y H:i') }}</p>
					</div>
					<div>
						<h5 class="text-sm font-semibold text-gray-500 dark:text-gray-400">Datum poslední úpravy</h5>
						<p class="text-base text-gray-900 dark:text-white">{{ vyzadany_dokument.updatedAt|date('d.m.Y H:i') }}</p>
					</div>
				</div>

				<div class="mb-4">
					<h5 class="text-sm font-semibold text-gray-500 dark:text-gray-400">Autor</h5>
					<p class="text-base text-gray-900 dark:text-white">{{ vyzadany_dokument.autor.getFullName() }}</p>
				</div>

				<div class="mb-4">
					<h5 class="text-sm font-semibold text-gray-500 dark:text-gray-400">Co vyžádáno</h5>
					<p class="text-base text-gray-900 dark:text-white">{{ vyzadany_dokument.coVyzadano }}</p>
				</div>

				<div class="mb-4">
					<h5 class="text-sm font-semibold text-gray-500 dark:text-gray-400">Stav</h5>
					<div>
						{% if vyzadany_dokument.stav == 'VYZADANO' %}
							<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">
								Vyžádáno
							</span>
						{% elseif vyzadany_dokument.stav == 'DOLOZENO' %}
							<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
								Doloženo
							</span>
						{% elseif vyzadany_dokument.stav == 'UZ_NEZADANO' %}
							<span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-gray-300">
								Už nežádáno
							</span>
						{% endif %}
					</div>
				</div>

				<div class="flex gap-2 mt-4">
					{% if not pou.uzavreno %}
						<a href="{{ path('app_vyzadany_dokument_edit', { id: vyzadany_dokument.id }) }}" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Editace</a>
						<button type="button" data-modal-target="popup-delete-{{ vyzadany_dokument.id }}" data-modal-toggle="popup-delete-{{ vyzadany_dokument.id }}" class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900">Smazat</button>
					{% endif %}

					<a href="{{ path('app_vyzadany_dokument_list', { id: pou.id }) }}" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">Zpět na vyžádané dokumenty</a>
				</div>

				{% if not pou.uzavreno %}
					<div id="popup-delete-{{ vyzadany_dokument.id }}" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
						<div class="relative p-4 w-full max-w-[75%] max-h-[75%]">
							<div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
								<button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="popup-delete-{{ vyzadany_dokument.id }}">
									<svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
									</svg>
									<span class="sr-only">Zavřít</span>
								</button>
								<div class="p-4 space-y-4">
									<h3 class="text-lg font-semibold text-gray-900 dark:text-white">Opravdu chcete smazat tuto položku?</h3>
									<div class="flex justify-end space-x-3">
										<button type="button" class="px-4 py-2 text-sm font-medium text-white bg-primary-700 rounded-lg focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-900 hover:bg-primary-800" data-modal-hide="popup-delete-{{ vyzadany_dokument.id }}">Zrušit</button>
										<form method="post" action="{{ path('app_vyzadany_dokument_delete', {'id': vyzadany_dokument.id}) }}" class="inline">
											<input type="hidden" name="_token" value="{{ csrf_token('delete' ~ vyzadany_dokument.id) }}">
											<button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-red-700 rounded-lg focus:ring-4 focus:ring-red-200 dark:focus:ring-red-900 hover:bg-red-800">Smazat</button>
										</form>
									</div>
								</div>
							</div>
						</div>
					</div>
				{% endif %}
			</div>
		</div>
	</section>
{% endblock %}
