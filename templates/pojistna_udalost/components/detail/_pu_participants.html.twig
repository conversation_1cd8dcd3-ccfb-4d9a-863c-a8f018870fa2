<div class="mt-5 bg-blue-50 p-6 rounded-lg shadow-md border border-blue-100 dark:bg-gray-700 dark:border-gray-600">
	<h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Účastníci pojistné ud<PERSON></h2>

	{# Prvn<PERSON> - Pojištěný a Poškozený #}
	<div
		class="flex flex-wrap gap-4 mb-4">
		{# Pojištěný - je pouze 1 max #}

		{% include 'pojistna_udalost/components/detail/_ucastnik.html.twig' with {
				'ucastnik': pojisteny,
				'nazevRole': 'Pojištěný'
			} %}


		{# Poškození #}
		{% for poskozeny in poskozeni %}
			{% include 'pojistna_udalost/components/detail/_ucastnik.html.twig' with {
				'ucastnik': poskozeny,
				'nazevRole': '<PERSON><PERSON><PERSON>zený'
			} %}
		{% endfor %}

		{# Pověřen<PERSON> osoba #}
		{% for poverenaOsoba in povereneOsoby %}
			{% include 'pojistna_udalost/components/detail/_ucastnik.html.twig' with {
				'ucastnik': poverenaOsoba,
				'nazevRole': 'Pověřená osoba'
			} %}
		{% endfor %}

		{# Pověřená osoba #}
		{% for kontaktniOsoba in kontaktniOsoby %}
			{% include 'pojistna_udalost/components/detail/_ucastnik.html.twig' with {
				'ucastnik': kontaktniOsoba,
				'nazevRole': 'Kontaktní osoba'
			} %}
		{% endfor %}


	</div>


	{# Tlačítko pro přidání nového účastníka #}
	<div class="mt-6 text-center">
		<a href="{{ path('app_pojistna_udalost_ucastnik_new', {'id': pou.id}) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-400">
			<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
			</svg>
			Přidat nového účastníka
		</a>
	</div>
</div>
