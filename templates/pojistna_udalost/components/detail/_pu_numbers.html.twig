<div class="grid grid-cols-1 md:grid-cols-4 gap-4 my-3">
	<div class="border min-h-10 p-2"><strong><span class="pr-3"><PERSON><PERSON>lo ŠU:</span></strong>
		{{ pou.cisloSkodniUdalost }}</div>
	<div class="border min-h-10 p-2"><strong><span class="pr-3">Číslo PU:</span></strong>
		{{ pou.cisloPojistnaUdalost }}</div>
	<div class="border min-h-10 p-2"><strong><span class="pr-3">Kategorie:</span></strong>
		{{ pou.getkategorieFullString() }}</div>
	<div class="border min-h-10 p-2 relative"
		{% if is_granted('ROLE_COM_ADMIN') and not pou.uzavreno %}
		data-controller="pojistnaudalost--likvidator"
		data-pojistnaudalost--likvidator-pojistna-udalost-id-value="{{ pou.id }}"
		data-pojistnaudalost--likvidator-get-likvidators-url-value="{{ path('api_likvidators') }}"
		data-pojistnaudalost--likvidator-change-likvidator-url-value="{{ path('api_pojistna_udalost_likvidator_change', {'id': pou.id}) }}"
		{% endif %}
	>
		<strong><span class="pr-3">Likvidátor:</span></strong>
		{% if is_granted('ROLE_COM_ADMIN') and not pou.uzavreno %}
			<span data-pojistnaudalost--likvidator-target="displayName" class="cursor-pointer hover:text-primary-700">
				{% if pou.likvidator %}
					{{ pou.likvidator.name }}
					{{ pou.likvidator.surname }}
				{% else %}
					zatím nepřiřazený
				{% endif %}
			</span>
			<div id="likvidator-spinner" class="absolute top-0 w-full h-full flex items-center justify-center hidden">
				{{ include('components/_spinner.html.twig') }}
			</div>
			<div data-pojistnaudalost--likvidator-target="selectContainer" class="hidden mt-2">
				<select data-pojistnaudalost--likvidator-target="selectInput" data-action="change->pojistnaudalost--likvidator#save" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
					<option value="">bez likvidátora</option>
					<!-- Bude naplněno pomocí AJAX -->
				</select>
			</div>
		{% else %}
			{% if pou.likvidator %}
				{{ pou.likvidator.name }}
				{{ pou.likvidator.surname }}
			{% else %}
				zatím nepřiřazený
			{% endif %}
		{% endif %}
	</div>
</div>
