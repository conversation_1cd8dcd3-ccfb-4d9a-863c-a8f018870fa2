<div class="py-4 flex justify-between">
  <h2 class="text-xl font-bold text-gray-900 dark:text-white">Pojistná událost č. {{ pou.cisloPojistnaUdalost }}</h2>
  <div class="flex flex-row gap-1">
    {% if is_granted('ROLE_COM_ADMIN') %}
      {% if pou.archive %}
        <span class="bg-red-100 text-red-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Archivováno</span>
        <a href="{{ path('app_pou_toggle_archive', { id: pou.id }) }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">Vyj<PERSON>ut z archivu</a>
      {% elseif pou.uzavreno %}
        <a href="{{ path('app_pou_toggle_archive', { id: pou.id }) }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">Archivovat</a>
      {% endif %}
    {% endif %}
    <div class="relative"
      {% if is_granted('ROLE_COM_ADMIN') and not pou.uzavreno %}
      data-controller="pojistnaudalost--stav"
      data-pojistnaudalost--stav-pojistna-udalost-id-value="{{ pou.id }}"
      data-pojistnaudalost--stav-get-stavy-url-value="{{ path('api_pojistna_udalost_stavy') }}"
      data-pojistnaudalost--stav-change-stav-url-value="{{ path('api_pojistna_udalost_stav_change', {'id': pou.id}) }}"
      {% endif %}
      class="text-xl font-bold text-gray-900 dark:text-white"
    >
      {% if is_granted('ROLE_COM_ADMIN') and not pou.uzavreno %}
        <button type="button" data-pojistnaudalost--stav-target="displayName" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 cursor-pointer">
          Stav: {{ pou.getStavLikvidaceLabel() }}
        </button>
        <div id="stav-spinner" class="absolute top-0 w-full h-full flex items-center justify-center">
          {{ include('components/_spinner.html.twig') }}
        </div>
        <div data-pojistnaudalost--stav-target="selectContainer" class="hidden mt-2">
          <select data-pojistnaudalost--stav-target="selectInput" data-action="change->pojistnaudalost--stav#save" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
            <!-- Bude naplněno pomocí AJAX -->
          </select>
        </div>
      {% else %}
        <button type="button" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
          Stav: {{ pou.getStavLikvidaceLabel() }}
        </button>
      {% endif %}
    </div>
  </div>
</div>
