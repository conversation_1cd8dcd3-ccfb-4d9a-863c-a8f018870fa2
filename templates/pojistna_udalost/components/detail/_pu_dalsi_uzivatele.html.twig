<div class="mt-4 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
	<div class="flex justify-between items-center mb-3">
		<h3 class="text-lg font-semibold text-gray-900 dark:text-white"><PERSON><PERSON><PERSON> u<PERSON> s přístupem</h3>
		<a href="{{ path('app_pojistna_udalost_dalsi_uzivatele', { id: pou.id }) }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
			<svg class="w-4 h-4 mr-2" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
				<path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
			</svg>
			Správa uživatelů
		</a>
	</div>

	{% if pou.dalsiUzivatele|length > 0 %}
		<div class="flow-root">
			<ul class="divide-y divide-gray-200 dark:divide-gray-700">
				{% for uzivatel in pou.dalsiUzivatele %}
					<li class="py-3 sm:py-4">
						<div class="flex items-center space-x-4">
							<div class="flex-shrink-0">
								<div class="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
									<span class="text-primary-800 dark:text-primary-300 font-medium">{{ uzivatel.name|first }}{{ uzivatel.surname|first }}</span>
								</div>
							</div>
							<div class="flex-1 min-w-0">
								<p class="text-sm font-medium text-gray-900 truncate dark:text-white">
									{{ uzivatel.fullName }}
								</p>
								<p class="text-sm text-gray-500 truncate dark:text-gray-400">
									{{ uzivatel.email }}
								</p>
							</div>
							<div class="inline-flex items-center space-x-3">
								<span class="text-base font-semibold text-gray-900 dark:text-white">
									{{ uzivatel.roleString }}
								</span>
								{% if is_granted('ROLE_COM_ADMIN') %}
									<a href="{{ path('app_pojistna_udalost_dalsi_uzivatele_remove', { id: pou.id, userId: uzivatel.id }) }}" class="text-red-600 hover:text-red-800 dark:text-red-500 dark:hover:text-red-400" onclick="return confirm('Opravdu chcete odebrat uživatele {{ uzivatel.fullName }} z pojistné události?')">
										<svg class="w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
										</svg>
									</a>
								{% endif %}
							</div>
						</div>
					</li>
				{% endfor %}
			</ul>
		</div>
	{% else %}
		<div class="py-4 text-center text-gray-500 dark:text-gray-400">
			<p>Žádní další uživatelé nemají přístup k této pojistné události.</p>
		</div>
	{% endif %}
</div>
