<div class="bg-white border border-gray-200 rounded-lg shadow p-4 dark:bg-gray-800 dark:border-gray-700 w-full min-w-[40%] xl:max-w-[49%]">
	<div class="flex justify-between items-center mb-3">
		{% if ucastnik.roleUcastnika == 'Kontaktní osoba' %}
			<h3 class="text-lg font-semibold text-gray-900 dark:text-white">Kontaktní osoba pro prohlídku</h3>
		{% else %}
			<h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ nazevRole }}</h3>
		{% endif %}

		{% if ucastnik %}
			<a href="{{ path('app_pojistna_udalost_ucastnik_edit', {'id': ucastnik.id}) }}" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-3 py-1.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
				Upravit
			</a>
		{% endif %}
	</div>

	{% if ucastnik %}
		{% if ucastnik.isCompany() %}
			{# Zobrazení pro firmu #}
			<div class="space-y-2">
				<div class="flex ">
					<span class="font-bold">{{ ucastnik.firma }}</span>
				</div>
				<div class="flex flex-row gap-2">
					<span class="">Sídlo:</span>
					<span class="">{{ ucastnik.sidlo|default('Neuvedeno') }}</span>
				</div>
				<div class="flex flex-row gap-2">
					<span class="">IČO:</span>
					<span class="">{{ ucastnik.ico|default('Neuvedeno') }}</span>
				</div>

				{% if nazevRole != 'Kontaktní osoba' and nazevRole != 'Pověřená osoba' %}
					{% set cisloUctu = ucastnik.cisloUctu|default('')|trim %}

					<div class="flex flex-row gap-2">
						<span class="">Č. účtu:</span>
						<span class="">{{ cisloUctu is empty ? 'Neuvedeno' : cisloUctu }}</span>
					</div>
				{% endif %}

				<div class="flex flex-row">
					{% if nazevRole != 'Kontaktní osoba' %}
						<span class="font-bold pr-2 border-r-2 border-black hover:underline mr-2">
							{% if ucastnik.email %}
								<a href="mailto:{{ ucastnik.email }}">
									{{ ucastnik.email }}</a>
							{% else %}
								Neuvedeno
							{% endif %}
						</span>
					{% endif %}
					<span class="font-bold">{{ ucastnik.telefon|default('Neuvedeno') }}</span>
				</div>

				<div class="flex flex-row gap-2">
					<span class="font-bold">Plátce DPH:
					</span>
					<span class="font-bold">{{ ucastnik.isPlatceDPH() ? 'ANO' : 'NE' }}</span>
				</div>
			</div>
		{% else %}
			{# Zobrazení pro fyzickou osobu #}
			<div class="space-y-2">
				<div class="flex">
					<span class="font-bold">{{ ucastnik.prijmeni }}
						{{ ucastnik.jmeno }}</span>
				</div>

				{% if nazevRole == 'Pojištěný' or nazevRole == 'Poškozený' %}
					<div class="flex">
						<span class="font-medium">{{ ucastnik.adresa|default('Neuvedeno') }}</span>
					</div>
					<div class="flex flex-row gap-2">
						<span class="">RČ:</span>
						<span class="">{{ ucastnik.rodneCislo|default('Neuvedeno') }}</span>
					</div>
					<div class="flex flex-row gap-2">
						<span class="">Č. účtu</span>
						<span class="">{{ ucastnik.cisloUctu|default('Neuvedeno') }}</span>
					</div>
				{% endif %}
				<div class="flex flex-row">
					{% if nazevRole != 'Kontaktní osoba' %}
						<span class="font-bold pr-2 border-r-2 border-black hover:underline mr-2">
							{% if ucastnik.email %}
								<a href="mailto:{{ ucastnik.email }}">
									{{ ucastnik.email }}</a>
							{% else %}
								Neuvedeno
							{% endif %}
						</span>
					{% endif %}


					<span class="font-bold">{{ ucastnik.telefon|default('Neuvedeno') }}</span>
				</div>

				{% if nazevRole == 'Pojištěný' or nazevRole == 'Poškozený' %}
				<div class="flex flex-row gap-2">
					<span class="font-bold">Plátce DPH:
					</span>
					<span class="font-bold">{{ ucastnik.isPlatceDPH() ? 'ANO' : 'NE' }}</span>
				</div>
				{% endif %}

			</div>
		{% endif %}
	{% else %}
		<p class="text-gray-500 dark:text-gray-400">Žádný účastník typu
			{{ nazevRole|lower }}
			nebyl nalezen.</p>
	{% endif %}
</div>
