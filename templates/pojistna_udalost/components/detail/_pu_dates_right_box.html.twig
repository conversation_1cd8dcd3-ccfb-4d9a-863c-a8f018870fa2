<!-- Detaily místa a prohlídky -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-5 border border-gray-200 dark:border-gray-700 flex flex-col flex-1">
	<h4 class="text-base font-medium text-gray-900 dark:text-white mb-3"><PERSON><PERSON><PERSON><PERSON><PERSON></h4>
	<div class="flex flex-col flex-1 justify-between ">
		<div class="mb-4" {% if not pou.uzavreno %} data-controller="prohlidka--add" data-prohlidka--add-pojistna-udalost-id-value="{{ pou.id }}" data-prohlidka--add-get-users-url-value="{{ path('api_users_non_admin') }}" data-prohlidka--add-save-prohlidka-url-value="{{ path('api_prohlidka_add') }}" {% endif %}>
			<div class="mb-2 relative">
				<div id="prohlidka-spinner" class="absolute w-full h-full flex items-center justify-center hidden">
				{{ include('components/_spinner.html.twig') }}
				</div>



				{% for prohlidka in pou.prohlidky %}
					<div class="flex justify-between mb-2">
						<div class="flex items-center">
							<div class="border p-2 rounded-lg">
								<p>
									<strong>Technik:</strong>
									{{ prohlidka.technik.name }}
									{{ prohlidka.technik.surname }}
								</p>
							</div>

							{% if is_granted('ROLE_COM_ADMIN') and not pou.uzavreno and prohlidka.datum is null %}
								<div class="ml-2" data-controller="prohlidka--delete" data-prohlidka--delete-prohlidka-id-value="{{ prohlidka.id }}" data-prohlidka--delete-delete-prohlidka-url-value="{{ path('api_prohlidka_delete', {'id': prohlidka.id}) }}">
									<button type="button" data-action="prohlidka--delete#delete" class="text-red-600 hover:text-red-800 p-1 rounded-lg" title="Smazat prohlídku">
										<svg class="w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
										</svg>
									</button>
								</div>
							{% endif %}
						</div>

						<div class="relative max-w-44" 
						{% if not pou.uzavreno %} data-controller="prohlidka--update-datum" data-prohlidka--update-datum-prohlidka-id-value="{{ prohlidka.id }}" data-prohlidka--update-datum-update-datum-url-value="{{ path('api_prohlidka_update_datum', {'id': prohlidka.id}) }}" 
						{% endif %}
						>
							<div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
								<svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewbox="0 0 20 20">
									<path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
								</svg>
							</div>
							<input type="text" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Zadat datum" 
							{% if not pou.uzavreno %} 
							data-prohlidka--update-datum-target="datumInput",
							{% endif %}

							{% if prohlidka.datum %}
								value="{{ prohlidka.datum|date('d.m.Y') }}"
							{% endif %}
							>

						</div>


					</div>
				{% else %}
					<p class=" text-gray-500">Žádné prohlídky</p>
				{% endfor %}
				</div>

				{% if not pou.uzavreno %}
					<button type="button" data-prohlidka--add-target="displayButton" data-action="prohlidka--add#showForm" class="w-full text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 uppercase">
						+ přidat další prohlídku
					</button>

					<div data-prohlidka--add-target="formContainer" class="hidden mt-2">
						<select data-prohlidka--add-target="selectInput" data-action="change->prohlidka--add#save" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500 mb-2">
							<option value="">Vyberte technika</option>
							<!-- Bude naplněno pomocí AJAX -->
						</select>
						<div class="flex justify-end">
							<button type="button" data-action="prohlidka--add#cancel" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-3 py-1.5 me-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
								Zrušit
							</button>
						</div>
					</div>
				{% endif %}
			</div>

			<div class="mb-4">

				<p>
					<strong>Místo vzniku PU:</strong>
					{{ pou.mistoPu ?: 'Nezadáno' }}</p>
			</div>
		</div>
	</div>
