<div class="bg-white gap-1 flex flex-col p-4 border rounded-md">
  <div class="flex justify-between">
    <p class="font-bold text-2xl">
      {{ ucastnik.jmeno }} {{ ucastnik.prijmeni }}
      {{ include('pojistna_udalost/components/detail/_pu_participant_role_badge.html.twig') }}
    </p>
    <div class="flex items-center gap-x-1">
      <button data-modal-target="popup-detail-{{ ucastnik.id }}" data-modal-toggle="popup-detail-{{ ucastnik.id }}" class="flex border items-center justify-center text-white px-4 py-2 text-sm font-medium rounded-md bg-blue-700 hover:bg-blue-800">Detail</button>
      {% if not pou.uzavreno %}
        <a href="{{ path('app_pojistna_udalost_ucastnik_edit', { id: ucastnik.id }) }}" class="flex border items-center justify-center text-white px-4 py-2 text-sm font-medium rounded-md bg-blue-700 hover:bg-blue-800">Upravit</a>
        <a href="#" data-action="click->pojistnaudalost--deleteucastnik#deleteUcastnikModalDialog" data-ucastnik-id="{{ ucastnik.id }}" data-ucastnik-name="{{ ucastnik.jmeno }} {{ ucastnik.prijmeni }}" data-ucastnik-token="{{ csrf_token('delete' ~ ucastnik.id) }}" data-ucastnik-deleteurl="{{ path('app_pojistna_udalost_ucastnik_delete', { id: ucastnik.id }) }}" class="flex border items-center justify-center text-white px-4 py-2 text-sm font-medium rounded-md bg-red-700 hover:bg-red-800">Smazat</a>
      {% endif %}

      {{ include('pojistna_udalost/modal/_ucastnik_detail_modal.html.twig') }}
    </div>
  </div>
  <hr class="my-1" />
  <div class="w-full">
    <div class="float-left w-1/2">
      <div>
        <a class="font-medium text-blue-600 dark:text-blue-500 hover:underline" href="mailto:{{ ucastnik.email }}">{{ ucastnik.email }}</a>
        | Tel:
        <a class="font-medium text-blue-600 dark:text-blue-500 hover:underline" href="tel:{{ ucastnik.telefon }}">{{ ucastnik.telefon }}</a>
      </div>
      <p>RČ: {{ ucastnik.rodneCislo }}</p>
    </div>
    <div class="float-left w-1/2">
      <div>Pojištovna: {{ ucastnik.pojistovna }}</div>
      <div>Číslo smlouvy: {{ ucastnik.cisloSmlouvy }}</div>
    </div>
  </div>
  {% if ucastnik.poznamka is not null %}
    <p class="font-bold text-lg">Poznámka:</p>
    <p>{{ ucastnik.poznamka }}</p>
  {% endif %}

  {% if pou.isCarAllowed() %}
    {% if not pou.uzavreno %}
      <div>
        <a href="{{ path('app_pojistna_udalost_ucastnik_vozidlo_new', { id: ucastnik.id }) }}" class="w-48 float-left flex border items-center justify-center text-white px-4 py-2 text-sm font-medium rounded-md bg-blue-700 hover:bg-blue-800">Přidat vozidlo</a>
      </div>
    {% endif %}
    <div>
      <table class="w-full">
        <tr>
          <th>RZ</th>
          <th>VIN</th>
          <th>VÝKON</th>
          <th>OBSAH</th>
          <th>DRUH</th>
          <th>TYP</th>
          <th></th>
        </tr>
        {% for vozidlo in ucastnik.getVozidla() %}
          <tr>
            <td class="text-center">{{ vozidlo.kodRZ }}</td>
            <td class="text-center">{{ vozidlo.kodVIN }}</td>
            <td class="text-center">{{ vozidlo.vykon }}</td>
            <td class="text-center">{{ vozidlo.obsah }}</td>
            <td class="text-center">{{ vozidlo.druh }}</td>
            <td class="text-center">{{ vozidlo.typ }}</td>
            {% if not pou.uzavreno %}
              <td>
                <a href="{{ path('app_pojistna_udalost_ucastnik_vozidlo_edit', { id: vozidlo.id }) }}" class="max-w-16 float-left flex border items-center justify-center text-white px-4 py-2 text-sm font-medium rounded-md bg-blue-700 hover:bg-blue-800">Upravit</a>
                <a href="#" data-action="click->pojistnaudalost--deletevehicle#deleteVehicleModalDialog" data-vehicle-id="{{ vozidlo.id }}" data-vehicle-kodrz="{{ vozidlo.kodRZ }}" data-vehicle-token="{{ csrf_token('delete' ~ vozidlo.id) }}" data-vehicle-deleteurl="{{ path('app_pojistna_udalost_ucastnik_vozidlo_delete', { id: vozidlo.id }) }}" class="max-w-16 float-left flex border items-center justify-center text-white px-4 py-2 text-sm font-medium rounded-md bg-red-700 hover:bg-red-800">Smazat</a>
              </td>
            {% endif %}
          </tr>
        {% endfor %}
      </table>
    </div>
  {% endif %}
</div>
