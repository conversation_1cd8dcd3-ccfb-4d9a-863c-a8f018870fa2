{% extends 'page_templates/single_pu_dashboard.html.twig' %}
{% block title %}
	InIn - Ukončení pojist<PERSON>
{% endblock %}

{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full" data-controller="pojistnaudalost--close-confirmation">
		<div class="py-8 px-4 mx-auto max-w-6xl lg:py-16 min-h-screen">
			{{ include('pojistna_udalost/components/_pu_header_number.html.twig',{pou:pou}) }}

			<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
				Ukončení PU
			</h3>

			<div class="flex flex-col lg:flex-row justify-between gap-3">
				<div class="flex flex-col flex-1 w-full lg:w-1/2">
					<!-- File Browser komponenta pro dokumenty pro pojiš<PERSON>ovnu -->
					{{ include('components/file_browser/file_browser.html.twig', {
						slozka: slozka_ukonceni,
						soubory: soubory_ukonceni,
						podslozky: podslozky_ukonceni,
						breadcrumb: breadcrumb_ukonceni,
						view_mode: 'list',
						permissions: {
							upload: true,
							create_folder: false,
							delete: false,
							rename: false
						}
					}) }}
				</div>
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-5 border border-gray-200 dark:border-gray-700 flex flex-col flex-1 w-full lg:w-1/2">
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
						Likvidační protokol
					</h3>
					{{ include('pojistna_udalost/ukonceni/protokol/index.html.twig') }}
				</div>
			</div>

			<!-- Sekce pro odeslání a uzavření PU -->
			<div class="mt-6">
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
					<div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
						<div class="flex-1">
							<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
								Dokončení pojistné události
							</h3>
							<p class="text-sm text-gray-600 dark:text-gray-400">
								Odešlete dokumenty do pojišťovny a uzavřete pojistnou událost.
							</p>
							{% if not can_close_pu and close_validation_errors|length > 0 %}
								<div class="mt-3">
									<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded dark:bg-red-900/20 dark:border-red-800 dark:text-red-400">
										<div class="flex">
											<div class="flex-shrink-0">
												<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
													<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
												</svg>
											</div>
											<div class="ml-3">
												<h3 class="text-sm font-medium">
													Pojistnou událost nelze uzavřít
												</h3>
												<div class="mt-2 text-sm">
													<ul class="list-disc list-inside space-y-1">
														{% for error in close_validation_errors %}
															<li>{{ error }}</li>
														{% endfor %}
													</ul>
												</div>
											</div>
										</div>
									</div>
								</div>
							{% endif %}
						</div>
						<div class="flex-shrink-0">
							{% if can_close_pu %}
								<button type="button" 
									data-action="click->pojistnaudalost--close-confirmation#showConfirmationModal"
									data-csrf-token="{{ csrf_token }}"
									data-pu-id="{{ pou.id }}"
									class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
									<svg class="w-4 h-4 mr-2 inline" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19V5m0 14-4-4m4 4 4-4"/>
									</svg>
									Odeslat
								</button>
							{% else %}
								<button type="button" disabled
									class="text-gray-400 bg-gray-300 cursor-not-allowed font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-600 dark:text-gray-500">
									<svg class="w-4 h-4 mr-2 inline" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
										<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19V5m0 14-4-4m4 4 4-4"/>
									</svg>
									Odeslat
								</button>
							{% endif %}
						</div>
					</div>
				</div>
			</div>

		</div>

		<!-- Modal pro potvrzení uzavření PU -->
		<div id="closePuConfirmationModal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
			<div class="relative p-4 w-full max-w-md max-h-full">
				<!-- Modal content -->
				<div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
					<!-- Modal header -->
					<div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
						<h3 class="text-lg font-semibold text-gray-900 dark:text-white">
							Potvrzení uzavření pojistné události
						</h3>
						<button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="closePuConfirmationModal">
							<svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
								<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
							</svg>
							<span class="sr-only">Zavřít modal</span>
						</button>
					</div>
					<!-- Modal body -->
					<div class="p-4 md:p-5">
						<div class="mb-4">
							<div class="flex items-center p-4 mb-4 text-blue-800 border border-blue-300 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-blue-800">
								<svg class="flex-shrink-0 w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
									<path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
								</svg>
								<div class="ms-3 text-sm font-medium">
									Opravdu chcete škodu ukončit a odeslat do pojišťovny?
								</div>
							</div>
						</div>
						
						<div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
							<p class="mb-2"><strong>Co se stane po potvrzení:</strong></p>
							<ul class="list-disc list-inside space-y-1 ml-4">
								<li>Dokumenty budou odeslány do pojišťovny</li>
								<li>Soubory budou přesunuty do složky "odesláno {{ "now"|date("d.m.Y") }}"</li>
								<li>Pojistná událost bude označena jako uzavřená</li>
							</ul>
						</div>
					</div>
					<!-- Modal footer -->
					<div class="flex items-center p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
						<button type="button" 
							data-action="click->pojistnaudalost--close-confirmation#confirmClosePu"
							class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800">
							Ano, uzavřít pojistnou událost
						</button>
						<button type="button" 
							data-action="click->pojistnaudalost--close-confirmation#cancelClosePu"
							class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
							Zrušit
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Skrytý formulář pro odeslání -->
		<form id="closePuConfirmationForm" method="POST" style="display: none;">
			<input type="hidden" name="token" data-pojistnaudalost--close-confirmation-target="csrfToken">
		</form>

	</section>

{% endblock %}
