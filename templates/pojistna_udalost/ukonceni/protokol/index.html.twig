<div data-controller="likvidacniprotokol--upload-toggle" class="flex flex-col gap-4">
	<!-- T<PERSON><PERSON><PERSON>tko pro zobrazení upload sekce -->
	<div data-likvidacniprotokol--upload-toggle-target="toggleButton" class="flex justify-center">
		<button data-action="click->likvidacniprotokol--upload-toggle#showUpload" 
				class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white">
			<svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
			</svg>
			Přidat nový protokol
		</button>
	</div>
	
	<!-- Upload sekce - defaultně skrytá -->
	<div data-likvidacniprotokol--upload-toggle-target="uploadSection" class="hidden">
		{{ include('pojistna_udalost/ukonceni/protokol/_upload_section.html.twig') }}
		<!-- Tlačítko pro skrytí -->
		<div class="flex justify-center mt-2">
			<button data-action="click->likvidacniprotokol--upload-toggle#hideUpload" 
					class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
				Zrušit
			</button>
		</div>
	</div>
	
	<!-- Seznam souborů zůstává vždy viditelný -->
	{{ include('pojistna_udalost/ukonceni/protokol/_file_list_section.html.twig') }}
</div>
