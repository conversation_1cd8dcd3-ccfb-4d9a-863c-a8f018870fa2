{% extends 'page_templates/single_pu_dashboard.html.twig' %}
{% block title %}
	InIn - Úprava informací o pojistné smlouvě
{% endblock %}

{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full min-h-screen">
		<div class="py-8 px-4 mx-auto max-w-2xl lg:py-16" data-controller="pojistnaudalost--pojistna-smlouva-edit">
		{{ include('pojistna_udalost/components/_pu_header_number.html.twig',{pou:pojistna_udalost}) }}
			<h2 class="mb-4 text-xl font-bold text-gray-900 dark:text-white">Úprava informací o pojistné smlouvě</h2>
			{{ form_start(form, {'attr': {'data-pojistnaudalost--pojistna-smlouva-edit-target': 'form'}}) }}

			<div class="flex flex-col gap-4 mb-4">
				{{ form_row(form.PSNazev, { label: 'Název' }) }}
				<div class="flex flex-col md:flex-row gap-4">
					<div class="min-w-56">
						{{ form_row(form.cisloPojistneSmlouvy, { label: 'Číslo' }) }}
					</div>

					<div class="min-w-56">
						{{ form_row(form.PSPlatnostOd, { label: 'Platnost od' }) }}
					</div>

				</div>
				<div class="flex flex-col md:flex-row gap-4">
					<div class="min-w-56">
						{{ form_row(form.PSMistoPojisteni, { label: 'Místo pojištění' }) }}
					</div>

					<div class="min-w-56">
						{{ form_row(form.PSZPojisteni, { label: 'Z pojištění' }) }}
					</div>

				</div>
				<div class="flex flex-col md:flex-row gap-4">
					<div class="min-w-56">
						{{ form_row(form.castkaPojistna, { label: 'Pojistná částka', attr: {'data-pojistnaudalost--pojistna-smlouva-edit-target': 'castkaPojistna'} }) }}
					</div>
					<div class="min-w-56">
						{{ form_row(form.castkaSpoluucast, { label: 'Spoluúčast', attr: {'data-pojistnaudalost--pojistna-smlouva-edit-target': 'castkaSpoluucast'} }) }}
					</div>
				</div>

				<div class="flex flex-col md:flex-row gap-4">
					<div class="min-w-56 md:max-w-56">
						{{ form_row(form.PSZRizika, { label: 'Z pojištěného rizika' }) }}
					</div>
					<div class="min-w-56 flex flex-row">
						<div>{{ form_row(form.castkaLimitRiziko, { label: 'Limit rizika', attr: {'data-pojistnaudalost--pojistna-smlouva-edit-target': 'castkaLimitRiziko'} }) }}</div>
						<div class="flex flex-col-reverse mb-4 mx-3 text-2xl">/</div>
						<div>{{ form_row(form.PSLimitRizikoNa, { label: 'Na' }) }}</div>


					</div>

				</div>
				<div class="flex flex-col md:flex-row gap-4">
					<div class="min-w-56">
						{{ form_row(form.PSVariantaPojisteni, { label: 'Varianta pojištění' }) }}
					</div>
					<div class="min-w-56">
						{{ form_row(form.PSVPP, { label: 'VPP' }) }}
					</div>
				</div>

				<div class="flex">
						{{ form_row(form.PSPojisteno, { label: 'Pojištěno' }) }}
				</div>

				<!-- Sekce pro Stavba -->
				<div id="limity-rizik-stavba-section" class="mt-4" style="display: none;" data-pojistnaudalost--pojistna-smlouva-edit-target="limityRizikaStavbaSection">
					<h3 class="font-semibold text-gray-900 dark:text-white mb-2">Stavba</h3>
					<div class="grid grid-cols-2 gap-4 mb-2">
						<div>
							<h4 class="font-medium text-gray-900 dark:text-white">Pojištěná rizika</h4>
						</div>
						<div>
							<h4 class="font-medium text-gray-900 dark:text-white">Limit Rizika / Na</h4>
						</div>
					</div>
					
					<div data-pojistnaudalost--pojistna-smlouva-edit-target="limityRizikaStavbaContainer">
						{% for limitRizikaForm in form.limityRizikaStavba %}
							<div class="grid grid-cols-2 gap-4 mb-2 limit-rizika-stavba-row">
								<div>
									{{ form_widget(limitRizikaForm.zRizika) }}
									{{ form_widget(limitRizikaForm.pojisteneRiziko) }}
									{{ form_widget(limitRizikaForm.id) }}
								</div>
								<div class="flex items-center">
									<div class="flex-1">
										{{ form_widget(limitRizikaForm.castkaLimitRizika) }}
									</div>
									<div class="mx-2">/</div>
									<div class="flex-1">
										{{ form_widget(limitRizikaForm.limitRizikaNa) }}
									</div>
									<button type="button" class="ml-2 text-red-500 remove-limit-rizika-stavba">
										<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
										</svg>
									</button>
								</div>
							</div>
						{% endfor %}
					</div>
					
					<div class="flex justify-end mt-2">
						<button type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-3 py-1.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" data-pojistnaudalost--pojistna-smlouva-edit-target="addLimitRizikaStavbaButton">
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
							</svg>
						</button>
					</div>
				</div>

				<!-- Sekce pro Domácnost -->
				<div id="limity-rizik-domacnost-section" class="mt-4" style="display: none;" data-pojistnaudalost--pojistna-smlouva-edit-target="limityRizikaDomacnostSection">
					<h3 class="font-semibold text-gray-900 dark:text-white mb-2">Domácnost</h3>
					<div class="grid grid-cols-2 gap-4 mb-2">
						<div>
							<h4 class="font-medium text-gray-900 dark:text-white">Pojištěná rizika</h4>
						</div>
						<div>
							<h4 class="font-medium text-gray-900 dark:text-white">Limit Rizika / Na</h4>
						</div>
					</div>
					
					<div data-pojistnaudalost--pojistna-smlouva-edit-target="limityRizikaDomacnostContainer">
						{% for limitRizikaForm in form.limityRizikaDomacnost %}
							<div class="grid grid-cols-2 gap-4 mb-2 limit-rizika-domacnost-row">
								<div>
									{{ form_widget(limitRizikaForm.zRizika) }}
									{{ form_widget(limitRizikaForm.pojisteneRiziko) }}
									{{ form_widget(limitRizikaForm.id) }}
								</div>
								<div class="flex items-center">
									<div class="flex-1">
										{{ form_widget(limitRizikaForm.castkaLimitRizika) }}
									</div>
									<div class="mx-2">/</div>
									<div class="flex-1">
										{{ form_widget(limitRizikaForm.limitRizikaNa) }}
									</div>
									<button type="button" class="ml-2 text-red-500 remove-limit-rizika-domacnost">
										<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
											<path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
										</svg>
									</button>
								</div>
							</div>
						{% endfor %}
					</div>
					
					<div class="flex justify-end mt-2">
						<button type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-3 py-1.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800" data-pojistnaudalost--pojistna-smlouva-edit-target="addLimitRizikaDomacnostButton">
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
							</svg>
						</button>
					</div>
				</div>

			</div>

			<!-- Template pro nové řádky Stavba -->
			<template id="limit-rizika-stavba-template" data-pojistnaudalost--pojistna-smlouva-edit-target="limitRizikaStavbaTemplate">
				<div class="grid grid-cols-2 gap-4 mb-2 limit-rizika-stavba-row">
					<div>
						<select name="pojistna_smlouva_edit[limityRizikaStavba][__name__][zRizika]" required="required" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
							<option value="">Vyberte riziko</option>
							{% for riziko in rizika %}
								<option value="{{ riziko.id }}">{{ riziko.typPojistneni }}</option>
							{% endfor %}
						</select>
						<input type="hidden" name="pojistna_smlouva_edit[limityRizikaStavba][__name__][pojisteneRiziko]" value="stavba" />
					</div>
					<div class="flex items-center">
						<div class="flex-1">
							<input type="text" name="pojistna_smlouva_edit[limityRizikaStavba][__name__][castkaLimitRizika]" required="required" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" data-pojistnaudalost--pojistna-smlouva-edit-target="castkaLimitRizika" />
						</div>
						<div class="mx-2">/</div>
						<div class="flex-1">
							<select name="pojistna_smlouva_edit[limityRizikaStavba][__name__][limitRizikaNa]" required="required" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
								<option value="rok">Rok</option>
								<option value="PU">PU</option>
							</select>
						</div>
						<button type="button" class="ml-2 text-red-500 remove-limit-rizika-stavba">
							<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
							</svg>
						</button>
					</div>
				</div>
			</template>

			<!-- Template pro nové řádky Domácnost -->
			<template id="limit-rizika-domacnost-template" data-pojistnaudalost--pojistna-smlouva-edit-target="limitRizikaDomacnostTemplate">
				<div class="grid grid-cols-2 gap-4 mb-2 limit-rizika-domacnost-row">
					<div>
						<select name="pojistna_smlouva_edit[limityRizikaDomacnost][__name__][zRizika]" required="required" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
							<option value="">Vyberte riziko</option>
							{% for riziko in rizika %}
								<option value="{{ riziko.id }}">{{ riziko.typPojistneni }}</option>
							{% endfor %}
						</select>
						<input type="hidden" name="pojistna_smlouva_edit[limityRizikaDomacnost][__name__][pojisteneRiziko]" value="domacnost" />
					</div>
					<div class="flex items-center">
						<div class="flex-1">
							<input type="text" name="pojistna_smlouva_edit[limityRizikaDomacnost][__name__][castkaLimitRizika]" required="required" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" data-pojistnaudalost--pojistna-smlouva-edit-target="castkaLimitRizika" />
						</div>
						<div class="mx-2">/</div>
						<div class="flex-1">
							<select name="pojistna_smlouva_edit[limityRizikaDomacnost][__name__][limitRizikaNa]" required="required" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
								<option value="rok">Rok</option>
								<option value="PU">PU</option>
							</select>
						</div>
						<button type="button" class="ml-2 text-red-500 remove-limit-rizika-domacnost">
							<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
							</svg>
						</button>
					</div>
				</div>
			</template>

			<div class="flex items-center space-x-4">
				<button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
					Uložit změny
				</button>
				<a href="{{ path('app_pojistna_smlouva_info', {'id': pojistna_udalost.id}) }}" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
					Zrušit
				</a>
			</div>

			{{ form_end(form) }}
		</div>
	</section>
{% endblock %}
