{% extends 'page_templates/single_pu_dashboard.html.twig' %}
{% block title %}
	InIn - Pojistná smlouva k pojistn<PERSON> u<PERSON>
{% endblock %}


{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full min-h-screen">
		<div
			id="additiona_pu_info" class="py-8 px-4 mx-auto max-w-3xl lg:py-16">
			{{ include('pojistna_udalost/components/_pu_header_number.html.twig',{pou:pojistna_udalost}) }}
			<!-- Edit button -->
			<div class="flex justify-end">
				<div class="mb-6">
					<a href="{{ path('app_pojistna_smlouva_edit', {'id': pojistna_udalost.id}) }}" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Upravit</a>
				</div>

			</div>
			<!-- Main card -->
			<div class="bg-white border border-gray-200 rounded-lg shadow-md dark:bg-gray-800 dark:border-gray-700 overflow-hidden">
				<div class="p-6">
					<h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Informace o pojistné smlouvě</h2>

					<div
						class="space-y-4">
						<!-- Název -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Název</div>
								<div class="w-1/2">{{ pojistna_udalost.PSNazev ?? 'Neuvedeno' }}</div>
							</div>
						</div>

						<!-- Číslo -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Číslo</div>
								<div class="w-1/2">{{ pojistna_udalost.cisloPojistneSmlouvy ?? 'Neuvedeno' }}</div>
							</div>
						</div>

						<!-- Platnost od -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Platnost od</div>
								<div class="w-1/2">{{ pojistna_udalost.PSPlatnostOd ? pojistna_udalost.PSPlatnostOd|date('d.m.Y') : 'Neuvedeno' }}</div>
							</div>
						</div>

						<!-- Místo pojištění -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Místo pojištění</div>
								<div class="w-1/2">{{ pojistna_udalost.PSMistoPojisteni ?? 'Neuvedeno' }}</div>
							</div>
						</div>

						<!-- Z pojištění -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Z pojištění</div>
								<div class="w-1/2">{{ pojistna_udalost.PSZPojisteni ?? 'Neuvedeno' }}</div>
							</div>
						</div>

						<!-- Pojistná částka -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Pojistná částka</div>
								<div class="w-1/2">{{ pojistna_udalost.castkaPojistna ? pojistna_udalost.castkaPojistna|number_format(0, ',', '.') ~ ' Kč' : 'Neuvedeno' }}</div>
							</div>
						</div>

						<!-- Spoluúčast -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Spoluúčast</div>
								<div class="w-1/2">{{ pojistna_udalost.castkaSpoluucast ? pojistna_udalost.castkaSpoluucast|number_format(0, ',', '.') ~ ' Kč' : 'Neuvedeno' }}</div>
							</div>
						</div>

						<!-- Z pojištěného rizika -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Z pojištěného rizika</div>
								<div class="w-1/2">{{ pojistna_udalost.PSZRizika ? pojistna_udalost.PSZRizika.typPojistneni : 'Neuvedeno' }}</div>
							</div>
						</div>

						<!-- Limit rizika -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Limit rizika</div>
								<div class="w-1/2">
									{{ pojistna_udalost.castkaLimitRiziko ? pojistna_udalost.castkaLimitRiziko|number_format(0, ',', '.') ~ ' Kč' : 'Neuvedeno' }}
									{% if pojistna_udalost.PSLimitRizikoNa %}
										/
										{{ pojistna_udalost.PSLimitRizikoNa }}
									{% endif %}
								</div>

							</div>
						</div>

						<!-- Varianta pojištění -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Varianta pojištění</div>
								<div class="w-1/2">{{ pojistna_udalost.PSVariantaPojisteni ?? 'Neuvedeno' }}</div>
							</div>
						</div>

						<!-- VPP -->
						<div class="border border-gray-200 rounded-lg p-4">
							<div class="flex items-center">
								<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">VPP</div>
								<div class="w-1/2">{{ pojistna_udalost.PSVPP ? pojistna_udalost.PSVPP.typVpp : 'Neuvedeno' }}</div>
							</div>
						</div>


						<!-- Pojisteno stavba -->
						{% if show_stavba_section %}
							<div class="border border-gray-200 rounded-lg p-4 mt-6">
								<div class="flex items-center">
									<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Pojištěno</div>
									<div class="w-1/2">Stavba</div>
								</div>
							</div>
							<div class="border border-gray-200 rounded-lg p-4">
								<div class="flex">
									<div>
										{% for limit in limity_stavba %}
											{{ limit.zRizika.typPojistneni }}<br>
											{{ limit.castkaLimitRizika|number_format(0, ',', '.') ~ ' Kč' }}
											/
											{{ limit.limitRizikaNa }}<br>
										{% endfor %}
									</div>
								</div>
							</div>
						{% endif %}


						<!-- Pojisteno domacnost -->
						{% if show_domacnost_section %}
							<div class="border border-gray-200 rounded-lg p-4 mt-6">
								<div class="flex items-center">
									<div class="font-bold text-gray-700 dark:text-gray-300 w-1/2">Pojištěno</div>
									<div class="w-1/2">Domácnost</div>
								</div>
							</div>
							<div class="border border-gray-200 rounded-lg p-4">
								<div class="flex">
									<div>
										{% for limit in limity_domacnost %}
											{{ limit.zRizika.typPojistneni }}<br>
											<ul class="list-disc ml-4">
												<li class="ml-4">{{ limit.castkaLimitRizika|number_format(0, ',', '.') ~ ' Kč' }}
													/
													{{ limit.limitRizikaNa }}
												</li>
											</ul>
											<br>
										{% endfor %}
									</div>
								</div>
							</div>
						{% endif %}
					</div>
				</div>
			</div>
		</div>
	</section>
{% endblock %}
