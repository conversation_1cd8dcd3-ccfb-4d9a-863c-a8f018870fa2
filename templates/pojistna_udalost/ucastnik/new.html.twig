{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block title %}
	InIn - P<PERSON><PERSON><PERSON>
{% endblock %}

{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full min-h-screen" {{ stimulus_controller('ucastnik--ucastnik-form') }}>
		<div class="py-8 px-4 mx-auto max-w-2xl lg:py-16">
			<h2 class="mb-4 text-xl font-bold text-gray-900 dark:text-white">PU #{{ pojistnaUdalost.cisloPojistnaUdalost }}
				- Přidejte účastníka</h2>
			{{ form_start(form) }}

			<div class="form-field role-field">
				{{ form_row(form.roleUcastnika, { attr: { 'data-ucastnik--ucastnik-form-target': 'roleSelect', 'data-action': 'change->ucastnik--ucastnik-form#handleRoleChange' } }) }}
			</div>

			<div class="form-field is-company-field my-5" data-role="pojisteny poskozeny vinik poverena-osoba kontaktni-osoba">
				{{ form_row(form.isCompany, { label: 'Jedná se o firmu', attr: { 'data-ucastnik--ucastnik-form-target': 'isCompany', 'data-action': 'change->ucastnik--ucastnik-form#toggleCompanyFields' } }) }}
			</div>

			<div class="grid gap-4 sm:grid-cols-2 sm:gap-6 form-field jmeno-prijmeni-field" data-role="pojisteny poskozeny vinik poverena-osoba kontaktni-osoba" data-person="true">
				<div class="form-field prijmeni-field">
					{{ form_row(form.prijmeni, { label: 'Příjmení' }) }}
				</div>
				<div class="form-field jmeno-field">
					{{ form_row(form.jmeno, { label: 'Jméno' }) }}
				</div>
			</div>

			<div class="form-field adresa-field" data-role="pojisteny poskozeny vinik" data-person="true">
				{{ form_row(form.adresa, { label: 'Adresa' }) }}
			</div>

<div class="grid gap-4 sm:grid-cols-2 sm:gap-6">
	<div class="form-field firma-field" data-role="pojisteny poskozeny vinik poverena-osoba kontaktni-osoba" data-company="true">
					{{ form_row(form.firma) }}
				</div>
				<div class="form-field ico-field" data-role="pojisteny poskozeny vinik poverena-osoba kontaktni-osoba" data-company="true">
					{{ form_row(form.ico, { label: 'IČO' }) }}
				</div>
			</div>

			<div class="form-field sidlo-field" data-role="pojisteny poskozeny vinik poverena-osoba kontaktni-osoba" data-company="true">
				{{ form_row(form.sidlo, { label: 'Sídlo', attr: { class: 'grow' } }) }}
			</div>

			<div class="grid gap-4 sm:grid-cols-2 sm:gap-6 form-field kontakt-field" data-role="pojisteny poskozeny vinik poverena-osoba kontaktni-osoba">
				<div class="form-field telefon-field">
					{{ form_row(form.telefon) }}
				</div>
				<div class="form-field email-field" data-role="pojisteny poskozeny vinik poverena-osoba">
					{{ form_row(form.email) }}
				</div>
			</div>

			<div class="grid gap-4 sm:grid-cols-2 sm:gap-6 form-field ucet-rc-field" data-role="pojisteny poskozeny vinik">
				<div class="form-field rodne-cislo-field" data-person="true">
					{{ form_row(form.rodneCislo, { label: 'Rodné číslo' }) }}
				</div>
				<div class="form-field cislo-uctu-field">
					{{ form_row(form.cisloUctu, { label: 'Číslo účtu' }) }}
				</div>
			</div>

			<div class="form-field platce-dph-field my-6" data-role="pojisteny poskozeny vinik poverena-osoba kontaktni-osoba" data-company="true">
				{{ form_row(form.platceDPH, { label: 'Je plátce DPH' }) }}
			</div>

			<button type="submit" class="inline-flex items-center px-5 py-2.5 mt-4 sm:mt-6 text-sm font-medium text-center text-white bg-primary-700 rounded-lg focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-900 hover:bg-primary-800">Přidat účastníka</button>
			<a href="{{ path('app_pojistna_udalost_read', { id: pojistnaUdalost.id }) }}" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 my-4 sm:mt-6 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">Zpět na pojistnou událost</a>

			{{ form_end(form) }}
		</div>
	</section>
{% endblock %}
