{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block title %}
	InIn - Spr<PERSON>va uživatelů pojistné ud<PERSON>ti
{% endblock %}

{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full min-h-screen">
		<div class="py-8 px-4 mx-auto max-w-2xl lg:py-16">
			<h2 class="mb-4 text-xl font-bold text-gray-900 dark:text-white">Spr<PERSON>va uživatelů pojistné události</h2>
			<div class="mb-6">
				<p class="text-gray-700 dark:text-gray-300">
					<PERSON><PERSON>lo pojistné události: <span class="font-semibold">{{ pojistnaUdalost.cisloPojistnaUdalost }}</span>
				</p>
				{% if pojistnaUdalost.cisloSkodniUdalost %}
					<p class="text-gray-700 dark:text-gray-300">
						<PERSON><PERSON><PERSON> ud<PERSON>losti: <span class="font-semibold">{{ pojistnaUdalost.cisloSkodniUdalost }}</span>
					</p>
				{% endif %}
				<p class="text-gray-700 dark:text-gray-300">
					Kategorie: <span class="font-semibold">{{ pojistnaUdalost.kategorieFullString }}</span>
				</p>
			</div>
			
			{{ form_start(form, {'attr': {'class': 'space-y-4'}}) }}
				<div class="mb-6">
					<label for="{{ form.uzivatele.vars.id }}" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
						Vyberte uživatele, kteří mají přístup k této pojistné události
					</label>
					{{ form_widget(form.uzivatele, {
						'attr': {
							'class': 'bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500'
						}
					}) }}
					<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
						Vyberte jednoho nebo více uživatelů, kteří budou mít přístup k této pojistné události.
					</p>
				</div>
				
				<div class="flex items-center space-x-4">
					<button type="submit" class="inline-flex items-center px-5 py-2.5 text-sm font-medium text-center text-white bg-primary-700 rounded-lg focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-900 hover:bg-primary-800">
						Uložit změny
					</button>
					<a href="{{ path('app_pojistna_udalost_read', {'id': pojistnaUdalost.id}) }}" class="text-sm font-medium text-primary-600 dark:text-primary-500 hover:underline">
						Zpět na detail
					</a>
				</div>
			{{ form_end(form) }}
		</div>
	</section>
{% endblock %}

{% block javascripts %}
	{{ parent() }}
	<script>
		document.addEventListener('DOMContentLoaded', function() {
			// Initialize select2 for multiple select
			$('.select2-multiple').select2({
				placeholder: 'Vyberte uživatele...',
				allowClear: true,
				width: '100%'
			});
		});
	</script>
{% endblock %}
