{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block canvas_content %}
	<section class="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5 h-full min-h-screen">
		<div class="px-4 mx-auto max-w-screen-2xl lg:px-12">
			<div
				class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
				{# counter section #}

				<div class="px-4 divide-y dark:divide-gray-700">
					<div class="flex flex-col py-3 space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:space-x-4">
						<div class="flex items-center flex-1 space-x-4 justify-between">
							<h5>
								<span class="text-gray-500">Celkem událostí:</span>
								<span class="dark:text-white">{{ pagination.getTotalItemCount }}</span>
							</h5>
							{% if filterActive is not null %}
								<div class="self-end flex items-center">
									<p class="font-semibold mr-2">{{ filterActive }}</p>
									<div class="h-3/4 bg-green-500 text-white font-semibold text-sm py-1 px-2 rounded-md">
										<a href="{{ path('app_pojistna_udalost_list') }}">FILTR AKTIVNÍ
											<span class="font-bold">✕</span>
										</a>
									</div>
								</div>
							{% endif %}
						</div>
					</div>

					{# controls section #}

					<div class="flex flex-col items-stretch justify-between py-4 space-y-3 md:flex-row md:items-center md:space-y-0">

						{% if is_granted('ROLE_COM_ADMIN') %}
							<a href="{{ path('app_pojistna_udalost_new') }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
								<svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
									<path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
								</svg>Přidat novou pojistnou událost
							</a>
						{% endif %}

						<form class="w-full md:max-w-sm flex-1 md:mr-4" method="get" action="{{ path('app_pojistna_udalost_list') }}">
							<label for="default-search" class="text-sm font-medium text-gray-900 sr-only dark:text-white">Hledat</label>
							<div class="relative">
								<div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
									<svg aria-hidden="true" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
									</svg>
								</div>
								<input type="search" name="search" id="default-search" class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Hledat..." required=""/>
								<button type="submit" class="text-white absolute right-0 bottom-0 top-0 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-r-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">Hledat</button>
							</div>
						</form>

						<div>
							<button id="filterDropdownButton" data-dropdown-toggle="filterDropdown" type="button" class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
								<svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"/>
								</svg>Filtrovat<svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
									<path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
								</svg>
							</button>

							<!-- Dropdown menu -->
							<div id="filterDropdown" class="z-10 hidden p-3 bg-white rounded-lg shadow w-72 dark:bg-gray-700 overflow-visible">
								<div class="mb-4 border-b border-gray-200 dark:border-gray-600">
									<ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="myTab" data-tabs-toggle="#myTabContent" role="tablist">
									<li class="mr-1" role="presentation">
											<button class="inline-block pb-2 pr-1" id="ruzne-tab" data-tabs-target="#ruzne" type="button" role="tab" aria-controls="ruzne" aria-selected="false">Různé</button>
										</li>
										<li class="mr-1" role="presentation">
											<button class="inline-block pb-2 pr-1" id="date-tab" data-tabs-target="#date" type="button" role="tab" aria-controls="date" aria-selected="false">Datum</button>
										</li>
										<li class="mr-1" role="presentation">
											<button class="inline-block pb-2 pr-1" id="category-tab" data-tabs-target="#category" type="button" role="tab" aria-controls="category" aria-selected="false">Kategorie</button>
										</li>
									</ul>
								</div>

								<div id="myTabContent" data-controller="pojistnaudalost--filter">
									<div class="grid grid-cols-2 gap-4" id="date" role="tabpanel" aria-labelledby="date-tab">
										<div class="w-full col-span-2">
											<label for="date-type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Typ data</label>
											<select id="date-type" class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 sm:text-xs focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
													data-pojistnaudalost--filter-target="dateType">
												<option value="prijato">Přijato</option>
												<option value="ukonceno">Ukončeno</option>
											</select>
										</div>
										<div class="flex items-center justify-between col-span-2 space-x-3">
											<div class="w-full">
												<label for="min-experience-input" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Od</label>

												<input type="date" id="date-from" {% if dates.dateFrom %} value="{{ dates.dateFrom }}" {% endif %} min="1" max="10000" class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 sm:text-xs focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="" data-pojistnaudalost--filter-target="dateFrom"/>
											</div>

											<div class="w-full">
												<label for="date-to" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Do</label>

												<input type="date" id="max-experience-input" {% if dates.dateTo %} value="{{ dates.dateTo }}" {% endif %} min="1" max="10000" class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 sm:text-xs focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="" data-pojistnaudalost--filter-target="dateTo"/>
											</div>
										</div>
									</div>
									<div class="space-y-2" id="category" role="tabpanel" aria-labelledby="category-tab">
										<div class="flex items-center">
											<input id="worldwide" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" data-pojistnaudalost--filter-target="categoryCheckbox" category-id="MAJ" {% if 'MAJ' in categories %} checked {% endif %}/>

											<label for="worldwide" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Majetek</label>
										</div>
										<div class="flex items-center">
											<input id="america" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" data-pojistnaudalost--filter-target="categoryCheckbox" category-id="ODP" {% if 'ODP' in categories %} checked {% endif %}/>

											<label for="america" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Odpovědnost</label>
										</div>
										<div class="flex items-center">
											<input id="europe" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" data-pojistnaudalost--filter-target="categoryCheckbox" category-id="POV" {% if 'POV' in categories %} checked {% endif %}/>

											<label for="europe" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Auto - Pov</label>
										</div>
										<div class="flex items-center">
											<input id="asia" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" data-pojistnaudalost--filter-target="categoryCheckbox" category-id="HAV" {% if 'HAV' in categories %} checked {% endif %}/>

											<label for="asia" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Auto - Hav</label>
										</div>
									</div>
									<div id="ruzne" role="tabpanel" aria-labelledby="ruzne-tab">
										<h6 class="mt-4 mb-2 text-sm font-medium text-gray-900 dark:text-white">Podle stavu</h6>
										<select id="include" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" data-pojistnaudalost--filter-target="stav">
											<option value="" selected disabled>Vyber stav</option>
											{% for key, label in stavy %}
												<option value="{{ label }}" {% if misc.stav is defined and misc.stav == label %}selected{% endif %}>
													{{ key }}
												</option>
											{% endfor %}
										</select>
										<h6 class="mt-4 mb-2 text-sm font-medium text-gray-900 dark:text-white">Podle likvidátora</h6>
										<select id="include" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" data-pojistnaudalost--filter-target="likvidator">
											<option value="" selected disabled>Vyber likvidátora</option>
											{% for l in likvidatorList %}
												<option value="{{ l.id }}" {% if misc.likvidator and misc.likvidator == l.id %} selected {% endif %}>
													{{ l.name }}
													{{ l.surname }}
												</option>
											{% endfor %}
										</select>
										<h6 class="mt-4 mb-2 text-sm font-medium text-gray-900 dark:text-white">Podle technika</h6>
										<select id="include" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" data-pojistnaudalost--filter-target="technik">
											<option value="" selected disabled>Vyber technika</option>
											{% for t in technikList %}
												<option value="{{ t.id }}" {% if misc.technik and misc.technik == t.id %} selected {% endif %}>
													{{ t.name }}
													{{ t.surname }}
												</option>
											{% endfor %}
										</select>
										<h6 class="mt-4 mb-2 text-sm font-medium text-gray-900 dark:text-white">Podle Zadavatele</h6>
										<select id="include" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" data-pojistnaudalost--filter-target="zadavatel">
											<option value="" selected disabled>Vyber zadavatele</option>
											{% for z in zadavatelList %}
												<option value="{{ z.id }}" {% if misc.zadavatel and misc.zadavatel == z.id %} selected {% endif %}>
													{{ z.nazevPojistovny }}
												</option>
											{% endfor %}
										</select>
									</div>
									<button type="button" data-action="pojistnaudalost--filter#applyFilters" class="mt-4 px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
										Filtrovat
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>

				{# table #}

				<div class="overflow-x-auto" data-controller="pojistnaudalost--columns">
					<table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
						<thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
							<tr>
								{% set currentDir = app.request.get('direction') %}
								{% set columnCount = 1 %} {# Start with 1 for the actions column #}

								{% if 'zadavatel' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">Zadavatel</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'cisloPu' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">
									<a href="{{ path('app_pojistna_udalost_list', app.request.query.all|merge({'sort': 'pou.cisloPojistnaUdalost', 'direction': currentDir == 'ASC' ? 'DESC' : 'ASC'})) }}">Číslo PU</a>
									</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'cisloSu' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">
									<a href="{{ path('app_pojistna_udalost_list', app.request.query.all|merge({'sort': 'pou.cisloSkodniUdalost', 'direction': currentDir == 'ASC' ? 'DESC' : 'ASC'})) }}">Číslo ŠU</a>
									</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'datumVzniku' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">
									<a href="{{ path('app_pojistna_udalost_list', app.request.query.all|merge({'sort': 'pou.datumVznikuSkody', 'direction': currentDir == 'ASC' ? 'DESC' : 'ASC'})) }}">Datum vzniku PU</a>
									</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'datumPrijeti' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">
									<a href="{{ path('app_pojistna_udalost_list', app.request.query.all|merge({'sort': 'pou.datumPrijetiOdPojistovny', 'direction': currentDir == 'ASC' ? 'DESC' : 'ASC'})) }}">Datum přijetí PU</a>
									</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'mistoPu' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">
									<a href="{{ path('app_pojistna_udalost_list', app.request.query.all|merge({'sort': 'pou.mistoPu', 'direction': currentDir == 'ASC' ? 'DESC' : 'ASC'})) }}">Místo vzniku PU</a>
									</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'pojisteny' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">Pojištěný</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'poskozeny' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">Poškozený</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'email' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">Email</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'telefon' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">Telefon</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'kategorie' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">Kategorie</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'likvidator' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">Likvidátor</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								{% if 'stav' in app.user.puShownCols %}
									<th scope="col" class="px-4 py-3">Stav PU</th>
									{% set columnCount = columnCount + 1 %}
								{% endif %}
								<th scope="col" class="px-4 py-3">
									<button id="dropdown-button-settings" type="button" data-dropdown-toggle="dropdown-settings" class="inline-flex items-center p-1 text-sm font-medium text-center text-gray-500 rounded-lg hover:text-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none dark:text-gray-400 dark:hover:text-gray-100">
										<svg transform="rotate(90)" class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
											<path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"/>
										</svg>
									</button>
									<div id="dropdown-settings" class="z-10 hidden bg-white divide-y divide-gray-100 rounded shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
										<ul class="py-2 normal-case px-3 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdown-button-0">
											<li class="flex flex-col gap-y-2">
												<div class="flex justify-between items-center">
													<p class="block hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Zobrazit sloupce</p>
													<a href="{{ path('app_user_reset_pu_cols') }}" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 rounded-md font-semibold text-sm px-[0.25rem] py-[0.125rem] dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">Obnovit</a>
												</div>
												<hr>
												{% for col in columns %}
													<div class="flex items-center gap-x-2 px-2 font-semibold">
														<input type="checkbox" data-pojistnaudalost--columns-target="colCheckbox" data-col="{{ col.value }}" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"
														{% if col.value in app.user.puShownCols %} checked {% endif %}
														/>
														<p>{{ col.label }}</p>
													</div>
												{% endfor %}
												<div class="w-full flex items-center justify-end px-[0.125rem]">
													<button data-url="{{ url('app_user_set_pu_cols', {columns: 'REPLACE_COLUMNS'}) }}" data-pojistnaudalost--columns-target="submitButton" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 rounded-md font-semibold text-sm px-[0.25rem] py-[0.125rem] dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" data-action="pojistnaudalost--columns#submit">Potvrdit</button>
												</div>
											</li>
										</ul>
									</div>
								</th>
							</tr>
						</thead>
						<tbody>

							{% for one_pou in pagination %}
								<tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
									{% if 'zadavatel' in app.user.puShownCols %}
										<td class="px-4 py-2">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}" class="whitespace-nowrap">{{ one_pou.zadavatel.nazevPojistovny }}</a>
										</td>
									{% endif %}
									{% if 'cisloPu' in app.user.puShownCols %}
										<td data-controller="pojistnaudalost--pou-quick-notes"
											data-action="click->pojistnaudalost--pou-quick-notes#toggle" scope="row" class="cursor-pointer px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
											<p>{{ one_pou.cisloPojistnaUdalost }}</p>
										</td>
									{% endif %}
									{% if 'cisloSu' in app.user.puShownCols %}
										<td class="px-4 py-2 whitespace-nowrap">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ one_pou.cisloSkodniUdalost }}</a>
										</td>
									{% endif %}
									{% if 'datumVzniku' in app.user.puShownCols %}
										<td class="px-4 py-2">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ one_pou.getDatumVzniku()|date('d.m.Y') }}</a>
										</td>
									{% endif %}
									{% if 'datumPrijeti' in app.user.puShownCols %}
										<td class="px-4 py-2">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ one_pou.getDatumPrijeti()|date('d.m.Y') }}</a>
										</td>
									{% endif %}
									{% if 'mistoPu' in app.user.puShownCols %}
										<td class="px-4 py-2">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ one_pou.mistoPu }}</a>
										</td>
									{% endif %}


									{% set pojistenyUcastnik = one_pou.getPojistenyParticipant() %}
									{% set poskozenyUcastnik = one_pou.getPoskozenyUcastnik() %}

									{% if 'pojisteny' in app.user.puShownCols %}
										<td class="px-4 py-2 whitespace-nowrap">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ pojistenyUcastnik.getFullName()| default ('---') }}</a>
										</td>
									{% endif %}
									{% if 'poskozeny' in app.user.puShownCols %}
										<td class="px-4 py-2 whitespace-nowrap">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ one_pou.poskozenyUcastnik ? one_pou.poskozenyUcastnik.getFullName() : '---' }}</a>
										</td>
									{% endif %}	
									{% if 'email' in app.user.puShownCols %}
										<td class="px-4 py-2 whitespace-nowrap">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ pojistenyUcastnik.email| default ('---') }}</a>
										</td>
									{% endif %}
									{% if 'telefon' in app.user.puShownCols %}
										<td class="px-4 py-2 whitespace-nowrap">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ pojistenyUcastnik.telefon| default ('---') }}</a>
										</td>
									{% endif %}
									{% if 'kategorie' in app.user.puShownCols %}
										<td class="px-4 py-2"> 
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ one_pou.kategorie }}</a>
										</td>
									{% endif %}
									{% if 'likvidator' in app.user.puShownCols %}
										<td class="px-4 py-2 whitespace-nowrap">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ one_pou.likvidator ? one_pou.likvidator.fullName : '---' }}</a>
										</td>
									{% endif %}
									{% if 'stav' in app.user.puShownCols %}
										<td class="px-4 py-2">
											<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}">{{ one_pou.getStavLikvidaceLabel() }}</a>
										</td>
									{% endif %}

									<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
										<button id="dropdown-button-{{ one_pou.id }}" type="button" data-dropdown-toggle="dropdown-{{ one_pou.id }}" class="inline-flex items-center p-1 text-sm font-medium text-center text-gray-500 rounded-lg hover:text-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none dark:text-gray-400 dark:hover:text-gray-100">
											<svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
												<path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"/>
											</svg>
										</button>
										<div id="dropdown-{{ one_pou.id }}" class="z-10 hidden bg-white divide-y divide-gray-100 rounded shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
											<ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdown-button-0">
												<li>
													<a href="{{ path('app_pojistna_udalost_read', { id: one_pou.id }) }}" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Detail</a>
												</li>
												<li>
													<a href="{{ path('app_pojistna_udalost_edit', { id: one_pou.id }) }}" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Editace</a>
												</li>
												<li>
													<a href="{{ path('app_pojistna_udalost_ucastnik_new', { id: one_pou.id }) }}" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Přidat účastníka</a>
												</li>
											</ul>
											<div class="py-1">
												<a href="{{ path('app_poznamka_new', { id: one_pou.id }) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">Přidat poznámku</a>
											</div>
										</div>
									</td>
								</tr>
								<tr class="pou-quick-notes-row hidden border-b">
									<td colspan="{{ columnCount + 1 > 8 ? 8 : columnCount + 1 }}">
										<div class="flex flex-col space-y-3 bg-white dark:bg-gray-800 dark:border-gray-700 px-4 py-2">
											{% if not one_pou.uzavreno %}
												<div class="flex flex-col items-stretch justify-between py-2 space-y-3 md:flex-row md:items-center md:space-y-0">
													<a href="{{ path('app_poznamka_new', { id: one_pou.id }) }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
														<svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
															<path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
														</svg>Přidat novou poznámku
													</a>
												</div>
											{% endif %}

											{% include 'poznamka/table/_poznamka_table.html.twig' with {
												'poznamky': one_pou.poznamky,
												'title': 'Poznámky',
												'row_classes': 'max-w-md',
												'empty_message': 'Žádné poznámky k zobrazení',
												'pou': one_pou
											} %}
										</div>
									</td>
								</tr>
							{% endfor %}

						</tbody>
					</table>
				</div>

				{# navigation below table #}
				<nav class="flex flex-col items-start justify-between p-4 space-y-3 md:flex-row md:items-center md:space-y-0" aria-label="Table navigation">
					<div class="flex items-center space-x-3">
						<label for="rows" class="text-xs font-normal text-gray-500 dark:text-gray-400">Rows per page</label>
						<select id="rows" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block py-1.5 pl-3.5 pr-6 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
							<option value="25">25</option>
							<option selected="" value="50">50</option>
							<option value="100">100</option>
						</select>
						<div class="text-xs font-normal text-gray-500 dark:text-gray-400">
							<span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getPaginationData.firstItemNumber }}
								-
								{{ pagination.getPaginationData.lastItemNumber }}</span>
							of
							<span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getTotalItemCount }}</span>
						</div>
					</div>
					{# display navigation #}
					<div class="navigation">{{ knp_pagination_render(pagination) }}</div>
				</nav>
			</div>
		</div>
	</section>
{% endblock %}
