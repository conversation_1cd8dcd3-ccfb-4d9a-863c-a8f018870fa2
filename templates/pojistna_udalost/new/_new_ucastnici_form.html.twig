<h3 class="font-bold "><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h3>
<div class="my-5">
	{{ form_row(form.pojisteny_isCompany, { 
		label: '<PERSON>jištěný je právnick<PERSON> osoba',
		attr: { 
			'data-pojistnaudalost--new-target': 'pojistenyIsCompany',
			'data-action': 'change->pojistnaudalost--new#togglePojistenyCompanyFields'
		}
	}) }}
</div>

<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6" data-pojisteny-person="true">
	{{ form_row(form.pojisteny_prijmeni, { label: 'Příjmení' }) }}
	{{ form_row(form.pojisteny_jmeno, { label: 'J<PERSON><PERSON>' }) }}
</div>

<div class="form-field" data-pojisteny-person="true" data-pojisteny-person="true">
	{{ form_row(form.pojisteny_adresa, { label: '<PERSON><PERSON><PERSON>' }) }}
</div>

<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6" data-pojisteny-company="true">
	{{ form_row(form.pojisteny_firma, { label: 'Firma' }) }}
	{{ form_row(form.pojisteny_ico, { label: 'IČO' }) }}
</div>

<div class="form-field" data-pojisteny-company="true">
	{{ form_row(form.pojisteny_sidlo, { label: 'Sídlo', attr: { class: 'grow' } }) }}
</div>

<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6" data-pojisteny-person="true" data-pojisteny-company="true">
	{{ form_row(form.pojisteny_telefon, { label: 'Telefon' }) }}
	{{ form_row(form.pojisteny_email, { label: 'Email' }) }}
</div>

<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6">
	<div data-pojisteny-person="true">
		{{ form_row(form.pojisteny_rodneCislo, { label: 'Rodné číslo' }) }}
	</div>
	<div data-pojisteny-person="true" data-pojisteny-company="true">
		{{ form_row(form.pojisteny_cisloUctu, { label: 'Číslo účtu' }) }}
	</div>
</div>

<div class="form-field" data-pojisteny-company="true" data-pojisteny-person="true">
	{{ form_row(form.pojisteny_platceDPH, { label: 'Je plátce DPH' }) }}
</div>

<div class="my-10">
	<div class="my-5" data-pojistnaudalost--new-target="checkboxPoskozenyWithLabel">
		{{ form_row(form.poskozeny_different_from_pojisteny, { 
		label: 'Poškozený odlišný od pojištěného',
		attr: { 
			'data-pojistnaudalost--new-target': 'checkboxPoskozeny',
			'data-action': 'change->pojistnaudalost--new#toggleSectionPoskozeny'
		}
	}) }}
	</div>
	<div class="my-5">
		{{ form_row(form.poverena_osoba_different_from_pojisteny, { 
		label: 'Pověřená osoba odlišná od pojištěného',
		attr: { 
			'data-pojistnaudalost--new-target': 'checkboxPoverenaOsoba',
			'data-action': 'change->pojistnaudalost--new#toggleSectionPoverenaOsoba'
		}
	}) }}
	</div>
	<div class="my-5">
		{{ form_row(form.kontaktni_osoba_different_from_pojisteny, { 
		label: 'Kontaktní osoba odlišná od pojištěného',
		attr: { 
			'data-pojistnaudalost--new-target': 'checkboxKontaktniOsoba',
			'data-action': 'change->pojistnaudalost--new#toggleSectionKontaktniOsoba'
		}
	}) }}
	</div>
</div>

<div class="my-5" data-pojistnaudalost--new-target="sectionPoskozeny">
	<h3 class="font-bold ">Poškozený</h3>
	<div class="my-5">
		{{ form_row(form.poskozeny_isCompany, { 
			label: 'Poškozený je právnická osoba',
			attr: { 
				'data-pojistnaudalost--new-target': 'poskozenyIsCompany',
				'data-action': 'change->pojistnaudalost--new#togglePoskozenyCompanyFields'
			}
		}) }}
	</div>

	<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6" data-poskozeny-person="true">
		{{ form_row(form.poskozeny_prijmeni, { label: 'Příjmení' }) }}
		{{ form_row(form.poskozeny_jmeno, { label: 'Jméno' }) }}
	</div>

	<div class="form-field" data-poskozeny-person="true" data-poskozeny-person="true">
		{{ form_row(form.poskozeny_adresa, { label: 'Adresa' }) }}
	</div>

	<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6" data-poskozeny-company="true">
		{{ form_row(form.poskozeny_firma, { label: 'Firma' }) }}
		{{ form_row(form.poskozeny_ico, { label: 'IČO' }) }}
	</div>

	<div class="form-field" data-poskozeny-company="true">
		{{ form_row(form.poskozeny_sidlo, { label: 'Sídlo', attr: { class: 'grow' } }) }}
	</div>

	<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6" data-poskozeny-person="true" data-poskozeny-company="true">
		{{ form_row(form.poskozeny_telefon, { label: 'Telefon' }) }}
		{{ form_row(form.poskozeny_email, { label: 'Email' }) }}
	</div>

	<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6">
		<div data-poskozeny-person="true">
			{{ form_row(form.poskozeny_rodneCislo, { label: 'Rodné číslo' }) }}
		</div>
		<div data-poskozeny-person="true" data-poskozeny-company="true">
			{{ form_row(form.poskozeny_cisloUctu, { label: 'Číslo účtu' }) }}
		</div>
	</div>

	<div class="form-field" data-poskozeny-company="true">
		{{ form_row(form.poskozeny_platceDPH, { label: 'Je plátce DPH' }) }}
	</div>
</div>


<div class="my-5" data-pojistnaudalost--new-target="sectionPoverenaOsoba">
	<h3 class="font-bold ">Pověřená osoba</h3>

	<div class="my-5">
		{{ form_row(form.poverena_osoba_isCompany, { 
			label: 'Pověřená osoba je právnická osoba',
			attr: { 
				'data-pojistnaudalost--new-target': 'poverenaOsobaIsCompany',
				'data-action': 'change->pojistnaudalost--new#togglePoverenaOsobaCompanyFields'
			}
		}) }}
	</div>

	<div class="grid gap-4 sm:grid-cols-2 sm:gap-6" data-poverenaosoba-person="true">
		{{ form_row(form.poverena_osoba_prijmeni, { label: 'Příjmení' }) }}
		{{ form_row(form.poverena_osoba_jmeno, { label: 'Jméno' }) }}
	</div>

	<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6" data-poverenaosoba-company="true">
		{{ form_row(form.poverena_osoba_firma, { label: 'Firma' }) }}
		{{ form_row(form.poverena_osoba_ico, { label: 'IČO' }) }}
	</div>

	<div class="grid gap-4 sm:grid-cols-2 sm:gap-6" data-poverenaosoba-person="true" data-poverenaosoba-company="true">
		{{ form_row(form.poverena_osoba_telefon,{ label: 'Telefon' }) }}
		{{ form_row(form.poverena_osoba_email, { label: 'Email' }) }}
	</div>

	<div class="form-field" data-poverenaosoba-company="true">
		{{ form_row(form.poverena_osoba_sidlo, { label: 'Sídlo', attr: { class: 'grow' } }) }}
	</div>
	<div class="form-field" data-poverenaosoba-company="true">
		{{ form_row(form.poverena_osoba_platceDPH, { label: 'Je plátce DPH' }) }}
	</div>
</div>

<div class="my-5" data-pojistnaudalost--new-target="sectionKontaktniOsoba">
	<h3 class="font-bold ">Kontaktní osoba pro prohlídku</h3>
	<div class="my-5">
		{{ form_row(form.kontaktni_osoba_isCompany, { 
			label: 'Kontaktní osoba je právnická osoba',
			attr: { 
				'data-pojistnaudalost--new-target': 'kontaktniOsobaIsCompany',
				'data-action': 'change->pojistnaudalost--new#toggleKontaktniOsobaCompanyFields'
			}
		}) }}
	</div>

	<div class="grid gap-4 sm:grid-cols-2 sm:gap-6" data-kontaktniosoba-person="true">
		{{ form_row(form.kontaktni_osoba_prijmeni, { label: 'Příjmení' }) }}
		{{ form_row(form.kontaktni_osoba_jmeno, { label: 'Jméno' }) }}
	</div>

	<div class="form-field grid gap-4 sm:grid-cols-2 sm:gap-6" data-kontaktniosoba-company="true">
		{{ form_row(form.kontaktni_osoba_firma, { label: 'Firma' }) }}
		{{ form_row(form.kontaktni_osoba_ico, { label: 'IČO' }) }}
	</div>


	<div class="grid gap-4 sm:grid-cols-2 sm:gap-6">
		{{ form_row(form.kontaktni_osoba_telefon,{ 
			label: 'Telefon' ,
			attr: { 
				'data-kontaktniosoba-person' : "true",
				'data-kontaktniosoba-company' : "true"
			}
		}) }}
		<span data-kontaktniosoba-company="true">
			{{ form_row(form.kontaktni_osoba_email, { 
			label: 'Email',
		}) }}
		</span>

	</div>

	<div class="form-field" data-kontaktniosoba-company="true">
		{{ form_row(form.kontaktni_osoba_sidlo, { label: 'Sídlo', attr: { class: 'grow' } }) }}
	</div>

	<div class="form-field" data-kontaktniosoba-company="true">
		{{ form_row(form.kontaktni_osoba_platceDPH, { label: 'Je plátce DPH' }) }}
	</div>
</div>
