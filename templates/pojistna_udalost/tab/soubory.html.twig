<div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800" id="soubory" role="tabpanel" aria-labelledby="soubory-tab">
	<div class="container mx-auto p-4">

		<div class="border border-gray-200 p-1.5 mb-4">
			<div class="flex flex-row justify-between">
				<h5 class="mb-4 text-2xl font-semibold tracking-tight text-gray-900 dark:text-white">Soubory od pojišťovny:</h5>
				<div>
					<a href=" {{ path('app_soubor_list_by_slozka', {slozkaId: pou.getSlozkaPojistovnaZadani().id}) }} " class="bg-blue-100 hover:bg-blue-200 text-blue-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-gray-700 dark:text-blue-400 border border-blue-400 inline-flex items-center justify-center">Souborů&nbsp
						<span data-slozka-type="pojistovna_zadani">{{ soubory_ve_slozkach[constant('App\\Entity\\Slozka::DRUH_SLOZKY_POJISTOVNA_ZADANI')] }}</span></a>
				</div>
			</div>

			{# První formulář - soubory od pojišťovny #}
			{% set uploadtype = constant('App\\Helper\\SouborUploadTypeHelper::TYPE_ODPOJISTOVNY') %}
			<div class="mb-1" {{ stimulus_controller('soubor--upload', { type: uploadtype, slozkaType: 'pojistovna_zadani' } ) }}>
				<div class="hidden" data-soubor--upload-target="odpojistovny" data-typestring={{ uploadtype }}></div>
				<form class="dropzone space-y-6" action="{{ path('app_soubor_upload', { id: pou.id, type: uploadtype }) }}" method="POST" enctype="multipart/form-data" data-soubor--upload-target="form" data-soubor--upload-type-value="{{ uploadtype }}">
					<div class="dz-message" data-dz-message>
						<span>Přetáhněte sem soubory od pojišťovny</span>
					</div>
				</form>
			</div>
		</div>
		<div
			class="grid grid-cols-1 md:grid-cols-2 gap-4">
			{# left col #}
			<div class="flex flex-col h-full">
				<div class="border border-gray-200 p-1.5 mb-4">
					<div class="flex flex-row justify-between">
						<h5 class="mb-4 text-2xl font-semibold tracking-tight text-gray-900 dark:text-white">Soubory od klienta:</h5>
						<div>
							<a href="{{ path('app_soubor_list_by_slozka', {slozkaId: pou.getSlozkaKlient().id}) }}" class="bg-blue-100 hover:bg-blue-200 text-blue-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-gray-700 dark:text-blue-400 border border-blue-400 inline-flex items-center justify-center">Souborů&nbsp 
								<span data-slozka-type="klient">{{ soubory_ve_slozkach[constant('App\\Entity\\Slozka::DRUH_SLOZKY_KLIENT')] }}</span></a>
						</div>
					</div>
					{# Druhý formulář - soubory od klienta #}
					{% set uploadtype = constant('App\\Helper\\SouborUploadTypeHelper::TYPE_ODKLIENTA') %}
					<div class="mb-1" {{ stimulus_controller('soubor--upload', { type: uploadtype, slozkaType: 'klient' } ) }}>
						<form class="dropzone space-y-6" action="{{ path('app_soubor_upload', { id: pou.id, type: uploadtype }) }}" method="POST" enctype="multipart/form-data" data-soubor--upload-target="form" data-soubor--upload-type-value="{{ uploadtype }}">
							<div class="dz-message" data-dz-message>
								<span>Přetáhněte sem soubory od klienta</span>
							</div>
						</form>
					</div>
				</div>
			</div>
			{# right col #}
			<div>
				<div class="flex flex-col h-full">
					<div class="border border-gray-200 p-1.5 mb-4">
						<div class="flex flex-row justify-between">
							<h5 class="mb-4 text-2xl font-semibold tracking-tight text-gray-900 dark:text-white">Soubory od technika:</h5>
							<div>
								<a href="{{ path('app_soubor_list_by_slozka', {slozkaId: pou.getSlozkaTechnik().id}) }}" class="bg-blue-100 hover:bg-blue-200 text-blue-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-gray-700 dark:text-blue-400 border border-blue-400 inline-flex items-center justify-center">Souborů&nbsp 
									<span data-slozka-type="technik">{{ soubory_ve_slozkach[constant('App\\Entity\\Slozka::DRUH_SLOZKY_TECHNIK')] }}</span></a>
							</div>
						</div>
						{# Druhý formulář - soubory od klienta #}
						{% set uploadtype = constant('App\\Helper\\SouborUploadTypeHelper::TYPE_ODTECHNIKA') %}
						<div class="mb-1" {{ stimulus_controller('soubor--upload', { type: uploadtype, slozkaType: 'technik' } ) }}>
							<form class="dropzone space-y-6" action="{{ path('app_soubor_upload', { id: pou.id, type: uploadtype }) }}" method="POST" enctype="multipart/form-data" data-soubor--upload-target="form" data-soubor--upload-type-value="{{ uploadtype }}">
								<div class="dz-message" data-dz-message>
									<span>Přetáhněte sem soubory od technika</span>
								</div>
							</form>
						</div>
					</div>

				</div>
			</div>
		</div>

		<div class="bg-yellow-50 border border-gray-200 p-1.5 mb-4 mt-10">
			<div class="flex flex-row justify-between">
				<h5 class="mb-4 text-2xl font-semibold tracking-tight text-gray-900 dark:text-white">Soubory pro pojišťovnu</h5>
				<div>
					<a href="{{ path('app_soubor_list_by_slozka', {slozkaId: pou.getSlozkaPojistovnaUkonceni().id}) }}" class="bg-blue-100 hover:bg-blue-200 text-blue-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-gray-700 dark:text-blue-400 border border-blue-400 inline-flex items-center justify-center">Souborů&nbsp 
						<span data-slozka-type="pojistovna_ukonceni">{{ soubory_ve_slozkach[constant('App\\Entity\\Slozka::DRUH_SLOZKY_POJISTOVNA_UKONCENI')] }}</span></a>
				</div>
			</div>


			{# První formulář - soubory od pojišťovny #}
			{% set uploadtype = constant('App\\Helper\\SouborUploadTypeHelper::TYPE_PROPOJISTOVNU') %}
			<div class="mb-1" {{ stimulus_controller('soubor--upload', { type: uploadtype, slozkaType: 'pojistovna_ukonceni' } ) }}>
				<div class="hidden" data-soubor--upload-target="odpojistovny" data-typestring={{ uploadtype }}></div>
				<form class="dropzone space-y-6" action="{{ path('app_soubor_upload', { id: pou.id, type: uploadtype }) }}" method="POST" enctype="multipart/form-data" data-soubor--upload-target="form" data-soubor--upload-type-value="{{ uploadtype }}">
					<div class="dz-message" data-dz-message>
						<span>Přetáhněte sem soubory pro pojišťovnu</span>
					</div>
				</form>
			</div>
		</div>
	</div>

</div>
<div>
