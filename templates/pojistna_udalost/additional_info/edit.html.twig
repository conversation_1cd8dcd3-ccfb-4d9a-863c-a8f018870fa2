{% extends 'page_templates/single_pu_dashboard.html.twig' %}
{% block title %}
	InIn - Editace doplňujících informací k pojistn<PERSON>
{% endblock %}

{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full min-h-screen">
		<div id="additiona_pu_info" class="py-8 px-4 mx-auto max-w-3xl lg:py-16">
			{{ include('pojistna_udalost/components/_pu_header_number.html.twig',{pou:pojistna_udalost}) }}
			<div class="flex justify-between mb-6 mt-8">
				<h2 class="text-xl font-semibold text-gray-900 dark:text-white">Editace doplňujících informací</h2>
				<a href="{{ path('app_pojistna_udalost_additional_pu_info', {'id': pojistna_udalost.id}) }}" class="text-white bg-gray-500 hover:bg-gray-600 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-600 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800">Zpět</a>
			</div>

			<div class="bg-white border border-gray-200 rounded-lg shadow-md dark:bg-gray-800 dark:border-gray-700 overflow-hidden">
				<div class="p-6" data-controller="pojistnaudalost--additional-info-form">
					{{ form_start(form, {'attr': {'data-pojistnaudalost--additional-info-form-target': 'form'}}) }}
					<div
						class="space-y-6">
						<!-- Popis vzniku -->
						<div>
							{{ form_label(form.popisVzniku, null, {'label_attr': {'class': 'text-lg font-bold text-gray-900 dark:text-white mb-2'}}) }}
							<div id="popisVznikuEditor" class="min-h-36 mb-2" data-pojistnaudalost--additional-info-form-target="popisVznikuEditor"></div>
							<div class="hidden">
								{{ form_widget(form.popisVzniku, {'attr': {'data-pojistnaudalost--additional-info-form-target': 'popisVznikuInput'}}) }}
							</div>
							{{ form_errors(form.popisVzniku) }}
						</div>

						<!-- Rozsah poškození -->
						<div>
							{{ form_label(form.rozsahPoskozeni, null, {'label_attr': {'class': 'text-lg font-bold text-gray-900 dark:text-white mb-2'}}) }}
							<div id="rozsahPoskozeniEditor" class="min-h-36 mb-2" data-pojistnaudalost--additional-info-form-target="rozsahPoskozeniEditor"></div>
							<div class="hidden">
								{{ form_widget(form.rozsahPoskozeni, {'attr': {'data-pojistnaudalost--additional-info-form-target': 'rozsahPoskozeniInput'}}) }}
							</div>
							{{ form_errors(form.rozsahPoskozeni) }}
						</div>

						<div
							class="flex flex-col md:flex-row gap-2">
							<!-- Způsob opravy -->
							<div>
								{{ form_label(form.zpusobOpravy, null, {'label_attr': {'class': 'text-lg font-bold text-gray-900 dark:text-white mb-2'}}) }}
								{{ form_widget(form.zpusobOpravy) }}
								{{ form_errors(form.zpusobOpravy) }}
							</div>
							<!-- rezerva -->
							<div>
								{{ form_label(form.castkaRezerva, null, {'label_attr': {'class': 'text-lg font-bold text-gray-900 dark:text-white mb-2'}}) }}
								{{ form_widget(form.castkaRezerva, {'attr': {'data-pojistnaudalost--additional-info-form-target': 'castkaRezerva'}}) }}
								{{ form_errors(form.castkaRezerva) }}
							</div>
						</div>


						<div
							class="flex flex-col md:flex-row gap-6">
							<!-- Regres -->
							<div class="min-w-40">
								{{ form_label(form.regres, null, {'label_attr': {'class': 'text-lg font-bold text-gray-900 dark:text-white mb-2'}}) }}
								{{ form_widget(form.regres, {'attr': {'data-pojistnaudalost--additional-info-form-target': 'regresSelect'}}) }}
								{{ form_errors(form.regres) }}
							</div>
							<div
								class="flex-1">
								<!-- Údaje o regresu -->
								<div id="regres_details" class="{% if not form.vars.data.regres %}hidden{% endif %}" data-pojistnaudalost--additional-info-form-target="regresDetails">
									<div class="text-lg font-bold text-gray-900 dark:text-white mb-2">
										Kontakt
									</div>
									<div class="flex flex-col md:flex-row gap-2">
										<div class="flex-1">
											{{ form_row(form.regresName) }}
										</div>
										<div class="flex-1">
											{{ form_row(form.regresTel) }}
										</div>
									</div>
									<div>
										{{ form_row(form.regresAdresa) }}
									</div>

								</div>

							</div>

						</div>


						<div class="flex justify-end mt-6">
							<button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Uložit</button>
						</div>
					</div>
					{{ form_end(form) }}
				</div>
			</div>
		</div>
	</section>
{% endblock %}
