{% extends 'page_templates/single_pu_dashboard.html.twig' %}
{% block title %}
	InIn - Doplňující informace k pojistn<PERSON>
{% endblock %}


{% block canvas_content %}
	<section class="bg-white dark:bg-gray-900 h-full min-h-screen">
		<div id="additiona_pu_info" class="py-8 px-4 mx-auto max-w-3xl lg:py-16">
			{{ include('pojistna_udalost/components/_pu_header_number.html.twig',{pou:pojistna_udalost}) }}
			<!-- Edit button -->
			<div class="flex justify-end mt-6">
				<div class="mb-6">
					<a href="{{ path('app_pojistna_udalost_additional_pu_info_edit', {'id': pojistna_udalost.id}) }}" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Editovat</a>
				</div>

			</div>
			<!-- Main card -->
			<div class="bg-white border border-gray-200 rounded-lg shadow-md dark:bg-gray-800 dark:border-gray-700 overflow-hidden">
				<div class="p-6">
					<h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Doplňující informace k pojistné události</h2>

					<div
						class="space-y-6">
						<!-- Popis vzniku -->
						<div class="border border-gray-200 rounded-lg p-4 dark:border-gray-700">
							<h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Popis vzniku</h3>
							<p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">
								{% if pojistna_udalost.popisVzniku %}
									<div data-controller="quill--view" class="prose max-w-none dark:prose-invert">
										<div data-quill--view-target="container" data-content={{ pojistna_udalost.popisVzniku|escape('html_attr') }}></div>
									</div>
									<div class="hidden">{{ pojistna_udalost.popisVzniku }}</div>
								{% else %}
									<p class="text-gray-500 dark:text-gray-400 italic">Neuvedeno</p>
								{% endif %}
							</p>
						</div>

						<!-- Rozsah poškození -->
						<div class="border border-gray-200 rounded-lg p-4 dark:border-gray-700">
							<h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Rozsah poškození</h3>
							<p class="text-gray-700 dark:text-gray-300 whitespace-pre-line">
								{% if pojistna_udalost.rozsahPoskozeni %}
									<div data-controller="quill--view" class="prose max-w-none dark:prose-invert">
										<div data-quill--view-target="container" data-content={{ pojistna_udalost.rozsahPoskozeni|escape('html_attr') }}></div>
									</div>
									<div class="hidden">{{ pojistna_udalost.rozsahPoskozeni }}</div>
								{% else %}
									<p class="text-gray-500 dark:text-gray-400 italic">Neuvedeno</p>
								{% endif %}
							</p>
						</div>

						<!-- Způsob opravy -->
						<div class="border border-gray-200 rounded-lg p-4 dark:border-gray-700">
							<div class="flex justify-between">

								<h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Způsob opravy</h3>


								<div class="text-gray-700 dark:text-gray-300">
									{% if pojistna_udalost.zpusobOpravy == 'rozpoctem' %}
										Rozpočet
									{% elseif pojistna_udalost.zpusobOpravy == 'faktura' %}
										Faktura
									{% else %}
										Neuvedeno
									{% endif %}
								</div>
							</div>
						</div>
						<!-- Rezerva (placeholder) -->
						<div class="border border-gray-200 rounded-lg p-4 dark:border-gray-700">
							<div class="flex justify-between">
								<h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Rezerva</h3>
								<div class="text-gray-700 dark:text-gray-300">
									{{ pojistna_udalost.castkaRezerva ? pojistna_udalost.castkaRezerva|number_format(0, ',', '.') ~ ' Kč' : 'Neuvedeno'}}
								</div>
							</div>
						</div>


						<!-- Regres -->
						<div class="border border-gray-200 rounded-lg p-4 dark:border-gray-700">
							<div class="flex justify-between">
								<h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Regres</h3>
								<div class="text-gray-700 dark:text-gray-300">
									{% if pojistna_udalost.regres is same as(true) %}
										ANO
									{% elseif pojistna_udalost.regres is same as(false) %}
										NE
									{% else %}
										Neuvedeno
									{% endif %}
								</div>
							</div>
						</div>


						<!-- Regres udaje -->
						{% if pojistna_udalost.regres is same as(true) %}
							<div class="border border-gray-200 rounded-lg p-4 dark:border-gray-700">
								<h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">Regres kontakt</h3>
								<div class="text-gray-700 dark:text-gray-300 leading-8">
									{{ pojistna_udalost.regresName | default ('není jméno') }},
									{{ pojistna_udalost.regresTel | default ('není telefon') }}
									<br>
									{{ pojistna_udalost.regresAdresa | default ('bez adresy') }}
								</div>
							</div>
						{% endif %}


					</div>
				</div>
			</div>
		</div>
	</section>
{% endblock %}
