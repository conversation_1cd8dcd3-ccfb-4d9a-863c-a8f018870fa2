{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block title %}Zprávy{% endblock %}

{% block canvas_content %}
  <section class="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5 h-screen">
    <div class="px-4 mx-auto max-w-screen-2xl lg:px-12">
      <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
        {# counter section #}

        <div class="px-4 divide-y dark:divide-gray-700">
          <div class="flex flex-col py-3 space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:space-x-4">
            <div class="flex items-center flex-1 space-x-4 justify-between">
              <h5>
                <span class="text-gray-500">Celkem zpráv:</span>
                <span class="dark:text-white">{{ pagination.getTotalItemCount }}</span>
              </h5>
              {% if filterActive is not null %}
                <div class="self-end flex items-center">
                  <p class="font-semibold mr-2">{{ filterActive }}</p>
                  <div class="h-3/4 bg-green-500 text-white font-semibold text-sm py-1 px-2 rounded-md">
                    <a href="{{ path('app_zprava_list') }}">FILTR AKTIVNÍ <span class="font-bold">✕</span></a>
                  </div>
                </div>
              {% endif %}
            </div>
          </div>

          {# controls section #}

          <div class="flex flex-col items-stretch justify-between py-4 space-y-3 md:flex-row md:items-center md:space-y-0">
            <form class="w-full md:max-w-sm flex-1 md:mr-4">
              <label for="default-search" class="text-sm font-medium text-gray-900 sr-only dark:text-white">Hledat</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <svg aria-hidden="true" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input type="search" id="default-search" class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Hledat..." required="" />
                <button type="submit" class="text-white absolute right-0 bottom-0 top-0 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-r-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">Hledat</button>
              </div>
            </form>

            <div>
              <button id="filterDropdownButton" data-dropdown-toggle="filterDropdown" type="button" class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" />
                </svg>Filtrovat<svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                </svg>
              </button>

              <!-- Dropdown menu -->
              <div id="filterDropdown" class="z-10 hidden p-3 bg-white rounded-lg shadow w-72 dark:bg-gray-700 overflow-visible">
                <div class="mb-4 border-b border-gray-200 dark:border-gray-600">
                  <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="myTab" data-tabs-toggle="#myTabContent" role="tablist">
                    <li class="mr-1" role="presentation">
                      <button class="inline-block pb-2 pr-1" id="date-tab" data-tabs-target="#date" type="button" role="tab" aria-controls="date" aria-selected="false">Datum</button>
                    </li>
                    <li class="mr-1" role="presentation">
                      <button class="inline-block pb-2 pr-1" id="type-tab" data-tabs-target="#type" type="button" role="tab" aria-controls="type" aria-selected="false">Typ</button>
                    </li>
                  </ul>
                </div>

                <div id="myTabContent">
                  <div class="grid grid-cols-2 gap-4" id="date" role="tabpanel" aria-labelledby="date-tab" data-controller="zprava--filtr--datum">
                    <div class="flex items-center justify-between col-span-2 space-x-3">
                      <div class="w-full">
                        <label for="min-experience-input" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Od</label>

                        <input type="date" id="date-from"
                          {% if dates.dateFrom %} value="{{ dates.dateFrom }}" {% endif %}
                          min="1" max="10000" class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 sm:text-xs focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder=""  data-zprava--filtr--datum-target="dateFrom"/>
                      </div>

                      <div class="w-full">
                        <label for="date-to" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Do</label>

                        <input type="date" id="max-experience-input"
                          {% if dates.dateTo %} value="{{ dates.dateTo }}" {% endif %}
                          min="1" max="10000" class="block w-full p-2 text-gray-900 border border-gray-300 rounded-lg bg-gray-50 sm:text-xs focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="" data-zprava--filtr--datum-target="dateTo" />
                      </div>
                    </div>
                    <div></div>
                    <div>
                      <a href="?" class="mt-[-0.5rem] float-right py-1 px-2 text-sm font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800" data-action="zprava--filtr--datum#reset">Resetovat</a>
                    </div>
                  </div>
                  <div class="space-y-2" id="type" role="tabpanel" aria-labelledby="type-tab" data-controller="zprava--filtr--typ">
                    <div class="flex items-center">
                      <input id="lik-assignment" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" data-zprava--filtr--typ-target="checkbox" type-id="LIK_ASSIGNMENT"
                      {% if 'LIK_ASSIGNMENT' in types %} checked {% endif %}
                      />

                      <label for="lik-assignment" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Přiřazení likvidátora</label>
                    </div>
                    <div class="flex items-center">
                      <input id="lik-revocation" type="checkbox" value="" class="w-4 h-4 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500" data-zprava--filtr--typ-target="checkbox" type-id="LIK_REVOCATION"
                      {% if 'LIK_REVOCATION' in types %} checked {% endif %}
                      />

                      <label for="lik-revocation" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Odebrání likvidátora</label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {# table #}

        <div class="overflow-x-auto">
          <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-4 py-3">
                      <svg width="34px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#6B7280" class="max-h-6"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M3 8L8.44992 11.6333C9.73295 12.4886 10.3745 12.9163 11.0678 13.0825C11.6806 13.2293 12.3194 13.2293 12.9322 13.0825C13.6255 12.9163 14.2671 12.4886 15.5501 11.6333L21 8M6.2 19H17.8C18.9201 19 19.4802 19 19.908 18.782C20.2843 18.5903 20.5903 18.2843 20.782 17.908C21 17.4802 21 16.9201 21 15.8V8.2C21 7.0799 21 6.51984 20.782 6.09202C20.5903 5.71569 20.2843 5.40973 19.908 5.21799C19.4802 5 18.9201 5 17.8 5H6.2C5.0799 5 4.51984 5 4.09202 5.21799C3.71569 5.40973 3.40973 5.71569 3.21799 6.09202C3 6.51984 3 7.07989 3 8.2V15.8C3 16.9201 3 17.4802 3.21799 17.908C3.40973 18.2843 3.71569 18.5903 4.09202 18.782C4.51984 19 5.07989 19 6.2 19Z" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>
                    </th>
                    <th scope="col" class="px-4 py-3">Titulek</th>
                    <th scope="col" class="px-4 py-3">Obsah</th>
                    <th scope="col" class="px-4 py-3">Typ</th>
                    <th scope="col" class="px-4 py-3">Datum vytvoření</th>
                    <th scope="col" class="px-4 py-3">Akce</th>
                </tr>
            </thead>
            <tbody>
                {% for notification in pagination %}
                    <tr data-controller="zprava--menu-ajax" class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 [&>td]:px-4 [&>td]:py-2">
                        <td class="[&>svg]:ml-1 [&>svg]:cursor-pointer" data-zprava--menu-ajax-target="readIcon">
                          {% if notification.precteno %}
                            <svg width="26px" height="26px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#6B7280"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M3.02832 10L10.2246 14.8166C10.8661 15.2443 11.1869 15.4581 11.5336 15.5412C11.8399 15.6146 12.1593 15.6146 12.4657 15.5412C12.8124 15.4581 13.1332 15.2443 13.7747 14.8166L20.971 10M10.2981 4.06879L4.49814 7.71127C3.95121 8.05474 3.67775 8.22648 3.4794 8.45864C3.30385 8.66412 3.17176 8.90305 3.09111 9.161C3 9.45244 3 9.77535 3 10.4212V16.8C3 17.9201 3 18.4802 3.21799 18.908C3.40973 19.2843 3.71569 19.5903 4.09202 19.782C4.51984 20 5.07989 20 6.2 20H17.8C18.9201 20 19.4802 20 19.908 19.782C20.2843 19.5903 20.5903 19.2843 20.782 18.908C21 18.4802 21 17.9201 21 16.8V10.4212C21 9.77535 21 9.45244 20.9089 9.161C20.8282 8.90305 20.6962 8.66412 20.5206 8.45864C20.3223 8.22648 20.0488 8.05474 19.5019 7.71127L13.7019 4.06879C13.0846 3.68116 12.776 3.48735 12.4449 3.4118C12.152 3.34499 11.848 3.34499 11.5551 3.4118C11.224 3.48735 10.9154 3.68116 10.2981 4.06879Z" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>
                          {% else %}
                            <svg width="27px" height="27px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M19 15V18M19 21H19.01M15 19H6.2C5.0799 19 4.51984 19 4.09202 18.782C3.71569 18.5903 3.40973 18.2843 3.21799 17.908C3 17.4802 3 16.9201 3 15.8V8.2C3 7.0799 3 6.51984 3.21799 6.09202C3.40973 5.71569 3.71569 5.40973 4.09202 5.21799C4.51984 5 5.0799 5 6.2 5H17.8C18.9201 5 19.4802 5 19.908 5.21799C20.2843 5.40973 20.5903 5.71569 20.782 6.09202C21 6.51984 21 7.0799 21 8.2V11.5M20.6067 8.26229L15.5499 11.6335C14.2669 12.4888 13.6254 12.9165 12.932 13.0827C12.3192 13.2295 11.6804 13.2295 11.0677 13.0827C10.3743 12.9165 9.73279 12.4888 8.44975 11.6335L3.14746 8.09863" stroke="#000000" stroke-width="2.4" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>
                          {% endif %}
                        </td>
                        <td data-zprava--menu-ajax-target="title" class="{% if not notification.precteno %}font-bold{% endif %} text-[1.05rem] text-black">
                            <a href="{{ path('app_zprava_read', {id: notification.id}) }}">{{ notification.titulek }}</a>
                        </td>
                        <td class="truncate max-w-48">
                            <p>{{ notification.obsah|striptags }}</p>
                        </td>
                        <td>
                            <p>{{ notification.typString }}</p>
                        </td>
                        <td>
                            <p>{{ notification.datumVytvoreni|date('d.m.Y H:i') }}</p>
                        </td>
                        <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            <button id="dropdown-button-{{ notification.id }}" type="button" data-dropdown-toggle="dropdown-{{ notification.id }}" class="inline-flex items-center p-1 text-sm font-medium text-center text-gray-500 rounded-lg hover:text-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none dark:text-gray-400 dark:hover:text-gray-100">
                                <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
                                </svg>
                            </button>
                            <div id="dropdown-{{ notification.id }}" class="z-10 hidden bg-white divide-y divide-gray-100 rounded shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
                                <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdown-button-0">
                                    <li>
                                        <a href="{{ path('app_zprava_toggle_read', {'id': notification.id }) }}" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" data-zprava--menu-ajax-target="toggleReadButton">Označit ne/přečtené</a>
                                    </li>
                                    <div class="px-2"><hr></div>
                                    <li>
                                        <a href="{{ path('app_zprava_read', {id: notification.id}) }}" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Detail</a>
                                    </li>
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Editace</a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
          </table>
        </div>

        {# navigation below table #}
        <nav class="flex flex-col items-start justify-between p-4 space-y-3 md:flex-row md:items-center md:space-y-0" aria-label="Table navigation">
          <div class="flex items-center space-x-3">
            <label for="rows" class="text-xs font-normal text-gray-500 dark:text-gray-400">Rows per page</label><select id="rows" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block py-1.5 pl-3.5 pr-6 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
              <option value="25">25</option>
              <option value="50">50</option>
              <option selected value="100">100</option>
            </select>
            <div class="text-xs font-normal text-gray-500 dark:text-gray-400">
              <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getPaginationData.firstItemNumber }} - {{ pagination.getPaginationData.lastItemNumber }}</span>
              of
              <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getTotalItemCount }}</span>
            </div>
          </div>
          {# display navigation #}
          <div class="navigation">{{ knp_pagination_render(pagination) }}</div>
        </nav>
      </div>
    </div>
  </section>
{% endblock %}
