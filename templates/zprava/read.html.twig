{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block title %}{{ notification.titulek }}{% endblock %}

{% block canvas_content %}
  <section class="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5 min-h-[calc(100vh-8.25rem)]">
    <div class="px-4 mx-auto max-w-screen-2xl lg:px-12">
      <div class="relative bg-white shadow-md dark:bg-gray-800 rounded-md sm:rounded-lg">

        <div class="px-4 divide-y dark:divide-gray-700">
          <div class="flex flex-col py-3 space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:space-x-4">
            <div class="flex gap-x- items-center flex-1 justify-between py-1 px-4">
              <div class="flex flex-wrap items-center gap-x-2">
                <a href="{{ path('app_zprava_list') }}" class="flex items-center justify-center px-2 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"><<</a>
                <div class="flex items-end gap-x-4">
                  <h1 class="font-bold text-3xl">{{ notification.titulek }}</h1>
                  <p class="text-gray-500 text-xl mb-[0.125rem]">{{ notification.typString }}</p>
                </div>
              </div>
              <div class="flex self-end">
                <div class="text-right text-xs text-gray-500 dark:text-gray-400" data-controller="zprava--detail-ajax">
                    <p>{{ notification.datumVytvoreni|date('d.m.Y H:i') }}</p>
                    <div class="flex items-center">
                      <a href="{{ path('app_zprava_toggle_read', {'id': notification.id }) }}" data-zprava--detail-ajax-target="readIcon" class="mr-1">
                          <svg width="22px" height="22px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#6B7280"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M3.02832 10L10.2246 14.8166C10.8661 15.2443 11.1869 15.4581 11.5336 15.5412C11.8399 15.6146 12.1593 15.6146 12.4657 15.5412C12.8124 15.4581 13.1332 15.2443 13.7747 14.8166L20.971 10M10.2981 4.06879L4.49814 7.71127C3.95121 8.05474 3.67775 8.22648 3.4794 8.45864C3.30385 8.66412 3.17176 8.90305 3.09111 9.161C3 9.45244 3 9.77535 3 10.4212V16.8C3 17.9201 3 18.4802 3.21799 18.908C3.40973 19.2843 3.71569 19.5903 4.09202 19.782C4.51984 20 5.07989 20 6.2 20H17.8C18.9201 20 19.4802 20 19.908 19.782C20.2843 19.5903 20.5903 19.2843 20.782 18.908C21 18.4802 21 17.9201 21 16.8V10.4212C21 9.77535 21 9.45244 20.9089 9.161C20.8282 8.90305 20.6962 8.66412 20.5206 8.45864C20.3223 8.22648 20.0488 8.05474 19.5019 7.71127L13.7019 4.06879C13.0846 3.68116 12.776 3.48735 12.4449 3.4118C12.152 3.34499 11.848 3.34499 11.5551 3.4118C11.224 3.48735 10.9154 3.68116 10.2981 4.06879Z" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>
                      </a>
                      <a href="{{ path('app_zprava_toggle_read', {'id': notification.id }) }}" data-zprava--detail-ajax-target="toggleReadButton" class="flex items-center">
                          Označit ne/přečtené
                      </a>
                    </div>
                </div>
              <div>
            </div>

          </div>
        </div>        
      </div>
      <hr>
      <div class="px-6 lg:px-8 zprava-window">
        {{ notification.obsah|raw }}
      </div>
    </div>
  </section>
{% endblock %}
