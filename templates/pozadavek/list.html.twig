{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block canvas_content %}
    <section class="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5 h-screen">
        <div class="px-4 mx-auto max-w-screen-2xl lg:px-12">
            <div class="relative bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
                {# counter section #}

                <div class="px-4 divide-y dark:divide-gray-700">
                    <div class="flex flex-col py-3 space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:space-x-4">
                        <div class="flex items-center flex-1 space-x-4">
                            <h5>
                                <span class="text-gray-500">Celk<PERSON> požadavků:</span>
                                <span class="dark:text-white">{{ pagination.getTotalItemCount }}</span>
                            </h5>
                        </div>
                    </div>

                    {# controls section #}

                    <div class="flex flex-col items-stretch justify-between py-4 space-y-3 md:flex-row md:items-center md:space-y-0">
                        <a href="{{ path('app_pozadavek_new') }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                            <svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                <path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" />
                            </svg>Přidat nový požadavek
                        </a>

                    </div>
                </div>

                {# table #}

                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Id požadavku</th>
                            <th scope="col" class="px-4 py-3">Typ požadavku</th>
                            <th scope="col" class="px-4 py-3">Akce</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for pozadavek in pagination %}
                            <tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <td class="px-4 py-2">{{ pozadavek.id }}</td>
                                <td scope="row" class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">{{ pozadavek.typPozadavku }}</td>

                                <td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    <button id="dropdown-button-{{ pozadavek.id }}" type="button" data-dropdown-toggle="dropdown-{{ pozadavek.id }}" class="inline-flex items-center p-1 text-sm font-medium text-center text-gray-500 rounded-lg hover:text-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none dark:text-gray-400 dark:hover:text-gray-100">
                                        <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
                                        </svg>
                                    </button>

                                    <div id="dropdown-{{ pozadavek.id }}" class="z-10 hidden bg-white divide-y divide-gray-100 rounded shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
                                        <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdown-button-0">
                                            <li>
                                                <a href="{{ path ('app_pozadavek_edit',{ id: pozadavek.id }) }}" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Editace</a>
                                            </li>
                                            <li>
                                                <a data-modal-target="popup-delete-{{ pozadavek.id }}" data-modal-toggle="popup-delete-{{ pozadavek.id }}" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Smazat</a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>

                    {% for pozadavek in pagination %}
                        <div id="popup-delete-{{ pozadavek.id }}" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                            <div class="relative p-4 w-full max-w-[75%] max-h-[75%]">
                                <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                                    <button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="popup-delete-{{ pozadavek.id }}">
                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                        </svg>
                                        <span class="sr-only">Zavřít</span>
                                    </button>
                                    <div class="p-4 space-y-4">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Opravdu chcete smazat tuto položku?</h3>
                                        <div class="flex justify-end space-x-3">
                                            <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-primary-700 rounded-lg focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-900 hover:bg-primary-800" data-modal-hide="popup-delete-{{ pozadavek.id }}">Zrušit</button>
                                            <a href="{{ path ('app_pozadavek_delete',{ id: pozadavek.id }) }}" class="px-4 py-2 text-sm font-medium text-white bg-red-700 rounded-lg focus:ring-4 focus:ring-red-200 dark:focus:ring-red-900 hover:bg-red-800">Smazat</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                {# navigation below table #}
                <nav class="flex flex-col items-start justify-between p-4 space-y-3 md:flex-row md:items-center md:space-y-0" aria-label="Table navigation">
                    <div class="flex items-center space-x-3">
                        <label for="rows" class="text-xs font-normal text-gray-500 dark:text-gray-400">Rows per page</label><select id="rows" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block py-1.5 pl-3.5 pr-6 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="25">25</option>
                            <option selected="" value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <div class="text-xs font-normal text-gray-500 dark:text-gray-400">
                            <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getPaginationData.firstItemNumber }} - {{ pagination.getPaginationData.lastItemNumber }}</span>
                            z
                            <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getTotalItemCount }}</span>
                        </div>
                    </div>
                    {# display navigation #}
                    <div class="navigation">{{ knp_pagination_render(pagination) }}</div>
                </nav>
            </div>
        </div>
    </section>
{% endblock %}
