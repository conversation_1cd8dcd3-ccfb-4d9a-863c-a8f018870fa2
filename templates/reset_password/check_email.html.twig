{% extends 'base.html.twig' %}

{% block title %}
  Email pro resetování hesla odeslán
{% endblock %}

{% block body %}
  <section class="bg-gray-50 dark:bg-gray-900">
    <div class="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
      <a href="{{ path('app_dashboard') }}" class="flex flex-col items-center mb-6 text-2xl font-semibold text-gray-900 dark:text-white">
        <div>
          <img class="max-w-20 mr-2" src="{{ asset('images/inin-logo-minimized.jpg') }}" alt="logo" />
        </div>Insurance Inspection
      </a>
      <div class="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
        <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1 class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">Email pro resetování hesla odeslán</h1>
          <p>Pokud existuje uživatel s vaší email adresou, pak systém právě odeslal emailovou zprávu s instrukcemi, jak resetovat heslo.

            Tento odkaz expiruje za {{ resetToken.expirationMessageKey|trans(resetToken.expirationMessageData, 'ResetPasswordBundle') }}.</p>
          <p>
            Pokud neobdržíte daný email, zkontrolujte složku nevyžádané pošty nebo <a href="{{ path('app_forgot_password_request') }}">vygenerujte novou žádost o změnu hesla</a>.
          </p>
        </div>
      </div>
    </div>
  </section>
{% endblock %}
