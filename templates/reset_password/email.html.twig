<h1><PERSON><PERSON><PERSON><PERSON> den</h1>

<p><PERSON><PERSON><PERSON><PERSON><PERSON> jsme požadavek na resetování hesla do aplikace InIn. Pokud jste tento požadavek nezadávali, nemusíte si tohoto emailu všímat.</p>

<a href="{{ url('app_reset_password', {token: resetToken.token}) }}">{{ url('app_reset_password', {token: resetToken.token}) }}</a>

<p>Tento odkaz vyexpiruje za {{ resetToken.expirationMessageKey|trans(resetToken.expirationMessageData, 'ResetPasswordBundle') }}.</p>

<p><PERSON><PERSON><PERSON> tým Comfort Lividační, s.r.o.</p>
