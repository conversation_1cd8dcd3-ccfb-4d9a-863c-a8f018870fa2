{% extends 'base.html.twig' %}

{% block title %}Resetování hesla{% endblock %}

{% block body %}

  <section class="bg-gray-50 dark:bg-gray-900">
    <div class="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen lg:py-0">
      <a href="{{ path('app_dashboard') }}" class="flex flex-col items-center mb-6 text-2xl font-semibold text-gray-900 dark:text-white">
        <div>
          <img class="max-w-20 mr-2" src="{{ asset('images/inin-logo-minimized.jpg') }}" alt="logo" />
        </div>Insurance Inspection
      </a>
      <div class="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
        {% if app.user %}
          <div>
            <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
              <span class="font-medium">Info:</span>
              Už jste přihlášený {{ app.user.userIdentifier }},
              <a href="{{ path('app_logout') }}">Logout</a>
            </div>
          </div>
          <div class="mb-3"></div>
        {% endif %}

        <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1 class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">Resetování hesla</h1>
          {{ form_start(resetForm) }}
          <div>{{ form_row(resetForm.plainPassword) }}</div>
          <button type="submit" class="w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">Nastavit heslo</button>
          {{ form_end(resetForm) }}
        </div>
      </div>
    </div>
  </section>
{% endblock %}
