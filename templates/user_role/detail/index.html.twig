{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block title %}
  InIn - Detail uživatelské role
{% endblock %}

{% block canvas_content %}
  <section class="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5">
    <div class="px-4 mx-auto max-w-screen-xl lg:px-12">
      <div class="relative overflow-hidden bg-white shadow-md dark:bg-gray-800 sm:rounded-lg p-4">
        <div class="flex items-center justify-between pb-4 mb-4 border-b dark:border-gray-700">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Detail uživatelské role</h2>
          <a href="{{ path('app_user_role_list') }}" class="inline-flex items-center text-gray-500 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-3 py-1.5 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Zpět na seznam
          </a>
        </div>

        <div class="space-y-6">
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Základní informace</h3>
              
              <div class="space-y-3">
                <div>
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Název role</p>
                  <p class="text-base font-semibold text-gray-900 dark:text-white">{{ userRole.name }}</p>
                </div>
                
                <div>
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Popis</p>
                  <p class="text-base text-gray-900 dark:text-white">{{ userRole.description ?: 'Bez popisu' }}</p>
                </div>
                
                <div>
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</p>
                  <div class="flex items-center gap-x-2 mt-1">
                    {% if userRole.active %}
                      <div class="w-3 h-3 bg-green-500 border rounded-full"></div>
                      <p class="text-base text-gray-900 dark:text-white">Aktivní</p>
                    {% else %}
                      <div class="w-3 h-3 bg-red-500 border rounded-full"></div>
                      <p class="text-base text-gray-900 dark:text-white">Deaktivovaný</p>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
            
            <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Časové údaje</h3>
              
              <div class="space-y-3">
                <div>
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Vytvořeno</p>
                  <p class="text-base text-gray-900 dark:text-white">{{ userRole.createdAt|date('d.m.Y H:i') }}</p>
                </div>
                
                <div>
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Naposledy aktualizováno</p>
                  <p class="text-base text-gray-900 dark:text-white">{{ userRole.updatedAt|date('d.m.Y H:i') }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="flex items-center space-x-4 mt-6">
            <a href="{{ path('app_user_role_edit', {id: userRole.id}) }}" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
              <svg class="w-4 h-4 mr-1 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path>
                <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path>
              </svg>
              Editovat
            </a>
            
            <a href="{{ path('app_user_role_toggle_active', {id: userRole.id}) }}" class="text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700">
              {% if userRole.active %}
                <svg class="w-4 h-4 mr-1 inline-block text-red-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                Deaktivovat
              {% else %}
                <svg class="w-4 h-4 mr-1 inline-block text-green-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Aktivovat
              {% endif %}
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
{% endblock %}
