{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block title %}
  InIn - Nov<PERSON> uživatelská role
{% endblock %}

{% block canvas_content %}
  <section class="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5">
    <div class="px-4 mx-auto max-w-screen-xl lg:px-12">
      <div class="relative overflow-hidden bg-white shadow-md dark:bg-gray-800 sm:rounded-lg p-4">
        <div class="flex items-center justify-between pb-4 mb-4 border-b dark:border-gray-700">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Nová uživatelská role</h2>
          <a href="{{ path('app_user_role_list') }}" class="inline-flex items-center text-gray-500 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-3 py-1.5 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Zpět na seznam
          </a>
        </div>

        {{ form_start(form, {'attr': {'class': 'space-y-4'}}) }}
          <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
            <div>
              {{ form_label(form.name) }}
              {{ form_widget(form.name) }}
              {{ form_errors(form.name) }}
            </div>
            <div class="lg:col-span-2">
              {{ form_label(form.description) }}
              {{ form_widget(form.description) }}
              {{ form_errors(form.description) }}
            </div>
            <div class="flex items-center">
              {{ form_widget(form.active) }}
              {{ form_label(form.active) }}
              {{ form_errors(form.active) }}
            </div>
          </div>

          <div class="flex items-center space-x-4 mt-6">
            <button type="submit" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
              Vytvořit roli
            </button>
            <a href="{{ path('app_user_role_list') }}" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
              Zrušit
            </a>
          </div>
        {{ form_end(form) }}
      </div>
    </div>
  </section>
{% endblock %}
