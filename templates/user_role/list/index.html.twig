{% extends 'page_templates/default_dashboard_template.html.twig' %}

{% block title %}
	InIn - <PERSON><PERSON><PERSON><PERSON> už<PERSON>lských rolí
{% endblock %}


{% block canvas_content %}
	<section class="bg-gray-50 dark:bg-gray-900 py-3 sm:py-5 h-screen">
		<div data-controller="user-role--delete" class="px-4 mx-auto max-w-screen-2xl lg:px-12">
			{% if searchString is not empty %}
				<h4 class="text-xl text-center text-gray-900 font-bold mb-4">Výsledky hledání:
					{{ searchString }}</h4>
			{% endif %}
			<div
				class="relative overflow-hidden bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
				{# counter section #}

				<div class="px-4 divide-y dark:divide-gray-700">
					<div class="flex flex-col py-3 space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:space-x-4">
						<div class="flex items-center flex-1 space-x-4">
							<h5>
								{% if searchString is empty %}
									<span class="text-gray-500">Celkem rolí:</span>
								{% else %}
									<span class="text-gray-500">Nalezeno rolí:</span>
								{% endif %}
								<span class="dark:text-white">{{ pagination.getTotalItemCount }}</span>
							</h5>
						</div>
					</div>

					{# controls section #}
					{% if searchString is empty %}
						<div class="flex flex-col items-stretch justify-between py-4 space-y-3 md:flex-row md:items-center md:space-y-0">
							<a href="{{ path ('app_user_role_new') }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
								<svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
									<path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
								</svg>Přidat novou roli
							</a>
							<form class="w-full md:max-w-sm flex-1 md:mr-4" action="{{ path('app_user_role_search') }}">
								<label for="default-search" class="text-sm font-medium text-gray-900 sr-only dark:text-white">Hledat</label>
								<div class="relative">
									<div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
										<svg aria-hidden="true" class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
										</svg>
									</div>
									<input type="search" name="q" id="default-search" class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Hledat..." required=""/>
									<button type="submit" class="text-white absolute right-0 bottom-0 top-0 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-r-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">Hledat</button>
								</div>
							</form>

							<div>
								<button id="filterDropdownButton" data-dropdown-toggle="filterDropdown" type="button" class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
									<svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" class="h-4 w-4 mr-2 text-gray-400" viewbox="0 0 20 20" fill="currentColor">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"/>
									</svg>Filtrovat<svg class="-mr-1 ml-1.5 w-5 h-5" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
										<path clip-rule="evenodd" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"/>
									</svg>
								</button>

								<div id="filterDropdown" class="z-10 hidden p-3 bg-white rounded-lg shadow w-56 dark:bg-gray-700">
									<h6 class="mb-2 text-sm font-medium text-gray-900 dark:text-white">Podle stavu</h6>
									<ul class="space-y-2 text-sm" aria-labelledby="dropdownDefault">
										<li>
											<label class="flex items-center text-sm font-medium text-gray-900 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-md px-1.5 py-1 w-full"><input type="checkbox" value="" checked="" class="w-4 h-4 mr-2 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"/>Aktivní</label>
										</li>
										<li>
											<label class="flex items-center text-sm font-medium text-gray-900 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-md px-1.5 py-1 w-full"><input type="checkbox" value="" class="w-4 h-4 mr-2 bg-gray-100 border-gray-300 rounded text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"/>Neaktivní</label>
										</li>
									</ul>
								</div>
							</div>
						</div>
					{% else %}
						<div class="flex flex-col items-stretch justify-between py-4 space-y-3 md:flex-row md:items-center md:space-y-0">
							<a href="{{ path('app_user_role_list') }}" class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
								<svg class="h-3.5 w-3.5 mr-2" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
									<path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"/>
								</svg>Zobrazit všechny
							</a>
						</div>
					{% endif %}
				</div>

				{# table #}

				<div class="overflow-x-auto">
					<table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
						<thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
							<tr>
								<th scope="col" class="px-4 py-3">Kód</th>
								<th scope="col" class="px-4 py-3">Název</th>
								<th scope="col" class="px-4 py-3">Popis</th>
								<th scope="col" class="px-4 py-3">Status</th>
								<th scope="col" class="px-4 py-3">Vytvořeno</th>
								<th scope="col" class="px-4 py-3">Aktualizováno</th>
								<th scope="col" class="px-4 py-3">Akce</th>
							</tr>
						</thead>
						<tbody>
							<tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
								<td class="px-4 py-2">ROLE_COM_ADMIN</td>
								<td scope="row" class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
									<div class="flex items-center">Administrátor</div>
								</td>
								<td class="px-4 py-2">administrátor Comfort-Likvidační</td>
								<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
									<div class="flex items-center gap-x-2 hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded">

										{% set label = 'Aktivní' %}
										{% set buttonText = 'Deaktivovat' %}
										{% set dotColor = 'green' %}
										<div class="w-3 h-3 bg-{{ dotColor }}-500 border rounded-full"></div>
										<p>{{ label }}</p>
									</div>
								</td>
								<td class="px-4 py-2">systémová role</td>
								<td class="px-4 py-2">---</td>
								<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white"></td>
							</tr>
							<tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
								<td class="px-4 py-2">ROLE_LIKVIDATOR</td>
								<td scope="row" class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
									<div class="flex items-center">Likvidátor</div>
								</td>
								<td class="px-4 py-2">Likvidátor pojistných událostí</td>
								<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
									<div class="flex items-center gap-x-2 hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded">

										{% set label = 'Aktivní' %}
										{% set buttonText = 'Deaktivovat' %}
										{% set dotColor = 'green' %}
										<div class="w-3 h-3 bg-{{ dotColor }}-500 border rounded-full"></div>
										<p>{{ label }}</p>
									</div>
								</td>
								<td class="px-4 py-2">systémová role</td>
								<td class="px-4 py-2">---</td>
								<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white"></td>
							</tr>              
							{% for role in pagination %}
								<tr class="border-b dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
									<td class="px-4 py-2">{{ role.code }}</td>
									<td scope="row" class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
										<div class="flex items-center">{{ role.name }}</div>
									</td>
									<td class="px-4 py-2">{{ role.description }}</td>
									<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
										<a href="{{ path('app_user_role_toggle_active', {id: role.id}) }}" class="flex items-center gap-x-2 hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded cursor-pointer">
											{% if role.active %}
												{% set label = 'Aktivní' %}
												{% set buttonText = 'Deaktivovat' %}
												{% set dotColor = 'green' %}
											{% else %}
												{% set label = 'Deaktivovaný' %}
												{% set buttonText = 'Aktivovat' %}
												{% set dotColor = 'red' %}
											{% endif %}

											<div class="w-3 h-3 bg-{{ dotColor }}-500 border rounded-full"></div>
											<p>{{ label }}</p>
											<svg class="w-4 h-4 ml-1 text-gray-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
												<path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" transform="rotate(45 10 10)" clip-rule="evenodd"></path>
											</svg>
										</a>
									</td>
									<td class="px-4 py-2">{{ role.createdAt|date('d.m.Y H:i') }}</td>
									<td class="px-4 py-2">{{ role.updatedAt|date('d.m.Y H:i') }}</td>
									<td class="px-4 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
										<button id="dropdown-button-{{ role.id }}" type="button" data-dropdown-toggle="dropdown-{{ role.id }}" class="inline-flex items-center p-1 text-sm font-medium text-center text-gray-500 rounded-lg hover:text-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none dark:text-gray-400 dark:hover:text-gray-100">
											<svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
												<path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"/>
											</svg>
										</button>

										<div id="dropdown-{{ role.id }}" class="z-10 hidden bg-white divide-y divide-gray-100 rounded shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
											<ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdown-button-{{ role.id }}">
												<li>
													<a href="{{ path('app_user_role_detail', {id: role.id}) }}" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
														Detail
													</a>
												</li>
												<li>
													<a href="{{ path('app_user_role_edit', {id: role.id}) }}" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Editace</a>
												</li>
												<li>
													{% if deletableRoles[role.id] %}
														<a href="#" data-action="user-role--delete#deleteModalDialog" data-role-name="{{ role.name }}" data-role-token="{{ csrf_token('delete' ~ role.id) }}" data-delete-url="{{ path('app_user_role_delete', {'id': role.id}) }}" data-modal-target="deleteModal" data-modal-toggle="deleteModal" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
															Smazat
														</a>
													{% else %}
														<span class="block px-4 py-2 text-gray-400 cursor-not-allowed" title="Tuto roli nelze smazat, protože je přiřazena uživatelům">
															Smazat
														</span>
													{% endif %}
												</li>
											</ul>
											{# Removed activation toggle link from dropdown as it's now in the Status column #}
										</div>
									</td>
								</tr>
							{% endfor %}
						</tbody>
					</table>
				</div>

				{{ include('components/user_role/modal/_user_role_delete_modal.html.twig') }}
				{# Detail modal removed as we now have a separate detail page #}

				{# navigation below table #}
					<nav class="flex flex-col items-start justify-between p-4 space-y-3 md:flex-row md:items-center md:space-y-0" aria-label="Table navigation"> <div class="flex items-center space-x-3">
						<label for="rows" class="text-xs font-normal text-gray-500 dark:text-gray-400">Rows per page</label>
						<select id="rows" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block py-1.5 pl-3.5 pr-6 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
							<option selected="" value="10">10</option>
							<option value="25">25</option>
							<option value="50">50</option>
							<option value="100">100</option>
						</select>
						<div class="text-xs font-normal text-gray-500 dark:text-gray-400">
							<span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getPaginationData.firstItemNumber }}
								-
								{{ pagination.getPaginationData.lastItemNumber }}</span>
							of
							<span class="font-semibold text-gray-900 dark:text-white">{{ pagination.getTotalItemCount }}</span>
						</div>
					</div>
					{# display navigation #}
					<div class="navigation">{{ knp_pagination_render(pagination) }}</div>
				</nav>
			</div>
		</div>
	</section>

{% endblock %}
