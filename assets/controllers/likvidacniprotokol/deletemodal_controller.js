import { Controller } from '@hotwired/stimulus';
import { Modal } from 'flowbite';

export default class extends Controller {
    static targets = ['soubor']

    connect() {

        // set the modal menu element
        const $targetEl = document.getElementById('deleteLPModal');

        // options with default values
        const options = {
            placement: 'center-center',
            backdrop: 'dynamic',
            backdropClasses:
                'bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40',
            closable: true,
        };

        // instance options object
        const instanceOptions = {
            id: 'deleteLPModal',
            override: true
        };
        this.modal = new Modal($targetEl, options, instanceOptions);

        this.deleteUrl = null;
    }

    open(event) {
        event.preventDefault();
        const link = event.currentTarget;
        this.deleteUrl = link.dataset.deleteUrl;
        this.souborTarget.textContent = link.dataset.filename;
        this.modal.show();
    }

    close(event) {
        this.modal.hide();
    }

    confirmDelete() {
        this.close();
        if (this.deleteUrl) {
            window.location.href = this.deleteUrl;
        }
    }

}