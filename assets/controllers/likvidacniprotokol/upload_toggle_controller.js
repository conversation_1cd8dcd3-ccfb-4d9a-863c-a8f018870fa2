import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["toggleButton", "uploadSection"]

    showUpload() {
        this.toggleButtonTarget.classList.add("hidden")
        this.uploadSectionTarget.classList.remove("hidden")
    }

    hideUpload() {
        this.uploadSectionTarget.classList.add("hidden")
        this.toggleButtonTarget.classList.remove("hidden")
    }
}
