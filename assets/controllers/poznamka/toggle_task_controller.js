// assets/controllers/toggle_task_controller.js
import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ["checkbox", "toggleDiv"]

    connect() {
        this.toggle(); // Initial check on load
        this.checkboxTarget.addEventListener('change', this.toggle.bind(this));
    }

    toggle() {
        if (this.checkboxTarget.checked) {
            this.toggleDivTarget.style.display = "block";
        } else {
            this.toggleDivTarget.style.display = "none";
        }
    }
}
