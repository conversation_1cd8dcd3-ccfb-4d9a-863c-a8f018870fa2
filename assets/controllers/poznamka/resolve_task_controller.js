import { Controller } from '@hotwired/stimulus';
import { Modal } from 'flowbite';

export default class extends Controller {
    static targets = ["taskId", "taskCsrfToken", "resolveUrl"];


    modal = null;

    connect() {
        // Inicializace controlleru
    }

    openModal(event) {
        try {
            const eventTarget= event.currentTarget;
            const button = eventTarget;

            const poznamkaId = button.getAttribute('data-poznamka-id');
            const poznamkaToken = button.getAttribute('data-poznamka-token');
            this.resolveUrl = button.getAttribute('data-poznamka-resolveUrl');

            if (poznamkaId !== null) {
                this.taskIdTarget.value = poznamkaId;
                this.taskCsrfTokenTarget.value = poznamkaToken;
                this.resolveUrlTarget.value = this.resolveUrl;
            }

            // set the modal menu element
            const $targetEl = document.getElementById('resolveTaskModal');

            // options with default values
            const options = {
                placement: 'center-center',
                backdrop: 'dynamic',
                backdropClasses:
                    'bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40',
                closable: true,
            };

            // instance options object
            const instanceOptions = {
                id: 'resolveTaskModal',
                override: true
            };

            this.modal = new Modal($targetEl, options, instanceOptions);

            // show the modal
            this.modal.show();


        } catch (error) {
            console.error("Error in openModal:", error);
        }
    }

    closeModal() {
        this.modal.hide();
    }

    confirmResolveTask() {
        try {
            this.closeModal();
            const form = document.getElementById('confirmResolveTaskForm');
            if (form && this.resolveUrlTarget.value) {
                form.action = this.resolveUrlTarget.value;
                form.submit();
            } else {
                console.error("Form or resolveUrl not found");
            }
        } catch (error) {
            console.error("Error in confirmResolveTask:", error);
        }
    }
}
