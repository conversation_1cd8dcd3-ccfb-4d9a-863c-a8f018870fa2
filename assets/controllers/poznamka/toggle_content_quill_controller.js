import { Controller } from '@hotwired/stimulus';
import Quill from 'quill';

export default class extends Controller {
  static targets = ["content", "quillContainer"];
  
  // Mapa pro uchování instancí Quill editorů
  quillInstances = new Map();

  connect() {
    // Inicializace controlleru
  }

  toggle(event) {
    // Přepnutí viditelnosti obsahu
    this.contentTarget.classList.toggle('hidden');
    
    // Inicializace Quill editoru při zobrazení obsahu
    if (!this.contentTarget.classList.contains('hidden') && this.hasQuillContainerTarget) {
      this.initializeQuill();
    }
  }
  
  toggleAll(event) {
    // Najde všechny elementy s třídou poznamka-content a přepne na nich třídu hidden
    document.querySelectorAll('.poznamka-content').forEach(element => {
      element.classList.toggle('hidden');
      
      // Pokud je element viditelný a obsahuje quill container, inicializuj Quill
      if (!element.classList.contains('hidden')) {
        const quillContainer = element.querySelector('[data-poznamka--toggle-content-quill-target="quillContainer"]');
        if (quillContainer) {
          this.initializeQuillForContainer(quillContainer);
        }
      }
    });
  }
  
  initializeQuill() {
    // Inicializace Quill editoru pro aktuální container
    if (this.hasQuillContainerTarget) {
      this.initializeQuillForContainer(this.quillContainerTarget);
    }
  }
  
  initializeQuillForContainer(container) {
    const containerId = container.dataset.id;
    
    // Inicializuj Quill pouze pokud ještě nebyl inicializován pro tento container
    if (!this.quillInstances.has(containerId)) {
      try {
        const quill = new Quill(container, {
          readOnly: true,
          modules: {
            toolbar: false
          }
        });
        
        const content = JSON.parse(container.dataset.content);
        quill.setContents(content);
        
        this.quillInstances.set(containerId, quill);
      } catch (error) {
        console.error('Chyba při inicializaci Quill editoru:', error);
      }
    }
  }
}
