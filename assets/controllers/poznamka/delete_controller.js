import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ["inputPoznamkaId", "poznamkaCsrfToken", "deletePoznamkaUrl"];

    deletePoznamkaModalDialog(event) {
        try {
            const button = event.currentTarget;
            const poznamkaId = button.getAttribute('data-poznamka-id');
            const poznamkaToken = button.getAttribute('data-poznamka-token');
            this.deleteUrl = button.getAttribute('data-poznamka-deleteurl');

            if (poznamkaId !== null) {
                this.inputPoznamkaIdTarget.value = poznamkaId;
                this.poznamkaCsrfTokenTarget.value = poznamkaToken;
            }
        } catch (error) {
            console.error("Error in deleteModalDialog:", error);
        }
    }

    confirmDeletePoznamkaButton() {
        try {
            var form = document.getElementById('confirmDeletePoznamkaButtonForm');
            form.action = this.deleteUrl;
            form.submit();
            
        } catch (error) {
            console.error("Error in confirmDeleteButton:", error);
        }
    }
};
