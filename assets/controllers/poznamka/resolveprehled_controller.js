import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ["inputPoznamkaId", "poznamkaCsrfToken", "resolveprehledPoznamkaUrl"];

    resolveprehledPoznamkaModalDialog(event) {
        try {
            const button = event.currentTarget;
            const poznamkaId = button.getAttribute('data-poznamka-id');
            const poznamkaToken = button.getAttribute('data-poznamka-token');
            this.resolveprehledUrl = button.getAttribute('data-poznamka-resolveprehledurl');
            if (poznamkaId !== null) {
                this.inputPoznamkaIdTarget.value = poznamkaId;
                this.poznamkaCsrfTokenTarget.value = poznamkaToken;
            }
        } catch (error) {
            console.error("Error in resolveModalDialog:", error);
        }
    }

    confirmResolveprehledPoznamkaButton() {
        try {
            var form = document.getElementById('confirmResolveprehledPoznamkaButtonForm');
            form.action = this.resolveprehledUrl;
            form.submit();
        } catch (error) {
            console.error("Error in confirmResolveprehledButton:", error);
        }
    }
};
