import { Controller } from '@hotwired/stimulus';
import Datepicker from 'flowbite-datepicker/Datepicker';
import Quill from 'quill';
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";

export default class extends Controller {
    static targets = ['textareaObsah', 'form']
    
    quill = null;

    connect() {
        // Initialize datepicker
        const datepickerEl = document.getElementById('poznamka_new_termin');
        if (datepickerEl) {
            new Datepicker(datepickerEl, {
                format: 'dd.mm.yyyy',
                todayHighlight: true,
                minDate : new Date()
            });
        }

        // Initialize Quill editor
        const options = {
            modules: {
                toolbar: [
                    [{ header: [1, 2, false] }],
                    ['bold', 'italic', 'underline'],
                    ['image', 'blockquote', 'link'],
                    [{ list: 'ordered' }, { list: 'bullet' }],
                ]
            },
            placeholder: '<PERSON><PERSON> můžete zadat obsah komentá<PERSON>e <PERSON>...',
            theme: 'snow'
        };

        this.quill = new Quill('#editorquill', options);
        

        // Add form submit event listener
        this.formTarget.addEventListener('submit', this.handleSubmit.bind(this));
    }

    // Handle form submission
    handleSubmit(event) {
        event.preventDefault(); // Prevent default form submission

        // Get Quill content
        const quillContent = this.quill.getContents();
        
        // Check if Quill content is empty (only contains an empty line)
        const isEmpty = quillContent.ops.length === 0 || 
                       (quillContent.ops.length === 1 && 
                        quillContent.ops[0].insert === '\n');
        
        if (isEmpty) {
            // If content is empty, show error message and don't submit
            alert('Obsah poznámky nemůže být prázdný. Prosím, vyplňte obsah.');
            return;
        }
        
        // Save Quill content to hidden textarea
        this.textareaObsahTarget.value = JSON.stringify(quillContent.ops);

        // Submit the form
        this.formTarget.submit();
    }
}
