import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ["inputPoznamkaId", "poznamkaCsrfToken", "resolvePoznamkaUrl"];

    resolvePoznamkaModalDialog(event) {
        try {
            const button = event.currentTarget;
            const poznamkaId = button.getAttribute('data-poznamka-id');
            const poznamkaToken = button.getAttribute('data-poznamka-token');
            this.resolveUrl = button.getAttribute('data-poznamka-resolveurl');
            if (poznamkaId !== null) {
                this.inputPoznamkaIdTarget.value = poznamkaId;
                this.poznamkaCsrfTokenTarget.value = poznamkaToken;
            }
        } catch (error) {
            console.error("Error in resolveModalDialog:", error);
        }
    }

    confirmResolvePoznamkaButton() {
        try {
            var form = document.getElementById('confirmResolvePoznamkaButtonForm');
            form.action = this.resolveUrl;
            form.submit();
        } catch (error) {
            console.error("Error in confirmResolveButton:", error);
        }
    }
};
