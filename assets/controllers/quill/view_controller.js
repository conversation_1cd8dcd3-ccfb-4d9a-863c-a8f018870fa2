import { Controller } from '@hotwired/stimulus';
import Quill from 'quill';

export default class extends Controller {
    static targets = ['container'];

    connect() {
        const quill = new Quill(this.containerTarget, {
            readOnly: true,
            modules: {
                toolbar: false
            }
        });

        const content = JSON.parse(this.containerTarget.dataset.content);
        quill.setContents(content);
    }



}