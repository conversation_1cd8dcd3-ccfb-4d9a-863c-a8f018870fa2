import { Controller } from '@hotwired/stimulus';
import Dropzone from 'dropzone';
import 'dropzone/dist/dropzone.css';

Dropzone.autoDiscover = false;

export default class extends Controller {
    static targets = ['form', 'counter']
    static values = {
        type: String, // přidáme hodnotu pro rozlišení typu formuláře
        slozkaType: String
    }

    connect() {
        
        this.initializeDropzone();
    }

    initializeDropzone() {
        const controller = this;
        const dropzone = new Dropzone(this.formTarget, {
            url: this.formTarget.action,
            paramName: 'soubor', // Změněno na 'soubor' pro lepší konzistenci s češtinou
            maxFilesize: 10,
            acceptedFiles: '.pdf,.doc,.docx,.jpg,.jpeg,.png',
            addRemoveLinks: true,
            dictDefaultMessage: this.getDefaultMessage(),
            dictRemoveFile: 'Odstranit',
            dictCancelUpload: 'Zrušit nahrávání',
            dictFileTooBig: 'Soubor je příli<PERSON> velk<PERSON> ({{filesize}}MB). Maximální velikost: {{maxFilesize}}MB.',
            dictInvalidFileType: 'Tento typ souboru není povolen.',
            init: function () {
                this.on('success', function (file, response) {
                    console.log('Soubor úspěšně nahrán:', response);
                    
                    // Update the counter for the specific slozka type
                    controller.updateFileCounter(response.type);
                });

                this.on('error', function (file, errorMessage) {
                    console.error('Chyba při nahrávání:', errorMessage);
                });

                this.on('addedfile', function (file) {
                    console.log('Soubor přidán:', file.name);
                });

                this.on('complete', function (file) {
                    if (this.getUploadingFiles().length === 0 && this.getQueuedFiles().length === 0) {
                        console.log('Všechny soubory byly nahrány');
                    }
                });
            }
        });
    }
    
    updateFileCounter(uploadType) {
        // Use the slozkaType value passed to the controller if available
        // Otherwise, map the upload type to slozka type
        let slozkaType = this.slozkaTypeValue;
        
        console.log('Updating file counter for upload type:', uploadType);
        console.log('Controller slozkaType value:', slozkaType);
        
        if (!slozkaType) {
            const slozkaTypeMap = {
                'odpojistovny': 'pojistovna_zadani',
                'odklienta': 'klient',
                'odtechnika': 'technik',
                'propojistovnu': 'pojistovna_ukonceni'
            };
            
            slozkaType = slozkaTypeMap[uploadType];
            console.log('Mapped slozkaType:', slozkaType);
        }
        
        if (!slozkaType) {
            console.warn('Could not determine slozkaType for upload type:', uploadType);
            return;
        }
        
        // Find the counter element for this slozka type
        const counterSelector = `[data-slozka-type="${slozkaType}"]`;
        console.log('Looking for counter element with selector:', counterSelector);
        const counterElement = document.querySelector(counterSelector);
        
        if (counterElement) {
            console.log('Found counter element:', counterElement);
            // Increment the counter
            let currentCount = parseInt(counterElement.textContent.trim(), 10);
            if (!isNaN(currentCount)) {
                const newCount = currentCount + 1;
                console.log(`Updating counter from ${currentCount} to ${newCount}`);
                counterElement.textContent = newCount.toString();
            } else {
                console.warn('Counter element content is not a number:', counterElement.textContent);
            }
        } else {
            console.warn('Counter element not found with selector:', counterSelector);
            console.log('Trying fallback method to find counter...');
            
            // If we can't find the counter with data attribute, try to find all counters and update based on context
            const allCounters = document.querySelectorAll('.bg-blue-100.text-blue-800');
            console.log('Found', allCounters.length, 'potential counter elements');
            
            allCounters.forEach((counter, index) => {
                // Check if this counter is within a section that matches our slozka type
                const sectionElement = counter.closest('div.flex.flex-row');
                const sectionTitle = sectionElement?.querySelector('h5')?.textContent.toLowerCase() || '';
                console.log(`Counter ${index} section title:`, sectionTitle);
                
                let shouldUpdate = false;
                
                if (slozkaType === 'pojistovna_zadani' && sectionTitle.includes('od pojišťovny')) {
                    shouldUpdate = true;
                } else if (slozkaType === 'klient' && sectionTitle.includes('od klienta')) {
                    shouldUpdate = true;
                } else if (slozkaType === 'technik' && sectionTitle.includes('od technika')) {
                    shouldUpdate = true;
                } else if (slozkaType === 'pojistovna_ukonceni' && sectionTitle.includes('pro pojišťovnu')) {
                    shouldUpdate = true;
                }
                
                if (shouldUpdate) {
                    console.log(`Found matching counter for ${slozkaType}:`, counter);
                    let currentCount = parseInt(counter.textContent.trim(), 10);
                    if (!isNaN(currentCount)) {
                        const newCount = currentCount + 1;
                        console.log(`Updating counter from ${currentCount} to ${newCount}`);
                        counter.textContent = newCount.toString();
                    } else {
                        console.warn('Counter element content is not a number:', counter.textContent);
                    }
                }
            });
        }
    }

    getDefaultMessage(type) {
        switch (this.typeValue) {
            case 'odpojistovny':
                return 'Přetáhněte sem soubory od pojišťovny';
            case 'odklienta':
                return 'Přetáhněte sem soubory od klienta';
            case 'odtechnika':
                return 'Přetáhněte sem soubory od technika';
            case 'propojistovnu':
                return 'Přetáhněte sem soubory pro pojistnovnu';
            default:
                return 'Přetáhněte soubory sem nebo klikněte pro výběr';
        }
    }
}
