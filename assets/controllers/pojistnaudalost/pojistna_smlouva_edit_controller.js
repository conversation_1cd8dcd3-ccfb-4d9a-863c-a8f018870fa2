import { Controller } from '@hotwired/stimulus';
import Datepicker from 'flowbite-datepicker/Datepicker';

export default class extends Controller {
    static targets = [
        'form',
        'castkaPojistna',
        'castkaSpoluucast',
        'castkaLimitRiziko',
        'limityRizikaStavbaSection',
        'limityRizikaStavbaContainer',
        'limitRizikaStavbaTemplate',
        'addLimitRizikaStavbaButton',
        'limityRizikaDomacnostSection',
        'limityRizikaDomacnostContainer',
        'limitRizikaDomacnostTemplate',
        'addLimitRizikaDomacnostButton'
    ]

    connect() {
        // Inicializace datepickeru pro pole PSPlatnostOd
        const datepickerEl = document.getElementById('pojistna_smlouva_edit_PSPlatnostOd');
        if (datepickerEl) {
            new Datepicker(datepickerEl, {
                format: 'dd.mm.yyyy',
                orientation: 'bottom',
                autohide: true,
                language: 'cs'
            });
        }

        // Inicializace chování checkboxů PSPojisteno
        this.initSingleCheckboxBehavior();
        
        // Inicializace formátování číselných polí
        this.initNumberFormatting();
        
        // Inicializace sekcí s limity rizik
        this.initLimityRizikaSection();
    }

    // Metoda pro inicializaci chování checkboxů - pouze jeden může být zaškrtnutý
    initSingleCheckboxBehavior() {
        // Najdi všechny checkboxy pro PSPojisteno
        const checkboxes = this.formTarget.querySelectorAll('input[name="pojistna_smlouva_edit[PSPojisteno][]"]');
        
        // Přidej event listener na každý checkbox
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (event) => {
                if (event.target.checked) {
                    // Pokud je checkbox zaškrtnutý, odškrtni všechny ostatní
                    checkboxes.forEach(cb => {
                        if (cb !== event.target) {
                            cb.checked = false;
                        }
                    });
                }
                
                // Aktualizace sekcí s limity rizik
                this.toggleLimityRizikaSection();
            });
        });
        
        // Kontrola, zda mají být sekce s limity rizik zobrazeny při načtení stránky
        this.toggleLimityRizikaSection();
    }
    
    // Metoda pro inicializaci sekcí s limity rizik
    initLimityRizikaSection() {
        // Přidání event listenerů pro tlačítka přidání nových limitů rizik
        if (this.hasAddLimitRizikaStavbaButtonTarget) {
            this.addLimitRizikaStavbaButtonTarget.addEventListener('click', this.addLimitRizikaStavba.bind(this));
        }
        
        if (this.hasAddLimitRizikaDomacnostButtonTarget) {
            this.addLimitRizikaDomacnostButtonTarget.addEventListener('click', this.addLimitRizikaDomacnost.bind(this));
        }
        
        // Přidání event listenerů pro odstranění limitů rizik
        this.addRemoveLimitRizikaListeners();
        
        // Inicializace formátování pro existující pole castkaLimitRizika v limitech rizik
        this.initExistingNumberFormatting();
    }
    
    // Inicializace formátování pro existující pole
    initExistingNumberFormatting() {
        // Stavba sekce
        if (this.hasLimityRizikaStavbaContainerTarget) {
            const castkaInputs = this.limityRizikaStavbaContainerTarget.querySelectorAll('input[name$="[castkaLimitRizika]"]');
            castkaInputs.forEach(input => {
                this.formatNumberField(input);
                input.addEventListener('input', this.handleNumberInput.bind(this));
            });
        }
        
        // Domácnost sekce
        if (this.hasLimityRizikaDomacnostContainerTarget) {
            const castkaInputs = this.limityRizikaDomacnostContainerTarget.querySelectorAll('input[name$="[castkaLimitRizika]"]');
            castkaInputs.forEach(input => {
                this.formatNumberField(input);
                input.addEventListener('input', this.handleNumberInput.bind(this));
            });
        }
    }
    
    // Zobrazení/skrytí sekcí s limity rizik podle stavu checkboxů PSPojisteno
    toggleLimityRizikaSection() {
        const checkboxes = this.formTarget.querySelectorAll('input[name="pojistna_smlouva_edit[PSPojisteno][]"]:checked');
        
        if (checkboxes.length > 0) {
            const checkedValue = checkboxes[0].value;
            
            switch(checkedValue) {
                case 'stavba':
                    this.showStavbaSection();
                    this.hideDomacnostSection();
                    break;
                case 'domacnost':
                    this.hideStavbaSection();
                    this.showDomacnostSection();
                    break;
                case 'stav_dom':
                    this.showStavbaSection();
                    this.showDomacnostSection();
                    break;
                default:
                    this.hideStavbaSection();
                    this.hideDomacnostSection();
            }
        } else {
            this.hideStavbaSection();
            this.hideDomacnostSection();
        }
    }
    
    // Zobrazení sekce stavba
    showStavbaSection() {
        if (this.hasLimityRizikaStavbaSectionTarget) {
            this.limityRizikaStavbaSectionTarget.style.display = 'block';
        }
    }
    
    // Skrytí sekce stavba
    hideStavbaSection() {
        if (this.hasLimityRizikaStavbaSectionTarget) {
            this.limityRizikaStavbaSectionTarget.style.display = 'none';
        }
    }
    
    // Zobrazení sekce domácnost
    showDomacnostSection() {
        if (this.hasLimityRizikaDomacnostSectionTarget) {
            this.limityRizikaDomacnostSectionTarget.style.display = 'block';
        }
    }
    
    // Skrytí sekce domácnost
    hideDomacnostSection() {
        if (this.hasLimityRizikaDomacnostSectionTarget) {
            this.limityRizikaDomacnostSectionTarget.style.display = 'none';
        }
    }
    
    // Přidání nového limitu rizika pro stavbu
    addLimitRizikaStavba(event) {
        event.preventDefault();
        
        if (this.hasLimitRizikaStavbaTemplateTarget && this.hasLimityRizikaStavbaContainerTarget) {
            // Získání šablony a vytvoření nového řádku
            const template = this.limitRizikaStavbaTemplateTarget.innerHTML;
            const index = this.limityRizikaStavbaContainerTarget.querySelectorAll('.limit-rizika-stavba-row').length;
            const newRow = template.replace(/__name__/g, index);
            
            // Přidání nového řádku do kontejneru
            this.limityRizikaStavbaContainerTarget.insertAdjacentHTML('beforeend', newRow);
            
            // Přidání event listeneru pro odstranění
            this.addRemoveLimitRizikaListeners();
            
            // Inicializace formátování pro nové pole castkaLimitRizika
            const newCastkaLimitRizikaInput = this.limityRizikaStavbaContainerTarget.querySelector(`.limit-rizika-stavba-row:nth-child(${index + 1}) input[name$="[castkaLimitRizika]"]`);
            if (newCastkaLimitRizikaInput) {
                newCastkaLimitRizikaInput.addEventListener('input', this.handleNumberInput.bind(this));
            }
        }
    }
    
    // Přidání nového limitu rizika pro domácnost
    addLimitRizikaDomacnost(event) {
        event.preventDefault();
        
        if (this.hasLimitRizikaDomacnostTemplateTarget && this.hasLimityRizikaDomacnostContainerTarget) {
            // Získání šablony a vytvoření nového řádku
            const template = this.limitRizikaDomacnostTemplateTarget.innerHTML;
            const index = this.limityRizikaDomacnostContainerTarget.querySelectorAll('.limit-rizika-domacnost-row').length;
            const newRow = template.replace(/__name__/g, index);
            
            // Přidání nového řádku do kontejneru
            this.limityRizikaDomacnostContainerTarget.insertAdjacentHTML('beforeend', newRow);
            
            // Přidání event listeneru pro odstranění
            this.addRemoveLimitRizikaListeners();
            
            // Inicializace formátování pro nové pole castkaLimitRizika
            const newCastkaLimitRizikaInput = this.limityRizikaDomacnostContainerTarget.querySelector(`.limit-rizika-domacnost-row:nth-child(${index + 1}) input[name$="[castkaLimitRizika]"]`);
            if (newCastkaLimitRizikaInput) {
                newCastkaLimitRizikaInput.addEventListener('input', this.handleNumberInput.bind(this));
            }
        }
    }
    
    // Přidání event listenerů pro odstranění limitů rizik
    addRemoveLimitRizikaListeners() {
        // Stavba sekce
        if (this.hasLimityRizikaStavbaContainerTarget) {
            const removeButtons = this.limityRizikaStavbaContainerTarget.querySelectorAll('.remove-limit-rizika-stavba');
            removeButtons.forEach(button => {
                // Odstranění existujících event listenerů, aby se nepřidávaly duplicitně
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);
                // Přidání nového event listeneru
                newButton.addEventListener('click', this.removeLimitRizikaStavba.bind(this));
            });
        }
        
        // Domácnost sekce
        if (this.hasLimityRizikaDomacnostContainerTarget) {
            const removeButtons = this.limityRizikaDomacnostContainerTarget.querySelectorAll('.remove-limit-rizika-domacnost');
            removeButtons.forEach(button => {
                // Odstranění existujících event listenerů, aby se nepřidávaly duplicitně
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);
                // Přidání nového event listeneru
                newButton.addEventListener('click', this.removeLimitRizikaDomacnost.bind(this));
            });
        }
    }
    
    // Odstranění limitu rizika pro stavbu
    removeLimitRizikaStavba(event) {
        event.preventDefault();
        const row = event.target.closest('.limit-rizika-stavba-row');
        if (row) {
            row.remove();
        }
    }
    
    // Odstranění limitu rizika pro domácnost
    removeLimitRizikaDomacnost(event) {
        event.preventDefault();
        const row = event.target.closest('.limit-rizika-domacnost-row');
        if (row) {
            row.remove();
        }
    }
    
    // Metoda pro inicializaci formátování číselných polí
    initNumberFormatting() {
        // Inicializace formátování pro pole castkaPojistna
        if (this.hasCastkaPojistnaTarget) {
            // Nastavení počátečního formátování, pokud pole již obsahuje hodnotu
            this.formatNumberField(this.castkaPojistnaTarget);
            
            // Přidání event listeneru pro změny v poli
            this.castkaPojistnaTarget.addEventListener('input', this.handleNumberInput.bind(this));
        }
        
        // Inicializace formátování pro pole castkaSpoluucast
        if (this.hasCastkaSpoluucastTarget) {
            // Nastavení počátečního formátování, pokud pole již obsahuje hodnotu
            this.formatNumberField(this.castkaSpoluucastTarget);
            
            // Přidání event listeneru pro změny v poli
            this.castkaSpoluucastTarget.addEventListener('input', this.handleNumberInput.bind(this));
        }
        
        // Inicializace formátování pro pole castkaLimitRiziko
        if (this.hasCastkaLimitRizikoTarget) {
            // Nastavení počátečního formátování, pokud pole již obsahuje hodnotu
            this.formatNumberField(this.castkaLimitRizikoTarget);
            
            // Přidání event listeneru pro změny v poli
            this.castkaLimitRizikoTarget.addEventListener('input', this.handleNumberInput.bind(this));
        }
        
        // Přidání event listeneru pro odeslání formuláře
        this.formTarget.addEventListener('submit', this.handleSubmit.bind(this));
    }
    
    // Metoda pro zpracování vstupu do číselného pole
    handleNumberInput(event) {
        // Získání hodnoty bez formátování (odstranění teček)
        const value = event.target.value.replace(/\D/g, '');
        
        // Pokud je hodnota prázdná, vrátíme prázdný řetězec
        if (!value) {
            event.target.value = '';
            return;
        }
        
        // Převedení na číslo
        const number = parseInt(value, 10);
        
        // Formátování čísla s tečkami pomocí toLocaleString
        // Použijeme 'cs-CZ' locale, ale nahradíme mezery tečkami
        event.target.value = number.toLocaleString('cs-CZ').replace(/\s/g, '.').replace(/,/g, '');
    }
    
    // Metoda pro formátování číselného pole
    formatNumberField(field) {
        if (field && field.value) {
            // Odstranění všech nečíselných znaků
            const value = field.value.replace(/\D/g, '');
            
            // Pokud je hodnota prázdná, vrátíme prázdný řetězec
            if (!value) {
                field.value = '';
                return;
            }
            
            // Převedení na číslo
            const number = parseInt(value, 10);
            
            // Formátování čísla s tečkami pomocí toLocaleString
            // Použijeme 'cs-CZ' locale, ale nahradíme mezery tečkami
            field.value = number.toLocaleString('cs-CZ').replace(/\s/g, '.').replace(/,/g, '');
        }
    }
    
    // Metoda pro zpracování odeslání formuláře
    handleSubmit(event) {
        event.preventDefault(); // Zabráníme výchozímu odeslání formuláře
        
        // Odstranění formátování z číselných polí před odesláním
        if (this.hasCastkaPojistnaTarget && this.castkaPojistnaTarget.value) {
            this.castkaPojistnaTarget.value = this.castkaPojistnaTarget.value.replace(/\./g, '');
        }
        
        if (this.hasCastkaSpoluucastTarget && this.castkaSpoluucastTarget.value) {
            this.castkaSpoluucastTarget.value = this.castkaSpoluucastTarget.value.replace(/\./g, '');
        }
        
        if (this.hasCastkaLimitRizikoTarget && this.castkaLimitRizikoTarget.value) {
            this.castkaLimitRizikoTarget.value = this.castkaLimitRizikoTarget.value.replace(/\./g, '');
        }
        
        // Odstranění formátování z číselných polí v limitech rizik - stavba
        if (this.hasLimityRizikaStavbaContainerTarget) {
            const castkaInputs = this.limityRizikaStavbaContainerTarget.querySelectorAll('input[name$="[castkaLimitRizika]"]');
            castkaInputs.forEach(input => {
                if (input.value) {
                    input.value = input.value.replace(/\./g, '');
                }
            });
        }
        
        // Odstranění formátování z číselných polí v limitech rizik - domácnost
        if (this.hasLimityRizikaDomacnostContainerTarget) {
            const castkaInputs = this.limityRizikaDomacnostContainerTarget.querySelectorAll('input[name$="[castkaLimitRizika]"]');
            castkaInputs.forEach(input => {
                if (input.value) {
                    input.value = input.value.replace(/\./g, '');
                }
            });
        }
        
        // Odeslání formuláře
        this.formTarget.submit();
    }
}
