import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['displayName', 'selectContainer', 'selectInput'];
    static values = {
        pojistnaUdalostId: Number,
        getStavyUrl: String,
        changeStavUrl: String
    };

    connect() {
        // Přidání event listeneru pro kliknutí na stav
        this.displayNameTarget.addEventListener('click', this.showSelect.bind(this));

        // Načtení seznamu stavů při inicializaci
        this.loadStavy();
    }

    // Zobrazení select inputu po kliknutí na stav
    showSelect() {
        this.displayNameTarget.classList.add('hidden');
        this.selectContainerTarget.classList.remove('hidden');

        // Přidání event listeneru pro kliknutí mimo selectbox
        this.clickOutsideHandler = this.handleClickOutside.bind(this);
        document.addEventListener('click', this.clickOutsideHandler);
    }

    // Zpracování kliknutí mimo selectbox
    handleClickOutside(event) {
        // Pokud kliknutí není na selectbox ani na stav
        if (!this.selectContainerTarget.contains(event.target) && 
            !this.displayNameTarget.contains(event.target)) {
            this.hideSelect();
        }
    }

    // Skrytí select inputu
    hideSelect() {
        this.selectContainerTarget.classList.add('hidden');
        this.displayNameTarget.classList.remove('hidden');

        // Odstranění event listeneru
        if (this.clickOutsideHandler) {
            document.removeEventListener('click', this.clickOutsideHandler);
            this.clickOutsideHandler = null;
        }
    }

    // Uložení změny stavu
    save() {
        // Skryjeme select a zobrazíme stav
        this.hideSelect();

        const stavValue = this.selectInputTarget.value;

        // Zobrazíme spinner
        document.getElementById('stav-spinner').classList.remove('hidden');

        // AJAX volání pro změnu stavu
        fetch(this.changeStavUrlValue, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                stavLikvidace: stavValue
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Chyba při změně stavu');
            }
            return response.json();
        })
        .then(response => {
            // Aktualizace zobrazení stavu
            this.displayNameTarget.innerHTML = 'Stav: ' + response.data.stavLabel;

            // Zobrazení flash zprávy
            this.showFlashMessage('Stav byl úspěšně změněn', 'success');

            // Skryjeme spinner
            document.getElementById('stav-spinner').classList.add('hidden');
        })
        .catch(error => {
            console.error('Chyba:', error);
            this.showFlashMessage('Chyba při změně stavu', 'error');

            // Skryjeme spinner i v případě chyby
            document.getElementById('stav-spinner').classList.add('hidden');
        });
    }

    // Načtení seznamu stavů
    loadStavy() {
        // Zobrazíme spinner
        document.getElementById('stav-spinner').classList.remove('hidden');

        fetch(this.getStavyUrlValue, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Chyba při načítání stavů');
            }
            return response.json();
        })
        .then(response => {
            // Vyčištění select inputu
            this.selectInputTarget.innerHTML = '';

            // Naplnění select inputu stavy
            const stavy = response.data.stavy;
            for (const [label, value] of Object.entries(stavy)) {
                const option = document.createElement('option');
                option.value = value;
                option.textContent = label;

                // Nastavení vybraného stavu
                if (this.displayNameTarget.textContent.trim() === label) {
                    option.selected = true;
                }

                this.selectInputTarget.appendChild(option);
            }

            // Skryjeme spinner
            document.getElementById('stav-spinner').classList.add('hidden');
        })
        .catch(error => {
            console.error('Chyba:', error);
            this.showFlashMessage('Chyba při načítání stavů', 'error');

            // Skryjeme spinner i v případě chyby
            document.getElementById('stav-spinner').classList.add('hidden');
        });
    }

    // Pomocná metoda pro zobrazení flash zprávy
    showFlashMessage(message, type) {
        const event = new CustomEvent('flash-message', {
            detail: {
                message: message,
                type: type
            }
        });
        document.dispatchEvent(event);
    }
}
