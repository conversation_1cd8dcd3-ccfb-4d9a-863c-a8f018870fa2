import { Controller } from '@hotwired/stimulus';
import { Modal } from 'flowbite';

export default class extends Controller {
    static targets = ["inputUcastnikId", "ucastnikCsrfToken", "ucastnikNameSpan"];

    connect() {

        // set the modal menu element
        const $targetEl = document.getElementById('deleteUcastnikModal');

        // options with default values
        const options = {
            placement: 'center-center',
            backdrop: 'dynamic',
            backdropClasses:
                'bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40',
            closable: true,
        };

        // instance options object
        const instanceOptions = {
            id: 'deleteUcastnikModal',
            override: true
        };

        /*
 * $targetEl: required
 * options: optional
 */
        this.modal = new Modal($targetEl, options, instanceOptions);
    }

    deleteUcastnikModalDialog(event) {
        // show the modal
        try {
            const button = event.currentTarget;
            const ucastnikId = button.getAttribute('data-ucastnik-id');
            const ucastnikToken = button.getAttribute('data-ucastnik-token');
            const ucastnikName = button.getAttribute('data-ucastnik-name');
            this.ucastnikDeleteUrl = button.getAttribute('data-ucastnik-deleteurl');
            if (ucastnikId !== null) {
                this.inputUcastnikIdTarget.value = ucastnikId;
                this.ucastnikCsrfTokenTarget.value = ucastnikToken;
                try {
                    this.ucastnikNameSpanTarget.textContent = ucastnikName;
                } catch (error) {
                    console.error("Error setting ucastnikNameSpan textContent:", error);
                }

            }
            this.modal.show();
        } catch (error) {
            console.error("Error in deleteUcastnikModalDialog:", error);
        }
    }

    confirmDeleteUcastnikButton() {
        try {
            var form = document.getElementById('confirmDeleteUcastnikButtonForm');
            form.action = this.ucastnikDeleteUrl;
            this.modal.hide();
            form.submit();

        } catch (error) {
            console.error("Error in confirmDeleteUcastnikButton:", error);
        }
    }
};
