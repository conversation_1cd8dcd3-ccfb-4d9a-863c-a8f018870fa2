import { Controller } from '@hotwired/stimulus';
import Quill from 'quill';
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";

/**
 * Stimulus controller pro obsluhu formuláře doplňujících informací k pojistné ud<PERSON>
 */
export default class extends Controller {
    static targets = [
        'regresSelect', 
        'regresDetails', 
        'popisVznikuEditor', 
        'popisVznikuInput', 
        'rozsahPoskozeniEditor', 
        'rozsahPoskozeniInput',
        'form',
        'castkaRezerva'
    ];

    quillPopisVzniku = null;
    quillRozsahPoskozeni = null;

    connect() {
        // Inicializace Quill editorů
        this.initializeQuillEditors();
        
        // Při inicializaci controlleru nastavíme viditelnost detailů regresu podle aktuální hodnoty
        this.toggleRegresDetails();
        
        // Přidáme event listener pro změnu hodnoty v select boxu
        if (this.hasRegresSelectTarget) {
            this.regresSelectTarget.addEventListener('change', this.toggleRegresDetails.bind(this));
        }

        // Přidáme event listener pro odeslání formuláře
        if (this.hasFormTarget) {
            this.formTarget.addEventListener('submit', this.handleSubmit.bind(this));
        }
        
        // Inicializace formátování pole castkaRezerva
        if (this.hasCastkaRezervaTarget) {
            // Nastavení počátečního formátování, pokud pole již obsahuje hodnotu
            this.formatCastkaRezerva();
            
            // Přidání event listenerů pro změny v poli
            this.castkaRezervaTarget.addEventListener('input', this.handleCastkaRezervaInput.bind(this));
            //this.castkaRezervaTarget.addEventListener('blur', this.formatCastkaRezerva.bind(this));
        }
    }

    /**
     * Inicializuje Quill editory pro popis vzniku a rozsah poškození
     */
    initializeQuillEditors() {
        const options = {
            modules: {
                toolbar: [
                    [{ header: [1, 2, false] }],
                    ['bold', 'italic', 'underline'],
                    ['image', 'blockquote', 'link'],
                    [{ list: 'ordered' }, { list: 'bullet' }],
                ]
            },
            theme: 'snow'
        };

        // Inicializace Quill editoru pro popis vzniku
        if (this.hasPopisVznikuEditorTarget) {
            this.quillPopisVzniku = new Quill(this.popisVznikuEditorTarget, {
                ...options,
                placeholder: 'Zde můžete zadat popis vzniku...'
            });

            // Načtení existujícího obsahu, pokud je k dispozici
            if (this.hasPopisVznikuInputTarget && this.popisVznikuInputTarget.value) {
                try {
                    const existingContent = JSON.parse(this.popisVznikuInputTarget.value);
                    if (existingContent) {
                        this.quillPopisVzniku.setContents(existingContent);
                    }
                } catch (e) {
                    // Pokud obsah není validní JSON, nastavíme ho jako text
                    this.quillPopisVzniku.setText(this.popisVznikuInputTarget.value || '');
                }
            }
        }

        // Inicializace Quill editoru pro rozsah poškození
        if (this.hasRozsahPoskozeniEditorTarget) {
            this.quillRozsahPoskozeni = new Quill(this.rozsahPoskozeniEditorTarget, {
                ...options,
                placeholder: 'Zde můžete zadat rozsah poškození...'
            });

            // Načtení existujícího obsahu, pokud je k dispozici
            if (this.hasRozsahPoskozeniInputTarget && this.rozsahPoskozeniInputTarget.value) {
                try {
                    const existingContent = JSON.parse(this.rozsahPoskozeniInputTarget.value);
                    if (existingContent) {
                        this.quillRozsahPoskozeni.setContents(existingContent);
                    }
                } catch (e) {
                    // Pokud obsah není validní JSON, nastavíme ho jako text
                    this.quillRozsahPoskozeni.setText(this.rozsahPoskozeniInputTarget.value || '');
                }
            }
        }
    }

    /**
     * Přepíná viditelnost detailů regresu podle hodnoty v select boxu
     */
    toggleRegresDetails() {
        if (this.hasRegresSelectTarget && this.hasRegresDetailsTarget) {
            // Hodnota v select boxu je string, proto porovnáváme s '1' (true) a '0' (false)
            if (this.regresSelectTarget.value === '1') {
                this.regresDetailsTarget.classList.remove('hidden');
            } else {
                this.regresDetailsTarget.classList.add('hidden');
            }
        }
    }

    /**
     * Zpracovává odeslání formuláře - ukládá obsah Quill editorů do skrytých input polí
     */
    handleSubmit(event) {
        event.preventDefault(); // Zabráníme výchozímu odeslání formuláře

        // Uložení obsahu Quill editoru pro popis vzniku
        if (this.quillPopisVzniku && this.hasPopisVznikuInputTarget) {
            const quillContent = this.quillPopisVzniku.getContents();
            this.popisVznikuInputTarget.value = JSON.stringify(quillContent.ops);
        }

        // Uložení obsahu Quill editoru pro rozsah poškození
        if (this.quillRozsahPoskozeni && this.hasRozsahPoskozeniInputTarget) {
            const quillContent = this.quillRozsahPoskozeni.getContents();
            this.rozsahPoskozeniInputTarget.value = JSON.stringify(quillContent.ops);
        }

        // Odstranění formátování z pole castkaRezerva před odesláním
        if (this.hasCastkaRezervaTarget && this.castkaRezervaTarget.value) {
            this.castkaRezervaTarget.value = this.castkaRezervaTarget.value.replace(/\./g, '');
        }

        // Odeslání formuláře
        this.formTarget.submit();
    }
    
    /**
     * Zpracovává vstup do pole castkaRezerva - jednodušší přístup bez řešení pozice kurzoru
     */
    handleCastkaRezervaInput(event) {
        // Získání hodnoty bez formátování (odstranění teček)
        const value = event.target.value.replace(/\D/g, '');
        
        // Pokud je hodnota prázdná, vrátíme prázdný řetězec
        if (!value) {
            event.target.value = '';
            return;
        }
        
        // Převedení na číslo
        const number = parseInt(value, 10);
        
        // Formátování čísla s tečkami pomocí toLocaleString
        // Použijeme 'cs-CZ' locale, ale nahradíme čárky tečkami
        event.target.value = number.toLocaleString('cs-CZ').replace(/\s/g, '.').replace(/,/g, '');
    }

    /**
     * Formátuje hodnotu v poli castkaRezerva při ztrátě fokusu
     */
    formatCastkaRezerva() {
        if (this.hasCastkaRezervaTarget && this.castkaRezervaTarget.value) {
            // Odstranění všech nečíselných znaků
            const value = this.castkaRezervaTarget.value.replace(/\D/g, '');
            
            // Pokud je hodnota prázdná, vrátíme prázdný řetězec
            if (!value) {
                this.castkaRezervaTarget.value = '';
                return;
            }
            
            // Převedení na číslo
            const number = parseInt(value, 10);
            
            // Formátování čísla s tečkami pomocí toLocaleString
            // Použijeme 'cs-CZ' locale, ale nahradíme čárky tečkami
            this.castkaRezervaTarget.value = number.toLocaleString('cs-CZ').replace(/\s/g, '.').replace(/,/g, '');
        }
    }
}
