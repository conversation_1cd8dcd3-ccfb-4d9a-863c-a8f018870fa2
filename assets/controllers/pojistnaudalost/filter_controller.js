import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = [
        'likvidator',
        'technik',
        'stav',
        'zadavatel',
        'dateFrom',
        'dateTo',
        'dateType',
        'categoryCheckbox'
    ];

    applyFilters() {
        const params = new URLSearchParams();
        
        // Dates
        const dateFrom = this.dateFromTarget.value;
        const dateTo = this.dateToTarget.value;
        const dateType = this.dateTypeTarget.value;

        if (dateFrom || dateTo) {
          params.append('dateField', dateType); // 'prijato' or 'ukonceno'
        }
        if (dateFrom) params.append('od', dateFrom);
        if (dateTo) params.append('do', dateTo);

        // Likvidator, Stav, Zadavatel
        const likvidator = this.likvidatorTarget.value;
        const stav = this.stavTarget.value;
        const zadavatel = this.zadavatelTarget.value;
        const technik = this.technikTarget.value;

        if (likvidator) params.append('likvidator', likvidator);
        if (technik) params.append('technik', technik);
        if (stav) params.append('stav', stav);
        if (zadavatel) params.append('zadavatel', zadavatel);

        // Kategorie (checkboxes)
        const selectedCategories = this.categoryCheckboxTargets
            .filter(cb => cb.checked)
            .map(cb => cb.getAttribute('category-id'));

        if (selectedCategories.length) {
            params.append('kategorie', selectedCategories.join(','));
        }

        // Final redirect
        const baseUrl = window.location.href.split('?')[0];
        window.location.href = `${baseUrl}?${params.toString()}`;
    }

    resetFilters() {
        window.location.href = window.location.href.split('?')[0];
    }
}