import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['toggle']

    connect() {
        document.addEventListener('click', this.handleDocumentClick.bind(this));
    }

    disconnect() {
        document.removeEventListener('click', this.handleDocumentClick.bind(this));
    }

    toggle(event) {
        event.stopPropagation();
        const tr = event.currentTarget.closest('tr');
        const notesRow = tr.nextElementSibling;

        if (notesRow?.classList.contains('pou-quick-notes-row')) {
            if (!notesRow.classList.contains('hidden')) {
                notesRow.classList.add('hidden');
                tr.classList.remove('bg-gray-100', 'dark:bg-gray-700');
                return;
            }

            const allVisibleNoteRows = document.querySelectorAll('tr.pou-quick-notes-row:not(.hidden)');
            allVisibleNoteRows.forEach(row => {
                row.classList.add('hidden');
                const toggleRow = row.previousElementSibling;
                if (toggleRow) {
                    toggleRow.classList.remove('bg-gray-100', 'dark:bg-gray-700');
                }
            });

            notesRow.classList.remove('hidden');
            tr.classList.add('bg-gray-100', 'dark:bg-gray-700');
        }
    }

    handleDocumentClick(event) {
        const visibleNoteRows = document.querySelectorAll('tr.pou-quick-notes-row:not(.hidden)');

        if (visibleNoteRows.length === 0) return; // No open notes rows

        visibleNoteRows.forEach(noteRow => {
            const toggleRow = noteRow.previousElementSibling;

            if (!toggleRow.contains(event.target) && !noteRow.contains(event.target)) {
                noteRow.classList.add('hidden');
                if (toggleRow) {
                    toggleRow.classList.remove('bg-gray-100', 'dark:bg-gray-700');
                }
            }
        });
    }
}
