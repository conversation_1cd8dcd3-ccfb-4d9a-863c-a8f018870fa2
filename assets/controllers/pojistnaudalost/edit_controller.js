import { Controller } from '@hotwired/stimulus';
import Datepicker from 'flowbite-datepicker/Datepicker';
import Quill from 'quill';
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";

export default class extends Controller {
    static targets = ['datumVzniku', 'datumNahlaseni', 'textareaPopisvzniku', 'textareaRozsahposkozeni', 'form'];

    connect() {

        const datepicker1 = new Datepicker(this.datumNahlaseniTarget, {
            format: 'dd.mm.yyyy',
            orientation: 'bottom',
            autohide: true,
        });

        const datepicker2 = new Datepicker(this.datumVznikuTarget, {
            format: 'dd.mm.yyyy',
            maxDate: new Date(),
            orientation: 'bottom',
            autohide: true,
        });

        // Aktualizace minDate pro 'Datum nahlaseni' na základě 'Datum vzniku'
        this.datumVznikuTarget.addEventListener('changeDate', (event) => {
            const selectedDate = event.detail.date;
            datepicker1.setOptions({
                minDate: selectedDate
            });
            datepicker1.update();
        });


        const options1 = {
            modules: {
                toolbar: [
                    [{ header: [1, 2, false] }],
                    ['bold', 'italic', 'underline'],
                    ['image', 'blockquote', 'link'],
                    [{ list: 'ordered' }, { list: 'bullet' }],
                ]
            },
            placeholder: 'Zde můžete zadat popis vzniku pojistné události',
            theme: 'snow'
        };

        const options2 = {
            modules: {
                toolbar: [
                    [{ header: [1, 2, false] }],
                    ['bold', 'italic', 'underline'],
                    ['image', 'blockquote', 'link'],
                    [{ list: 'ordered' }, { list: 'bullet' }],
                ]
            },
            placeholder: 'Zde můžete zadat Rozsah poškození',
            theme: 'snow'
        };        


        this.quill = new Quill('#editorquillPopis', options1);                
        let popisContent = '';
        if (this.isJsonString(this.textareaPopisvznikuTarget.value)) {
            popisContent = JSON.parse(this.textareaPopisvznikuTarget.value);
        }

        const result = this.quill.setContents(popisContent);

        this.quill2 = new Quill('#editorquillRozsah', options2);
        let rozsahContent = '';
        if (this.isJsonString(this.textareaRozsahposkozeniTarget.value)) {
            rozsahContent = JSON.parse(this.textareaRozsahposkozeniTarget.value);
        }
        const result2 = this.quill2.setContents(rozsahContent);

        // Přidání event listeneru pro odeslání formuláře
        this.formTarget.addEventListener('submit', this.handleSubmit.bind(this));
    }


    // Metoda pro zpracování odeslání formuláře
    handleSubmit(event) {
        event.preventDefault(); // Zastavení výchozího odeslání formuláře

        // Zde můžeš provést potřebné úpravy dat
        this.upravitDataFormulare();

        // Odeslání formuláře
        this.formTarget.submit();
    }

    // Metoda pro úpravu dat formuláře
    upravitDataFormulare() {

        const quillContent = this.quill.getContents();
        this.textareaPopisvznikuTarget.value = JSON.stringify(quillContent.ops);

        const quillRozsahContent = this.quill2.getContents();
        this.textareaRozsahposkozeniTarget.value = JSON.stringify(quillRozsahContent.ops);        
    }

    isJsonString(str) {
        try {
            JSON.parse(str);
        } catch (e) {
            return false;
        }
        return true;
    }

}
