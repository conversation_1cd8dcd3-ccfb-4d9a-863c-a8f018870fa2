import { Controller } from '@hotwired/stimulus';
import { Modal } from 'flowbite';

export default class extends Controller {
    static targets = ["inputVehicleId", "vehicleCsrfToken", "vehicleKodrzSpan"];

    connect() {

        // set the modal menu element
        const $targetEl = document.getElementById('deleteCarModal');

        // options with default values
        const options = {
            placement: 'center-center',
            backdrop: 'dynamic',
            backdropClasses:
                'bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40',
            closable: true,
        };

        // instance options object
        const instanceOptions = {
            id: 'deleteCarModal',
            override: true
        };

        /*
 * $targetEl: required
 * options: optional
 */
        this.modal = new Modal($targetEl, options, instanceOptions);
    }

    deleteVehicleModalDialog(event) {
        // show the modal
        try {
            const button = event.currentTarget;
            const vehicleId = button.getAttribute('data-vehicle-id');
            const vehicleToken = button.getAttribute('data-vehicle-token');
            const vehicleKodrz = button.getAttribute('data-vehicle-kodrz');
            this.vehicleDeleteUrl = button.getAttribute('data-vehicle-deleteurl');
            if (vehicleId !== null) {
                this.inputVehicleIdTarget.value = vehicleId;
                this.vehicleCsrfTokenTarget.value = vehicleToken;
                try {
                    this.vehicleKodrzSpanTarget.textContent = vehicleKodrz;
                } catch (error) {
                    console.error("Error setting vehicleNameSpan textContent:", error);
                }

            }
            this.modal.show();
        } catch (error) {
            console.error("Error in deleteCarModalDialog:", error);
        }
    }

    confirmDeleteVehicleButton() {
        try {
            var form = document.getElementById('confirmDeleteVehicleButtonForm');
            form.action = this.vehicleDeleteUrl;
            this.modal.hide();
            form.submit();

        } catch (error) {
            console.error("Error in confirmDeleteButton:", error);
        }
    }
};
