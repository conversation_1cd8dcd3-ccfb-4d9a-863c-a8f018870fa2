import { Controller } from '@hotwired/stimulus';
import { Modal } from 'flowbite';

export default class extends Controller {
    static targets = ["csrfToken"];

    connect() {
        // set the modal menu element
        const $targetEl = document.getElementById('closePuConfirmationModal');

        // options with default values
        const options = {
            placement: 'center-center',
            backdrop: 'dynamic',
            backdropClasses:
                'bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40',
            closable: true,
        };

        // instance options object
        const instanceOptions = {
            id: 'closePuConfirmationModal',
            override: true
        };

        this.modal = new Modal($targetEl, options, instanceOptions);
    }

    showConfirmationModal(event) {
        try {
            const button = event.currentTarget;
            const csrfToken = button.getAttribute('data-csrf-token');
            const puId = button.getAttribute('data-pu-id');
            
            if (csrfToken !== null) {
                this.csrfTokenTarget.value = csrfToken;
            }
            
            this.puId = puId;
            this.modal.show();
        } catch (error) {
            console.error("Error in showConfirmationModal:", error);
        }
    }

    confirmClosePu() {
        try {
            const form = document.getElementById('closePuConfirmationForm');
            form.action = `/pou/${this.puId}/close`;
            this.modal.hide();
            form.submit();
        } catch (error) {
            console.error("Error in confirmClosePu:", error);
        }
    }

    cancelClosePu() {
        try {
            this.modal.hide();
        } catch (error) {
            console.error("Error in cancelClosePu:", error);
        }
    }
}
