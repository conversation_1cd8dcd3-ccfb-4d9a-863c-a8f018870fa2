import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['displayName', 'selectContainer', 'selectInput'];
    static values = {
        pojistnaUdalostId: Number,
        getLikvidatorsUrl: String,
        changeLikvidatorUrl: String
    };

    connect() {
        // Přidání event listeneru pro kliknutí na jméno likvidátora
        this.displayNameTarget.addEventListener('click', this.showSelect.bind(this));

        // Načtení seznamu likvidátorů při inicializaci
        this.loadLikvidators();
    }

    // Zobrazení select inputu po kliknutí na jméno likvidátora
    showSelect() {
        this.displayNameTarget.classList.add('hidden');
        this.selectContainerTarget.classList.remove('hidden');

        // Přidání event listeneru pro kliknutí mimo selectbox
        this.clickOutsideHandler = this.handleClickOutside.bind(this);
        document.addEventListener('click', this.clickOutsideHandler);
    }

    // Zpracování kliknutí mimo selectbox
    handleClickOutside(event) {
        // Pokud kliknutí není na selectbox ani na jméno likvidátora
        if (!this.selectContainerTarget.contains(event.target) && 
            !this.displayNameTarget.contains(event.target)) {
            this.hideSelect();
        }
    }

    // Skrytí select inputu
    hideSelect() {
        this.selectContainerTarget.classList.add('hidden');
        this.displayNameTarget.classList.remove('hidden');

        // Odstranění event listeneru
        if (this.clickOutsideHandler) {
            document.removeEventListener('click', this.clickOutsideHandler);
            this.clickOutsideHandler = null;
        }
    }

    // Uložení změny likvidátora
    save() {
        // Skryjeme select a zobrazíme jméno likvidátora
        this.hideSelect();

        const likvidatorId = this.selectInputTarget.value;

        // Zobrazíme spinner
        document.getElementById('likvidator-spinner').classList.remove('hidden');

        // AJAX volání pro změnu likvidátora
        fetch(this.changeLikvidatorUrlValue, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                likvidatorId: likvidatorId
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Chyba při změně likvidátora');
            }
            return response.json();
        })
        .then(response => {
            // Aktualizace zobrazení jména likvidátora
            this.displayNameTarget.innerHTML = response.data.likvidatorName || 'zatím nepřiřazený';

            // Zobrazení flash zprávy
            this.showFlashMessage('Likvidátor byl úspěšně změněn', 'success');

            // Skryjeme spinner
            document.getElementById('likvidator-spinner').classList.add('hidden');
        })
        .catch(error => {
            console.error('Chyba:', error);
            this.showFlashMessage('Chyba při změně likvidátora', 'error');

            // Skryjeme spinner i v případě chyby
            document.getElementById('likvidator-spinner').classList.add('hidden');
        });
    }

    // Načtení seznamu likvidátorů
    loadLikvidators() {
        // Zobrazíme spinner
        document.getElementById('likvidator-spinner').classList.remove('hidden');

        fetch(this.getLikvidatorsUrlValue, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Chyba při načítání likvidátorů');
            }
            return response.json();
        })
        .then(response => {
            // Vyčištění select inputu
            this.selectInputTarget.innerHTML = '<option value="">bez likvidátora</option>';

            // Naplnění select inputu likvidátory
            response.data.likvidators.forEach(likvidator => {
                const option = document.createElement('option');
                option.value = likvidator.id;
                option.textContent = `${likvidator.name} ${likvidator.surname}`;

                // Nastavení vybraného likvidátora - porovnání bez ohledu na bílé znaky
                const displayText = this.displayNameTarget.textContent.replace(/\s+/g, ' ').trim();
                const likvidatorText = `${likvidator.name} ${likvidator.surname}`;
                if (displayText === likvidatorText) {
                    option.selected = true;
                }

                this.selectInputTarget.appendChild(option);
            });

            // Skryjeme spinner
            document.getElementById('likvidator-spinner').classList.add('hidden');
        })
        .catch(error => {
            console.error('Chyba:', error);
            this.showFlashMessage('Chyba při načítání likvidátorů', 'error');

            // Skryjeme spinner i v případě chyby
            document.getElementById('likvidator-spinner').classList.add('hidden');
        });
    }

    // Pomocná metoda pro zobrazení flash zprávy
    showFlashMessage(message, type) {
        const event = new CustomEvent('flash-message', {
            detail: {
                message: message,
                type: type
            }
        });
        document.dispatchEvent(event);
    }
}
