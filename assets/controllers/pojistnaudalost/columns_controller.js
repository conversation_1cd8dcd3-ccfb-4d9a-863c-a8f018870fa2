import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['colCheckbox', 'submitButton'];
    timer = null;
    selectedCols = [];

    submit() {
        this.getCols();

        var url = this.submitButtonTarget.getAttribute('data-url');
        url = url.replace('REPLACE_COLUMNS', this.selectedCols.join(','));
        window.location.href = url;
    }

    getCols() {
        this.colCheckboxTargets.forEach((checkbox) => {
            var column = checkbox.getAttribute('data-col');
            if (checkbox.checked) this.selectedCols.push(column);
        });
    }
}
