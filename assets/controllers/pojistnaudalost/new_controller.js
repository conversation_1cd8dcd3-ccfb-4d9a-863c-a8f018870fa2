import { Controller } from '@hotwired/stimulus';
import Datepicker from 'flowbite-datepicker/Datepicker';
import Quill from 'quill';
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";


export default class extends Controller {
    static targets = [
        'kategorieSelect',
        'textareaPopisvzniku',
        'textareaRozsahposkozeni',
        'form',
        'sectionPoskozeny',
        'sectionPoverenaOsoba',
        'sectionKontaktniOsoba',
        'checkboxPoskozeny',
        'checkboxPoskozenyWithLabel',
        'checkboxPoverenaOsoba',
        'checkboxKontaktniOsoba',
        'pojistenyIsCompany',
        'poskozenyIsCompany',
        'poverenaOsobaIsCompany',
        'kontaktniOsobaIsCompany'
    ]

    static values = {
        initialized: Boolean
    }

    initiallyChecked = false;
    quill = null;
    quill2 = null;


    connect() {
        // Skrytí sekcí formulá<PERSON>e při načtení
        this.hideFormSections();

        // Inicializace stavu sekcí podle checkboxů
        this.initializeSections();

        // Inicializace zobrazení polí podle checkboxů právnických osob
        this.initializeCompanyFields();


        // Datum nahlaseni skody
        const datepickerEl1 = document.getElementById('pojistna_udalost_new_datumNahlaseniSkody');
        const datepicker1 = new Datepicker(datepickerEl1, {
            format: 'dd.mm.yyyy',
            orientation: 'bottom',
            autohide: true,
        });

        // Datum vzniku pojistné události
        const datepickerEl2 = document.getElementById('pojistna_udalost_new_datumVzniku');
        const datepicker2 = new Datepicker(datepickerEl2, {
            format: 'dd.mm.yyyy',
            maxDate: new Date(),
            orientation: 'bottom',
            autohide: true,
        });

        // Aktualizace minDate pro 'Datum nahlaseni' na základě 'Datum vzniku'
        datepickerEl2.addEventListener('changeDate', (event) => {
            const selectedDate = event.detail.date;
            datepicker1.setOptions({
                minDate: selectedDate
            });
            datepicker1.update();
        });

        const options1 = {
            modules: {
                toolbar: [
                    [{ header: [1, 2, false] }],
                    ['bold', 'italic', 'underline'],
                    ['image', 'blockquote', 'link'],
                    [{ list: 'ordered' }, { list: 'bullet' }],
                ]
            },
            placeholder: 'Zde můžete zadat popis vzniku pojistné události',
            theme: 'snow'
        };

        const options2 = {
            modules: {
                toolbar: [
                    [{ header: [1, 2, false] }],
                    ['bold', 'italic', 'underline'],
                    ['image', 'blockquote', 'link'],
                    [{ list: 'ordered' }, { list: 'bullet' }],
                ]
            },
            placeholder: 'Zde můžete zadat Rozsah poškození',
            theme: 'snow'
        };

        this.quill = new Quill('#editorquillPopis', options1);
        this.quill2 = new Quill('#editorquillRozsah', options2);


        // Add form submit event listener
        this.formTarget.addEventListener('submit', this.handleSubmit.bind(this));
    }


    // Metoda pro zpracování odeslání formuláře
    handleSubmit(event) {
        event.preventDefault(); // Zastavení výchozího odeslání formuláře

        // Zde můžeš provést potřebné úpravy dat
        this.upravitDataFormulare();

        // Odeslání formuláře
        this.formTarget.submit();
    }

    // Metoda pro úpravu dat formuláře
    upravitDataFormulare() {

        const quillPopisContent = this.quill.getContents();
        this.textareaPopisvznikuTarget.value = JSON.stringify(quillPopisContent.ops);

        const quillRozsahContent = this.quill2.getContents();
        this.textareaRozsahposkozeniTarget.value = JSON.stringify(quillRozsahContent.ops);
    }

    // Metoda pro skrytí sekcí formuláře
    hideFormSections() {
        // Skrytí všech sekcí při inicializaci
        if (this.hasSectionPoskozenyTarget) {
            this.sectionPoskozenyTarget.classList.add('hidden');
        }

        if (this.hasSectionPoverenaOsobaTarget) {
            this.sectionPoverenaOsobaTarget.classList.add('hidden');
        }

        if (this.hasSectionKontaktniOsobaTarget) {
            this.sectionKontaktniOsobaTarget.classList.add('hidden');
        }
    }

    // Inicializace zobrazení polí podle checkboxů právnických osob
    initializeCompanyFields() {
        // Konfigurace pro inicializaci polí podle typu osoby
        const companyFieldsConfig = [
            { hasTarget: 'hasPojistenyIsCompanyTarget', toggleMethod: 'togglePojistenyCompanyFields' },
            { hasTarget: 'hasPoskozenyIsCompanyTarget', toggleMethod: 'togglePoskozenyCompanyFields' },
            { hasTarget: 'hasPoverenaOsobaIsCompanyTarget', toggleMethod: 'togglePoverenaOsobaCompanyFields' },
            { hasTarget: 'hasKontaktniOsobaIsCompanyTarget', toggleMethod: 'toggleKontaktniOsobaCompanyFields' }
        ];
        
        // Iterace přes konfiguraci
        companyFieldsConfig.forEach(config => {
            if (this[config.hasTarget]) {
                this[config.toggleMethod]();
            }
        });
    }

    // Inicializace stavu sekcí podle checkboxů
    initializeSections() {
        // Konfigurace pro inicializaci sekcí
        const sectionsConfig = [
            { 
                hasCheckboxTarget: 'hasCheckboxPoskozenyTarget', 
                hasSectionTarget: 'hasSectionPoskozenyTarget', 
                toggleMethod: 'toggleSectionPoskozeny' 
            },
            { 
                hasCheckboxTarget: 'hasCheckboxPoverenaOsobaTarget', 
                hasSectionTarget: 'hasSectionPoverenaOsobaTarget', 
                toggleMethod: 'toggleSectionPoverenaOsoba' 
            },
            { 
                hasCheckboxTarget: 'hasCheckboxKontaktniOsobaTarget', 
                hasSectionTarget: 'hasSectionKontaktniOsobaTarget', 
                toggleMethod: 'toggleSectionKontaktniOsoba' 
            }
        ];
        
        // Iterace přes konfiguraci
        sectionsConfig.forEach(config => {
            if (this[config.hasCheckboxTarget] && this[config.hasSectionTarget]) {
                this[config.toggleMethod]();
            }
        });
    }

    // Action pro zobrazení/skrytí sekce poškozeného
    toggleSectionPoskozeny() {
        if (this.hasCheckboxPoskozenyTarget && this.hasSectionPoskozenyTarget) {
            const isChecked = this.checkboxPoskozenyTarget.checked;
            this.sectionPoskozenyTarget.classList.toggle('hidden', !isChecked);
        }
    }

    // Action pro zobrazení/skrytí sekce pověřené osoby
    toggleSectionPoverenaOsoba() {
        if (this.hasCheckboxPoverenaOsobaTarget && this.hasSectionPoverenaOsobaTarget) {
            const isChecked = this.checkboxPoverenaOsobaTarget.checked;
            this.sectionPoverenaOsobaTarget.classList.toggle('hidden', !isChecked);
        }
    }

    // Action pro zobrazení/skrytí sekce kontaktní osoby
    toggleSectionKontaktniOsoba() {
        if (this.hasCheckboxKontaktniOsobaTarget && this.hasSectionKontaktniOsobaTarget) {
            const isChecked = this.checkboxKontaktniOsobaTarget.checked;
            this.sectionKontaktniOsobaTarget.classList.toggle('hidden', !isChecked);
        }
    }

    // Obecná metoda pro přepínání polí podle typu osoby (fyzická/právnická)
    toggleCompanyFields(type, target) {
        if (!target) {
            return;
        }
        
        const isCompany = target.checked;
        const personOnlyFields = this.element.querySelectorAll(`[data-${type}-person="true"]:not([data-${type}-company="true"])`);
        const companyOnlyFields = this.element.querySelectorAll(`[data-${type}-company="true"]:not([data-${type}-person="true"])`);
        const commonFields = this.element.querySelectorAll(`[data-${type}-person="true"][data-${type}-company="true"]`);
        
        personOnlyFields.forEach(f => f.classList.toggle('hidden', isCompany));
        companyOnlyFields.forEach(f => f.classList.toggle('hidden', !isCompany));
        commonFields.forEach(f => f.classList.remove('hidden'));
    }

    // Action pro přepínání polí Pojištěného podle typu osoby (fyzická/právnická)
    togglePojistenyCompanyFields() {
        this.toggleCompanyFields('pojisteny', this.pojistenyIsCompanyTarget);
    }

    // Action pro přepínání polí Poškozeného podle typu osoby (fyzická/právnická)
    togglePoskozenyCompanyFields() {
        this.toggleCompanyFields('poskozeny', this.poskozenyIsCompanyTarget);
    }

    // Action pro přepínání polí Pověřené osoby podle typu osoby (fyzická/právnická)
    togglePoverenaOsobaCompanyFields() {
        this.toggleCompanyFields('poverenaosoba', this.poverenaOsobaIsCompanyTarget);
    }

    // Action pro přepínání polí Kontaktní osoby podle typu osoby (fyzická/právnická)
    toggleKontaktniOsobaCompanyFields() {
        this.toggleCompanyFields('kontaktniosoba', this.kontaktniOsobaIsCompanyTarget);
    }

    kategorieChanged(event) {
        const selectedValue = event.target.value;
        const isOdpovednost = selectedValue === 'ODP';
        
        // Skrytí/zobrazení checkboxu
        this.checkboxPoskozenyWithLabelTarget.classList.toggle('hidden', isOdpovednost);
        
        // Automatické zaškrtnutí a zobrazení sekce při výběru Odpovědnost
        if (isOdpovednost && this.hasCheckboxPoskozenyTarget) {
            this.checkboxPoskozenyTarget.checked = true;
            this.toggleSectionPoskozeny();
        }
    }
}
