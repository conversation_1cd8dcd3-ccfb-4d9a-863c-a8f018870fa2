import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['roleSelect', 'isCompany'];

    connect() {
        // Skryjeme všechna pole kromě výběru role
        this.hideAllFields();
        
        // Pokud je již role vybr<PERSON>a, zobrazíme př<PERSON>lu<PERSON> pole
        if (this.roleSelectTarget.value) {
            this.handleRoleChange();
        }
    }

    hideAllFields() {
        // Skryjeme všechna pole kromě výběru role
        const formFields = this.element.querySelectorAll('.form-field');
        formFields.forEach(field => {
            // Neskrýváme pole s výběrem role
            if (!field.classList.contains('role-field')) {
                field.classList.add('hidden');
            }
        });
    }

    handleRoleChange() {
        const selectedRole = this.roleSelectTarget.value;
        
        // Převedeme vybranou roli na hodnotu pro data-role atribut
        const roleValue = this.getRoleValue(selectedRole);
        
        // Skryjeme všechna pole kromě výběru role
        this.hideAllFields();
        
        // Zobrazíme pole, kter<PERSON> mají odpovídající data-role atribut
        const formFields = this.element.querySelectorAll('.form-field');
        formFields.forEach(field => {
            const dataRole = field.getAttribute('data-role');
            
            // Pokud pole nemá data-role atribut nebo obsahuje vybranou roli, zobrazíme ho
            if (!dataRole || dataRole.includes(roleValue)) {
                field.classList.remove('hidden');
            }
        });
        
        // Pokud je zobrazeno pole isCompany, zkontrolujeme jeho hodnotu a podle toho zobrazíme/skryjeme sekci s informacemi o firmě
        if (this.hasIsCompanyTarget && !this.isCompanyTarget.closest('.form-field').classList.contains('hidden')) {
            this.toggleCompanyFields();
        }
    }
    
    toggleCompanyFields() {
        if (!this.hasIsCompanyTarget) {
            return;
        }
        
        const isCompany = this.isCompanyTarget.checked;
        const selectedRole = this.roleSelectTarget.value;
        const roleValue = this.getRoleValue(selectedRole);
        
        // Najdeme všechna pole s atributem data-person
        const personFields = this.element.querySelectorAll('[data-person="true"]');
        // Najdeme všechna pole s atributem data-company
        const companyFields = this.element.querySelectorAll('[data-company="true"]');
        
        // Zobrazíme nebo skryjeme pole podle toho, zda je checkbox isCompany zaškrtnutý nebo ne
        if (isCompany) {
            // Pokud je zaškrtnutý, skryjeme pole pro fyzickou osobu
            personFields.forEach(field => {
                field.classList.add('hidden');
            });
            
            // Zobrazíme pole pro firmu, ale pouze pokud mají odpovídající data-role
            companyFields.forEach(field => {
                const dataRole = field.getAttribute('data-role');
                // Zobrazíme pouze pokud pole nemá data-role nebo obsahuje vybranou roli
                if (!dataRole || dataRole.includes(roleValue)) {
                    field.classList.remove('hidden');
                } else {
                    field.classList.add('hidden');
                }
            });
        } else {
            // Pokud není zaškrtnutý, skryjeme pole pro firmu
            companyFields.forEach(field => {
                field.classList.add('hidden');
            });
            
            // Zobrazíme pole pro fyzickou osobu, ale pouze pokud mají odpovídající data-role
            personFields.forEach(field => {
                const dataRole = field.getAttribute('data-role');
                // Zobrazíme pouze pokud pole nemá data-role nebo obsahuje vybranou roli
                if (!dataRole || dataRole.includes(roleValue)) {
                    field.classList.remove('hidden');
                } else {
                    field.classList.add('hidden');
                }
            });
        }
        
        // Speciální logika pro platceDPH - zobrazit vždy pro Pojištěného a Poškozeného
        const platceDPHField = this.element.querySelector('.platce-dph-field');
        if (platceDPHField && (roleValue === 'pojisteny' || roleValue === 'poskozeny')) {
            platceDPHField.classList.remove('hidden');
        }
    }
    
    getRoleValue(role) {
        switch (role) {
            case 'Pojištěný':
                return 'pojisteny';
            case 'Poškozený':
                return 'poskozeny';
            case 'Viník':
                return 'vinik';
            case 'Pověřená osoba':
                return 'poverena-osoba';
            case 'Kontaktní osoba':
                return 'kontaktni-osoba';
            default:
                return '';
        }
    }
}
