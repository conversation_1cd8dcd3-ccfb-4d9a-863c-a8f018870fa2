import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    connect() {
        this.updateHeights();
        window.onresize = () => { this.updateHeights() };
    }

    updateHeights() {
        if (window.innerHeight <= 640 ) return;

        this.resetChildren();

        let height = 0;
        let children = this.element.children;
        for (let i = 0; i < children.length; i++) {
            let child = children[i];
            if (child.offsetHeight > height) {
                height = child.offsetHeight;
            }
        }

        for (let i = 0; i < children.length; i++) {
            let child = children[i];
            child.style.height = height + 'px';
        }
    }

    resetChildren() {
        let children = this.element.children;
        for (let i = 0; i < children.length; i++) {
            let child = children[i];
            child.style.height = 'auto';
        }
    }
};
