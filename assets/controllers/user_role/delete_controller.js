import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['deleteForm', 'roleName', 'roleToken', 'errorMessage', 'confirmButtons', 'modalTitle'];

    connect() {
        console.log('UserRole Delete Controller connected');
    }

    async deleteModalDialog(event) {
        event.preventDefault();
        
        const roleName = event.currentTarget.dataset.roleName;
        const roleToken = event.currentTarget.dataset.roleToken;
        const deleteUrl = event.currentTarget.dataset.deleteUrl;
        const roleId = deleteUrl.split('/').pop();
        
        // Set role name immediately
        this.roleNameTarget.textContent = roleName;
        
        try {
            // Check if the role can be deleted
            const response = await fetch(`/user/role/check-delete/${roleId}`);
            const data = await response.json();
            
            if (data.canDelete) {
                // Role can be deleted, show normal delete dialog
                this.roleTokenTarget.value = roleToken;
                this.deleteFormTarget.action = deleteUrl;
                
                // Hide error message and show confirm buttons
                this.errorMessageTarget.classList.add('hidden');
                this.confirmButtonsTarget.classList.remove('hidden');
                this.modalTitleTarget.textContent = 'Smazat roli';
                
                // Open the modal
                const modal = document.getElementById('deleteModal');
                if (modal._flowbiteModal) {
                    modal._flowbiteModal.show();
                }
            } else {
                // Role cannot be deleted, show error message
                this.errorMessageTarget.textContent = data.message;
                this.errorMessageTarget.classList.remove('hidden');
                this.confirmButtonsTarget.classList.add('hidden');
                this.modalTitleTarget.textContent = 'Nelze smazat roli';
                
                // Open the modal
                const modal = document.getElementById('deleteModal');
                if (modal._flowbiteModal) {
                    modal._flowbiteModal.show();
                }
            }
        } catch (error) {
            console.error('Error checking if role can be deleted:', error);
        }
    }
}
