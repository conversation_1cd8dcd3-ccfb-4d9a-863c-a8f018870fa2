import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['checkbox', 'customInput', 'pozadavky', 'customInputContainer', 'pozadavkyContainer']

    connect() {
        this.toggleCustomInput();
        this.checkboxTarget.addEventListener('change', this.toggleCustomInput.bind(this));

        this.pozadavkyTarget.addEventListener('change', this.handlePozadavkyChange.bind(this));

        this.handlePozadavkyChange();
    }

    handlePozadavkyChange() {
        const hasSelectedPozadavky = this.hasSelectedPozadavky();

        if (hasSelectedPozadavky) {
            this.checkboxTarget.checked = false;
            this.checkboxTarget.closest('.flex-1').classList.add('hidden');

            this.customInputContainerTarget.classList.add('hidden');
            this.pozadavkyContainerTarget.classList.remove('hidden');
            this.customInputTarget.value = '';
        } else {
            this.checkboxTarget.closest('.flex-1').classList.remove('hidden');
        }
    }

    hasSelectedPozadavky() {
        if (this.pozadavkyTarget.tagName === 'SELECT') {
            return Array.from(this.pozadavkyTarget.selectedOptions).length > 0;
        } else {
            const checkboxes = this.pozadavkyContainerTarget.querySelectorAll('input[type="checkbox"]');
            return Array.from(checkboxes).some(checkbox => checkbox.checked);
        }
    }

    toggleCustomInput() {
        const isCustom = this.checkboxTarget.checked;

        if (isCustom) {
            this.customInputContainerTarget.classList.remove('hidden');
            this.pozadavkyContainerTarget.classList.add('hidden');

            if (this.pozadavkyTarget.tagName === 'SELECT') {
                Array.from(this.pozadavkyTarget.options).forEach(option => {
                    option.selected = false;
                });
            } else {
                const checkboxes = this.pozadavkyContainerTarget.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
            }
        } else {
            this.customInputContainerTarget.classList.add('hidden');
            this.customInputTarget.value = '';
            this.pozadavkyContainerTarget.classList.remove('hidden');
        }
    }
}
