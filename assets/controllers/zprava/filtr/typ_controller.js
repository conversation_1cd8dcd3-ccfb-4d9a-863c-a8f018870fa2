import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['checkbox'];
    timer = null;
    selectedTypes = [];

    connect() {
        this.addTimerListeners();
    }

    addTimerListeners() {
        this.checkboxTargets.forEach((checkbox) => {
            checkbox.addEventListener('change', this.startTimer.bind(this));
        });
    }

    startTimer() {
        clearTimeout(this.timer);
        this.timer = setTimeout(this.submit.bind(this), 2000);
    }

    submit() {
        this.getTypes();

        var url = window.location.href.split('?')[0];
        window.location.href = url + '?typ=' + this.selectedTypes;
    }

    getTypes() {
        this.checkboxTargets.forEach((checkbox) => {
            var typeId = checkbox.getAttribute('type-id');
            if (checkbox.checked && !this.selectedTypes.includes(typeId)) {
                this.selectedTypes.push(typeId);
            }

            if (!checkbox.checked && this.selectedTypes.includes(typeId)) {
                this.selectedTypes = this.selectedTypes.filter((type) => type !== typeId);
            }
        });

        this.selectedTypes = [...new Set(this.selectedTypes)];
    }
}
