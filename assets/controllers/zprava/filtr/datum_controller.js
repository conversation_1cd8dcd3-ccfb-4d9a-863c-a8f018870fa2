import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['dateFrom', 'dateTo'];
    timer = null;
    dateFrom = null;
    dateTo = null;

    connect() {
        this.addTimerListeners();
    }

    addTimerListeners() {
        this.dateFromTarget.addEventListener('change', this.startTimer.bind(this));
        this.dateToTarget.addEventListener('change', this.startTimer.bind(this));
    }

    startTimer() {
        this.clearTimer();
        this.timer = setTimeout(this.submit.bind(this), 4000);

        this.dateFromTarget.addEventListener('click', this.startLongTimer.bind(this));
        this.dateToTarget.addEventListener('click', this.startLongTimer.bind(this));
    }

    startLongTimer() {
        this.clearTimer();
        this.timer = setTimeout(this.submit.bind(this), 8000);
    }

    clearTimer() {
        clearTimeout(this.timer);
    }

    submit() {
        this.getDates();

        var url = window.location.href.split('?')[0];

        if (this.dateFrom && this.dateTo) {
            window.location.href = url + '?od=' + this.dateFrom + '&do=' + this.dateTo;
            return;
        }

        if (this.dateFrom) {
            window.location.href = url + '?od=' + this.dateFrom;
            return;
        }

        if (this.dateTo) {
            window.location.href = url + '?do=' + this.dateTo;
            return;
        }
    }

    getDates() {
        this.dateFrom = this.dateFromTarget.value;
        this.dateTo = this.dateToTarget.value;
    }
}
