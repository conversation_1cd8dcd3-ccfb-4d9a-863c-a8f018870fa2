import { NotificationHelper } from './notification_helper.js'

/**
 * Centrální třída pro standardizované error handling v file browser komponentách
 * 
 * Kategorie error handlingu:
 * - KRITICKÉ: API operace, navigace - vždy user feedback
 * - NEKRITICKÉ: localStorage, DOM targets - jen console log
 * - VALIDAČNÍ: input validation, state validation - preventivní
 */
export class ErrorHandler {
    
    /**
     * Zpracování kritických operací s povinným user feedback
     * @param {Function} operation - Async operace k provedení
     * @param {Object} context - Kontext s metadaty
     * @returns {Promise<any>} - Výsledek operace
     */
    static async handleCriticalOperation(operation, context = {}) {
        const operationName = context.operation || 'Critical Operation'
        
        try {
            console.log(`[ErrorHandler] Starting critical operation: ${operationName}`)
            
            const result = await operation()
            
            // Zobrazíme success notifikaci pokud je definována
            if (context.successMessage) {
                NotificationHelper.showSuccess(context.successMessage)
                console.log(`[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>] ✅ ${operationName} successful`)
            }
            
            return result
            
        } catch (error) {
            console.error(`[Error<PERSON>and<PERSON>] ❌ ${operationName} failed:`, error)
            
            // Zobrazíme error notifikaci uživateli
            const errorMessage = context.errorMessage || `Chyba při operaci "${operationName}": ${error.message}`
            NotificationHelper.showError(errorMessage)
            
            // Re-throw error pro další zpracování pokud potřeba
            throw error
        }
    }

    /**
     * Zpracování nekritických operací - jen console log, bez user feedback
     * @param {Function} operation - Operace k provedení
     * @param {Object} context - Kontext s metadaty
     * @returns {any} - Výsledek operace nebo fallback hodnota
     */
    static handleNonCritical(operation, context = {}) {
        const operationName = context.operation || 'Non-Critical Operation'
        
        try {
            const result = operation()
            console.log(`[ErrorHandler] ✅ ${operationName} successful`)
            return result
            
        } catch (error) {
            console.warn(`[ErrorHandler] ⚠️ ${operationName} failed:`, error)
            
            // Vrátíme fallback hodnotu pokud je definována
            if (context.fallback !== undefined) {
                console.log(`[ErrorHandler] Using fallback for ${operationName}:`, context.fallback)
                return context.fallback
            }
            
            return null
        }
    }

    /**
     * Validace povinných hodnot
     * @param {any} value - Hodnota k validaci
     * @param {string} fieldName - Název pole pro error message
     * @param {Object} context - Dodatečný kontext
     * @returns {boolean} - True pokud je hodnota validní
     */
    static validateRequired(value, fieldName, context = {}) {
        if (value === null || value === undefined || value === '') {
            const message = `Validation failed: ${fieldName} is required`
            console.warn(`[ErrorHandler] ⚠️ ${message}`, context)
            return false
        }
        return true
    }

    /**
     * Validace DOM elementů
     * @param {HTMLElement} element - Element k validaci
     * @param {string} elementName - Název elementu pro error message
     * @returns {boolean} - True pokud element existuje
     */
    static validateDOMElement(element, elementName) {
        if (!element) {
            console.warn(`[ErrorHandler] ⚠️ DOM element not found: ${elementName}`)
            return false
        }
        return true
    }

    /**
     * Validace API response
     * @param {Response} response - Fetch response
     * @param {Object} data - Parsed JSON data
     * @returns {boolean} - True pokud response je validní
     */
    static validateAPIResponse(response, data) {
        if (!response.ok) {
            const error = new Error(data?.error || data?.message || `HTTP ${response.status}: ${response.statusText}`)
            error.status = response.status
            error.statusText = response.statusText
            throw error
        }
        
        if (data?.statusCode && data.statusCode !== 200) {
            const error = new Error(data.error || data.message || 'API returned error status')
            error.statusCode = data.statusCode
            throw error
        }
        
        return true
    }

    /**
     * Wrapper pro fetch operace s automatickým error handlingem
     * @param {string} url - URL pro fetch
     * @param {Object} options - Fetch options
     * @param {Object} context - Error handling kontext
     * @returns {Promise<any>} - Parsed JSON response
     */
    static async fetchWithErrorHandling(url, options = {}, context = {}) {
        return await this.handleCriticalOperation(async () => {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    ...options.headers
                },
                ...options
            })
            
            const data = await response.json()
            this.validateAPIResponse(response, data)
            
            return data
            
        }, context)
    }

    /**
     * Zpracování drag & drop validačních chyb
     * @param {string} validationType - Typ validace
     * @param {Object} context - Kontext s detaily
     * @returns {boolean} - True pokud validace prošla
     */
    static validateDragDrop(validationType, context = {}) {
        switch (validationType) {
            case 'draggedFile':
                if (!context.draggedFile) {
                    console.warn('[ErrorHandler] ⚠️ Drag validation failed: No dragged file')
                    return false
                }
                break
                
            case 'sameController':
                if (context.sourceController === context.targetController) {
                    console.warn('[ErrorHandler] ⚠️ Drag validation failed: Cannot drop into same controller')
                    return false
                }
                break
                
            case 'validTarget':
                if (!context.targetController || !context.targetController.hasContentTarget) {
                    console.warn('[ErrorHandler] ⚠️ Drag validation failed: Invalid target controller')
                    return false
                }
                break
                
            default:
                console.warn(`[ErrorHandler] ⚠️ Unknown drag validation type: ${validationType}`)
                return false
        }
        
        return true
    }

    /**
     * Zpracování localStorage operací
     * @param {Function} operation - localStorage operace
     * @param {string} key - localStorage klíč
     * @param {any} fallback - Fallback hodnota
     * @returns {any} - Výsledek nebo fallback
     */
    static handleLocalStorage(operation, key, fallback = null) {
        return this.handleNonCritical(operation, {
            operation: `localStorage.${operation.name || 'operation'}`,
            fallback: fallback
        })
    }

    /**
     * Debug logging s kategorizací
     * @param {string} level - Log level (info, warn, error)
     * @param {string} operation - Název operace
     * @param {string} message - Zpráva
     * @param {any} data - Dodatečná data
     */
    static log(level, operation, message, data = null) {
        const prefix = `[ErrorHandler:${operation}]`
        
        switch (level) {
            case 'info':
                console.log(`${prefix} ℹ️ ${message}`, data || '')
                break
            case 'warn':
                console.warn(`${prefix} ⚠️ ${message}`, data || '')
                break
            case 'error':
                console.error(`${prefix} ❌ ${message}`, data || '')
                break
            default:
                console.log(`${prefix} ${message}`, data || '')
        }
    }
}
