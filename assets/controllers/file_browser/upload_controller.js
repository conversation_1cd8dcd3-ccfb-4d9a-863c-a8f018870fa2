import Dropzone from 'dropzone'
import 'dropzone/dist/dropzone.css'

/**
 * Třída pro správu upload funkcionalit (Dropzone)
 */
export class UploadController {
    
    constructor(controller) {
        this.controller = controller
        this.dropzone = null
    }

    /**
     * Inicializuje Dropzone
     */
    initializeDropzone() {
        if (!this.controller.hasDropzoneFormTarget || this.dropzone) {
            return
        }

        console.log("Initializing Dropzone")
        
        const controller = this.controller
        this.dropzone = new Dropzone(this.controller.dropzoneFormTarget, {
            url: this.controller.uploadApiUrlValue,
            paramName: 'file',
            maxFilesize: 2,
            acceptedFiles: '.pdf,.doc,.docx,.jpg,.jpeg,.png,.gif',
            addRemoveLinks: true,
            dictDefaultMessage: 'Přetáhněte soubory sem nebo klikněte pro výběr',
            dictRemoveFile: 'Odstranit',
            dictCancelUpload: 'Zrušit nahrávání',
            dictFileTooBig: 'Soubor je př<PERSON>li<PERSON> velk<PERSON> ({{filesize}}MB). Maximální velikost: {{maxFilesize}}MB.',
            dictInvalidFileType: 'Tento typ souboru není povolen.',
            
            init: function() {
                this.on('success', function(file, response) {
                    console.log('Soubor úspěšně nahrán:', response)
                    
                    // Importujeme NotificationHelper dynamicky
                    import('./notification_helper.js').then(({ NotificationHelper }) => {
                        NotificationHelper.showSuccess(`Soubor "${file.name}" byl úspěšně nahrán`)
                    })
                    
                    // Automaticky obnovíme obsah složky
                    setTimeout(() => {
                        controller.refresh()
                    }, 1000)
                })

                this.on('error', function(file, errorMessage) {
                    console.error('Chyba při nahrávání:', errorMessage)
                    const message = typeof errorMessage === 'string' ? errorMessage : errorMessage.error || 'Nepodařilo se nahrát soubor'
                    
                    // Importujeme NotificationHelper dynamicky
                    import('./notification_helper.js').then(({ NotificationHelper }) => {
                        NotificationHelper.showError(`Chyba při nahrávání "${file.name}": ${message}`)
                    })
                })

                this.on('addedfile', function(file) {
                    console.log('Soubor přidán:', file.name)
                })

                this.on('complete', function(file) {
                    if (this.getUploadingFiles().length === 0 && this.getQueuedFiles().length === 0) {
                        console.log('Všechny soubory byly nahrány')
                    }
                })
            }
        })
    }

    /**
     * Zobrazí upload zónu
     * @param {Event} event 
     */
    showUpload(event) {
        event.preventDefault()
        console.log("Showing upload zone")
        
        if (this.controller.hasUploadZoneTarget) {
            this.controller.uploadZoneTarget.classList.remove('hidden')
            
            // Inicializujeme Dropzone pokud ještě není
            if (!this.dropzone) {
                this.initializeDropzone()
            }
        }
    }

    /**
     * Skryje upload zónu
     * @param {Event} event 
     */
    hideUpload(event) {
        event.preventDefault()
        console.log("Hiding upload zone")
        
        if (this.controller.hasUploadZoneTarget) {
            this.controller.uploadZoneTarget.classList.add('hidden')
        }
    }

    /**
     * Getter pro dropzone instanci
     */
    getDropzone() {
        return this.dropzone
    }
}
