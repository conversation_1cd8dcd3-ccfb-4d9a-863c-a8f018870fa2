import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './error_handler.js'

/**
 * <PERSON><PERSON>r<PERSON><PERSON><PERSON> drag & drop funkcionalit pro file browser komponenty
 * 
 * KROK 2: Drag Detection
 * - Detekce začátku drag operace na souborech
 * - Registrace file browser instancí
 * - Console logy pro debugging
 * - Zatím bez drop handling
 */
export class DragDropManager {
    
    constructor() {
        // Registry všech file browser instancí na stránce
        this.fileBrowserInstances = new Map()
        
        // Aktuálně tažený soubor
        this.draggedFile = null
        
        // Debug mode
        this.debugMode = true
        
        this.log("🎯 DragDropManager initialized")
    }

    /**
     * Registrace file browser instance
     * @param {string} instanceId - Unikátní ID instance (např. slozka ID)
     * @param {Object} controller - File browser controller instance
     */
    registerFileBrowser(instanceId, controller) {
        this.fileBrowserInstances.set(instanceId, controller)
        this.log(`📝 Registered file browser instance: ${instanceId}`)
        
        // Inicializace drag events pro tuto instanci
        this.initializeDragEvents(controller)
    }

    /**
     * Odregistrování file browser instance
     * @param {string} instanceId 
     */
    unregisterFileBrowser(instanceId) {
        if (this.fileBrowserInstances.has(instanceId)) {
            this.fileBrowserInstances.delete(instanceId)
            this.log(`🗑️ Unregistered file browser instance: ${instanceId}`)
        }
    }

    /**
     * Inicializace drag events pro file browser instanci
     * @param {Object} controller - File browser controller
     */
    initializeDragEvents(controller) {
        // Validace controlleru pomocí ErrorHandler
        if (!ErrorHandler.validateDOMElement(controller.contentTarget, 'controller.contentTarget')) {
            return
        }

        const contentElement = controller.contentTarget
        
        // Event delegation pro soubory - použijeme event delegation
        // protože obsah se dynamicky mění při navigaci
        contentElement.addEventListener('dragstart', (event) => {
            this.handleDragStart(event, controller)
        })

        contentElement.addEventListener('dragend', (event) => {
            this.handleDragEnd(event, controller)
        })

        // Drop events pro cílové file browsery
        contentElement.addEventListener('dragover', (event) => {
            this.handleDragOver(event, controller)
        })

        contentElement.addEventListener('dragenter', (event) => {
            this.handleDragEnter(event, controller)
        })

        contentElement.addEventListener('dragleave', (event) => {
            this.handleDragLeave(event, controller)
        })

        contentElement.addEventListener('drop', (event) => {
            this.handleDrop(event, controller)
        })

        this.log(`🎪 Drag events initialized for controller with slozka ID: ${controller.slozkaIdValue}`)
    }

    /**
     * Handler pro začátek drag operace
     * @param {DragEvent} event 
     * @param {Object} controller 
     */
    handleDragStart(event, controller) {
        // Kontrola, že se táhne soubor (ne složka)
        const fileElement = this.findFileElement(event.target)
        
        if (!ErrorHandler.validateDOMElement(fileElement, 'draggable file element')) {
            return
        }

        const fileId = fileElement.dataset.souborId
        if (!ErrorHandler.validateRequired(fileId, 'fileElement.dataset.souborId')) {
            return
        }

        // Uložíme informace o tažené položce
        this.draggedFile = {
            id: parseInt(fileId),
            element: fileElement,
            sourceController: controller,
            sourceSlozkaId: controller.currentFolderId || controller.slozkaIdValue
        }

        // Nastavíme drag data
        event.dataTransfer.setData('text/plain', fileId)
        event.dataTransfer.effectAllowed = 'move'

        // Visual feedback - přidáme CSS třídu
        fileElement.classList.add('dragging')

        this.log(`🚀 Drag started:`, {
            fileId: this.draggedFile.id,
            sourceSlozkaId: this.draggedFile.sourceSlozkaId,
            fileName: this.getFileName(fileElement)
        })
    }

    /**
     * Handler pro konec drag operace
     * @param {DragEvent} event 
     * @param {Object} controller 
     */
    handleDragEnd(event, controller) {
        if (!this.draggedFile) {
            return
        }

        // Odebereme visual feedback
        this.draggedFile.element.classList.remove('dragging')

        this.log(`🏁 Drag ended for file ID: ${this.draggedFile.id}`)

        // Reset stavu
        this.draggedFile = null
    }

    // === DROP HANDLERS (KROK 3) ===

    /**
     * Handler pro dragover - povoluje drop
     * @param {DragEvent} event 
     * @param {Object} controller 
     */
    handleDragOver(event, controller) {
        // Kontrola, že máme tažený soubor
        if (!this.draggedFile) {
            return
        }

        // Kontrola, že se nepokoušíme dropnout do stejného file browseru
        if (this.draggedFile.sourceController === controller) {
            this.log(`⚠️ Cannot drop into same file browser`)
            return
        }

        // Povolíme drop
        event.preventDefault()
        event.dataTransfer.dropEffect = 'move'

        // Throttle logging pro dragover (spouští se velmi často)
        if (!this.lastDragOverLog || Date.now() - this.lastDragOverLog > 500) {
            this.log(`🎯 Drag over target folder: ${controller.currentFolderId || controller.slozkaIdValue}`)
            this.lastDragOverLog = Date.now()
        }
    }

    /**
     * Handler pro dragenter - vstup do drop zone
     * @param {DragEvent} event 
     * @param {Object} controller 
     */
    handleDragEnter(event, controller) {
        if (!this.draggedFile) {
            return
        }

        // Kontrola, že se nepokoušíme dropnout do stejného file browseru
        if (this.draggedFile.sourceController === controller) {
            return
        }

        event.preventDefault()
        
        // Visual feedback - zvýrazníme drop zone
        this.highlightDropZone(controller, true)
        
        this.log(`🎪 Drag entered drop zone: ${controller.currentFolderId || controller.slozkaIdValue}`)
    }

    /**
     * Handler pro dragleave - opuštění drop zone
     * @param {DragEvent} event 
     * @param {Object} controller 
     */
    handleDragLeave(event, controller) {
        if (!this.draggedFile) {
            return
        }

        // Kontrola, že opravdu opouštíme drop zone (ne jen přechod mezi child elementy)
        if (event.relatedTarget && controller.contentTarget.contains(event.relatedTarget)) {
            return
        }

        // Odebereme visual feedback
        this.highlightDropZone(controller, false)
        
        this.log(`🚪 Drag left drop zone: ${controller.currentFolderId || controller.slozkaIdValue}`)
    }

    /**
     * Handler pro drop - dokončení drag & drop operace
     * @param {DragEvent} event 
     * @param {Object} controller 
     */
    handleDrop(event, controller) {
        event.preventDefault()
        
        if (!this.draggedFile) {
            this.log(`⚠️ No dragged file found on drop`)
            return
        }

        // Kontrola, že se nepokoušíme dropnout do stejného file browseru
        if (this.draggedFile.sourceController === controller) {
            this.log(`⚠️ Cannot drop into same file browser`)
            this.highlightDropZone(controller, false)
            return
        }

        const targetSlozkaId = controller.currentFolderId || controller.slozkaIdValue
        
        this.log(`🎯 Drop detected:`, {
            fileId: this.draggedFile.id,
            sourceSlozkaId: this.draggedFile.sourceSlozkaId,
            targetSlozkaId: targetSlozkaId,
            fileName: this.getFileName(this.draggedFile.element)
        })

        // Odebereme visual feedback
        this.highlightDropZone(controller, false)
        
        // KROK 4: API volání pro přesunutí souboru
        this.performFileMove(this.draggedFile.id, targetSlozkaId, controller)
    }

    /**
     * Zvýrazní nebo zruší zvýraznění drop zone
     * @param {Object} controller 
     * @param {boolean} highlight 
     */
    highlightDropZone(controller, highlight) {
        if (!controller.hasContentTarget) {
            return
        }

        const contentElement = controller.contentTarget
        
        if (highlight) {
            contentElement.classList.add('drop-zone-active')
            this.log(`🌟 Drop zone highlighted`)
        } else {
            contentElement.classList.remove('drop-zone-active')
            this.log(`🌑 Drop zone unhighlighted`)
        }
    }

    /**
     * Najde element souboru v DOM hierarchii
     * @param {HTMLElement} target 
     * @returns {HTMLElement|null}
     */
    findFileElement(target) {
        // Hledáme element s data-soubor-id atributem
        let element = target
        
        while (element && element !== document.body) {
            if (element.dataset && element.dataset.souborId) {
                // Kontrola, že element je draggable
                if (element.draggable || element.getAttribute('draggable') === 'true') {
                    return element
                }
            }
            element = element.parentElement
        }
        
        return null
    }

    /**
     * Získá název souboru z elementu
     * @param {HTMLElement} fileElement 
     * @returns {string}
     */
    getFileName(fileElement) {
        // Pokusíme se najít název souboru v elementu
        const nameElement = fileElement.querySelector('[title]')
        if (nameElement) {
            return nameElement.getAttribute('title')
        }
        
        // Fallback - hledáme text content
        const textElement = fileElement.querySelector('.font-medium, .text-gray-900')
        if (textElement) {
            return textElement.textContent.trim()
        }
        
        return 'Neznámý soubor'
    }

    /**
     * Debug logging
     * @param {string} message 
     * @param {Object} data 
     */
    log(message, data = null) {
        if (this.debugMode) {
            if (data) {
                console.log(`[DragDropManager] ${message}`, data)
            } else {
                console.log(`[DragDropManager] ${message}`)
            }
        }
    }

    /**
     * Getter pro aktuálně tažený soubor
     * @returns {Object|null}
     */
    getDraggedFile() {
        return this.draggedFile
    }

    /**
     * Getter pro počet registrovaných instancí
     * @returns {number}
     */
    getInstanceCount() {
        return this.fileBrowserInstances.size
    }

    // === API INTEGRATION (KROK 4) ===

    /**
     * Provede API volání pro přesunutí souboru
     * @param {number} fileId - ID souboru
     * @param {number} targetSlozkaId - ID cílové složky
     * @param {Object} targetController - Controller cílového file browseru
     */
    async performFileMove(fileId, targetSlozkaId, targetController) {
        const sourceController = this.draggedFile.sourceController
        const fileName = this.getFileName(this.draggedFile.element)
        
        this.log(`🚀 Starting API call to move file ${fileId} to folder ${targetSlozkaId}`)
        
        try {
            // Zobrazíme loading state
            this.showMoveLoading(true)
            
            // Sestavíme API URL
            const apiUrl = `/api/internal/v1/soubor/${fileId}/move-to-folder/${targetSlozkaId}`
            
            // Zavoláme API pomocí ErrorHandler
            const data = await ErrorHandler.fetchWithErrorHandling(apiUrl, {
                method: 'POST'
            }, {
                operation: 'moveFile',
                successMessage: `Soubor "${fileName}" byl úspěšně přesunut`,
                errorMessage: `Nepodařilo se přesunout soubor "${fileName}"`
            })
            
            // Úspěch - refresh obou file browserů
            this.log(`✅ File move successful:`, data)
            await this.refreshFileBrowsers(sourceController, targetController)
            
        } catch (error) {
            this.log(`❌ File move failed:`, error)
            // ErrorHandler už zobrazil error notifikaci
            
        } finally {
            // Skryjeme loading state
            this.showMoveLoading(false)
        }
    }

    /**
     * Zobrazí/skryje loading state během API volání
     * @param {boolean} show 
     */
    showMoveLoading(show) {
        // TODO: Implementovat loading overlay nebo spinner
        // Prozatím jen console log
        if (show) {
            this.log(`⏳ Loading: Moving file...`)
        } else {
            this.log(`⏳ Loading finished`)
        }
    }

    /**
     * Zobrazí success notifikaci
     * @param {string} fileName 
     * @param {Object} moveData 
     */
    showMoveSuccess(fileName, moveData) {
        // Dynamicky importujeme NotificationHelper
        import('./notification_helper.js').then(({ NotificationHelper }) => {
            const message = `Soubor "${fileName}" byl úspěšně přesunut`
            NotificationHelper.showSuccess(message)
            this.log(`✅ Success notification shown: ${message}`)
        }).catch(error => {
            this.log(`⚠️ Could not load NotificationHelper:`, error)
        })
    }

    /**
     * Zobrazí error notifikaci
     * @param {string} fileName 
     * @param {string} errorMessage 
     */
    showMoveError(fileName, errorMessage) {
        // Dynamicky importujeme NotificationHelper
        import('./notification_helper.js').then(({ NotificationHelper }) => {
            const message = `Nepodařilo se přesunout soubor "${fileName}": ${errorMessage}`
            NotificationHelper.showError(message)
            this.log(`❌ Error notification shown: ${message}`)
        }).catch(error => {
            this.log(`⚠️ Could not load NotificationHelper:`, error)
        })
    }

    /**
     * Refresh obou file browserů po úspěšném přesunu
     * @param {Object} sourceController 
     * @param {Object} targetController 
     */
    async refreshFileBrowsers(sourceController, targetController) {
        this.log(`🔄 Refreshing file browsers after successful move`)
        
        try {
            // Refresh source file browser (odkud byl soubor přesunut)
            if (sourceController && typeof sourceController.refresh === 'function') {
                await sourceController.refresh()
                this.log(`✅ Source file browser refreshed`)
            }
            
            // Refresh target file browser (kam byl soubor přesunut)
            if (targetController && typeof targetController.refresh === 'function') {
                await targetController.refresh()
                this.log(`✅ Target file browser refreshed`)
            }
            
        } catch (error) {
            this.log(`⚠️ Error refreshing file browsers:`, error)
        }
    }
}

// Globální instance pro celou aplikaci
export const globalDragDropManager = new DragDropManager()
