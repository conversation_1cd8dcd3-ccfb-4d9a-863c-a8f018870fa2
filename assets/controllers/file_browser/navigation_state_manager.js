import { ErrorHand<PERSON> } from './error_handler.js'

/**
 * Spr<PERSON><PERSON>ce stavu navigace pro file browser s podporou transakcí a race condition prevence
 * 
 * Funkce:
 * - Transactional navigation (atomické operace)
 * - Cancel concurrent navigation (jen nejn<PERSON><PERSON><PERSON><PERSON><PERSON> navigace)
 * - Loading states a user feedback
 * - Rollback při ch<PERSON>
 * - State validation
 */
export class NavigationStateManager {
    
    constructor(controller) {
        this.controller = controller
        
        // Navigation state
        this.isNavigating = false
        this.currentNavigation = null
        this.navigationHistory = []
        
        // State tracking
        this.currentState = null
        this.previousState = null
        
        // Configuration
        this.maxHistorySize = 10
        this.navigationTimeout = 30000 // 30 sekund
        
        ErrorHandler.log('info', 'NavigationStateManager', 'Initialized')
    }

    /**
     * Hlavní metoda pro navigaci do složky s kompletní správou stavu
     * @param {number} folderId - ID cílové složky
     * @returns {Promise<void>}
     */
    async navigateToFolder(folderId) {
        ErrorHandler.log('info', 'navigation', `Starting navigation to folder: ${folderId}`)
        
        // Validace vstupu
        if (!ErrorHandler.validateRequired(folderId, 'folderId')) {
            throw new Error('Invalid folder ID')
        }
        
        // Kontrola, že se nenavigujeme do stejné složky
        if (folderId === this.controller.currentFolderId) {
            ErrorHandler.log('info', 'navigation', 'Already in target folder, skipping navigation')
            return
        }
        
        // Cancel předchozí navigaci pokud běží
        if (this.currentNavigation) {
            ErrorHandler.log('warn', 'navigation', 'Cancelling previous navigation')
            this.cancelCurrentNavigation()
        }
        
        // Vytvoř novou navigační transakci
        const navigation = this.createNavigation(folderId)
        this.currentNavigation = navigation
        
        try {
            await this.executeNavigation(navigation)
            ErrorHandler.log('info', 'navigation', `Navigation to folder ${folderId} completed successfully`)
            
        } catch (error) {
            ErrorHandler.log('error', 'navigation', `Navigation to folder ${folderId} failed`, error)
            
            // Pokud navigace nebyla zrušena, proveď rollback
            if (!navigation.cancelled) {
                await this.rollbackNavigation(navigation)
            }
            
            throw error
            
        } finally {
            // Cleanup
            if (this.currentNavigation === navigation) {
                this.currentNavigation = null
            }
        }
    }

    /**
     * Vytvoří novou navigační transakci
     * @param {number} folderId - ID cílové složky
     * @returns {Object} Navigation transaction object
     */
    createNavigation(folderId) {
        const navigation = {
            id: `nav_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            targetFolderId: folderId,
            startTime: Date.now(),
            cancelled: false,
            backup: null,
            abortController: new AbortController()
        }
        
        // Timeout pro navigaci
        setTimeout(() => {
            if (!navigation.cancelled && this.currentNavigation === navigation) {
                ErrorHandler.log('warn', 'navigation', `Navigation ${navigation.id} timed out`)
                this.cancelNavigation(navigation)
            }
        }, this.navigationTimeout)
        
        ErrorHandler.log('info', 'navigation', `Created navigation transaction: ${navigation.id}`)
        return navigation
    }

    /**
     * Provede navigační transakci
     * @param {Object} navigation - Navigation transaction
     */
    async executeNavigation(navigation) {
        if (navigation.cancelled) {
            throw new Error('Navigation was cancelled')
        }
        
        this.isNavigating = true
        
        try {
            // 1. Backup současného stavu
            navigation.backup = this.captureCurrentState()
            ErrorHandler.log('info', 'navigation', 'Current state backed up')
            
            // 2. Zobraz loading state
            this.showNavigationLoading(true)
            
            // 3. Proveď state změny atomicky
            await this.applyNavigationState(navigation)
            
            // 4. Validuj úspěch
            this.validateNavigationSuccess(navigation)
            
            // 5. Commit transakci
            this.commitNavigation(navigation)
            
        } finally {
            this.isNavigating = false
            this.showNavigationLoading(false)
        }
    }

    /**
     * Zachytí současný stav pro možný rollback
     * @returns {Object} Current state snapshot
     */
    captureCurrentState() {
        return {
            currentFolderId: this.controller.currentFolderId,
            rootFolderId: this.controller.rootFolderId,
            folderApiUrl: this.controller.folderApiUrlValue,
            uploadApiUrl: this.controller.uploadApiUrlValue,
            url: window.location.href,
            lastFolderData: this.controller.lastFolderData,
            timestamp: Date.now()
        }
    }

    /**
     * Aplikuje nový navigační stav
     * @param {Object} navigation - Navigation transaction
     */
    async applyNavigationState(navigation) {
        if (navigation.cancelled) {
            throw new Error('Navigation was cancelled')
        }
        
        const { targetFolderId } = navigation
        
        // 1. Aktualizuj current folder ID
        this.controller.currentFolderId = targetFolderId
        ErrorHandler.log('info', 'navigation', `Updated currentFolderId to: ${targetFolderId}`)
        
        // 2. Aktualizuj URL
        this.controller.updateUrlWithCurrentFolder()
        ErrorHandler.log('info', 'navigation', 'URL updated')
        
        // 3. Aktualizuj API URLs
        this.controller.updateApiUrlForCurrentFolder()
        ErrorHandler.log('info', 'navigation', 'API URLs updated')
        
        // 4. Načti obsah nové složky
        await this.refreshFolderContent(navigation)
        
        // 5. Aktualizuj navigační UI
        this.controller.updateNavigationUI()
        ErrorHandler.log('info', 'navigation', 'Navigation UI updated')
    }

    /**
     * Načte obsah složky s podporou cancellation
     * @param {Object} navigation - Navigation transaction
     */
    async refreshFolderContent(navigation) {
        if (navigation.cancelled) {
            throw new Error('Navigation was cancelled')
        }
        
        try {
            // Použij ErrorHandler pro API volání s abort signal
            const url = new URL(this.controller.folderApiUrlValue, window.location.origin)
            url.searchParams.set('sort', this.controller.currentSort.field)
            url.searchParams.set('order', this.controller.currentSort.order)
            
            const data = await ErrorHandler.fetchWithErrorHandling(url.toString(), {
                signal: navigation.abortController.signal
            }, {
                operation: 'refreshFolderContent',
                errorMessage: 'Nepodařilo se načíst obsah složky'
            })
            
            if (navigation.cancelled) {
                throw new Error('Navigation was cancelled')
            }
            
            // Aktualizuj obsah bez success notifikace (navigace má vlastní feedback)
            // API vrací {statusCode, status, message, data}, ale updateFolderContent očekává jen data část
            const folderData = data.data || data
            folderData.showSuccessMessage = false
            this.controller.updateFolderContent(folderData)
            
            ErrorHandler.log('info', 'navigation', 'Folder content refreshed successfully')
            
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Navigation was cancelled')
            }
            throw error
        }
    }

    /**
     * Validuje úspěch navigace
     * @param {Object} navigation - Navigation transaction
     */
    validateNavigationSuccess(navigation) {
        const { targetFolderId } = navigation
        
        // Kontrola konzistence stavu
        const urlFolderId = this.getfolderIdFromUrl()
        const currentFolderId = this.controller.currentFolderId
        
        if (currentFolderId !== targetFolderId) {
            throw new Error(`State inconsistency: currentFolderId (${currentFolderId}) !== targetFolderId (${targetFolderId})`)
        }
        
        if (urlFolderId !== null && urlFolderId !== targetFolderId) {
            throw new Error(`URL inconsistency: URL folder (${urlFolderId}) !== targetFolderId (${targetFolderId})`)
        }
        
        ErrorHandler.log('info', 'navigation', 'Navigation state validation passed')
    }

    /**
     * Commitne úspěšnou navigaci
     * @param {Object} navigation - Navigation transaction
     */
    commitNavigation(navigation) {
        // Aktualizuj state tracking
        this.previousState = this.currentState
        this.currentState = this.captureCurrentState()
        
        // Přidej do historie
        this.addToNavigationHistory(navigation)
        
        ErrorHandler.log('info', 'navigation', `Navigation ${navigation.id} committed successfully`)
    }

    /**
     * Provede rollback navigace při chybě
     * @param {Object} navigation - Navigation transaction
     */
    async rollbackNavigation(navigation) {
        if (!navigation.backup) {
            ErrorHandler.log('error', 'navigation', 'Cannot rollback: no backup state available')
            return
        }
        
        ErrorHandler.log('warn', 'navigation', `Rolling back navigation ${navigation.id}`)
        
        try {
            const backup = navigation.backup
            
            // Obnov stav
            this.controller.currentFolderId = backup.currentFolderId
            this.controller.folderApiUrlValue = backup.folderApiUrl
            this.controller.uploadApiUrlValue = backup.uploadApiUrl
            
            // Obnov URL
            window.history.replaceState({}, '', backup.url)
            
            // Obnov obsah pokud existuje
            if (backup.lastFolderData) {
                this.controller.updateFolderContent(backup.lastFolderData)
            }
            
            // Aktualizuj UI
            this.controller.updateNavigationUI()
            
            ErrorHandler.log('info', 'navigation', 'Navigation rollback completed')
            
        } catch (rollbackError) {
            ErrorHandler.log('error', 'navigation', 'Rollback failed, forcing page reload', rollbackError)
            
            // Kritická chyba - force reload
            window.location.reload()
        }
    }

    /**
     * Zruší současnou navigaci
     */
    cancelCurrentNavigation() {
        if (this.currentNavigation) {
            this.cancelNavigation(this.currentNavigation)
        }
    }

    /**
     * Zruší konkrétní navigaci
     * @param {Object} navigation - Navigation transaction
     */
    cancelNavigation(navigation) {
        if (navigation.cancelled) {
            return
        }
        
        navigation.cancelled = true
        navigation.abortController.abort()
        
        ErrorHandler.log('warn', 'navigation', `Navigation ${navigation.id} cancelled`)
    }

    /**
     * Zobrazí/skryje loading state během navigace
     * @param {boolean} show - True pro zobrazení loading
     */
    showNavigationLoading(show) {
        if (show) {
            // Zobraz loading overlay
            this.controller.showLoading()
            
            // Disable navigation controls
            this.disableNavigationControls()
            
            ErrorHandler.log('info', 'navigation', 'Navigation loading state shown')
            
        } else {
            // Skryj loading overlay
            this.controller.hideLoading()
            
            // Enable navigation controls
            this.enableNavigationControls()
            
            ErrorHandler.log('info', 'navigation', 'Navigation loading state hidden')
        }
    }

    /**
     * Zakáže navigační ovládací prvky během navigace
     */
    disableNavigationControls() {
        // Disable breadcrumb links
        const breadcrumbLinks = this.controller.element.querySelectorAll('[data-action*="navigateToFolder"]')
        breadcrumbLinks.forEach(link => {
            link.style.pointerEvents = 'none'
            link.style.opacity = '0.5'
        })
        
        // Disable back button
        if (this.controller.hasBackButtonTarget) {
            this.controller.backButtonTarget.disabled = true
        }
        
        // Disable folder double-clicks
        const folderElements = this.controller.element.querySelectorAll('[data-slozka-id]')
        folderElements.forEach(element => {
            element.style.pointerEvents = 'none'
            element.style.opacity = '0.7'
        })
    }

    /**
     * Povolí navigační ovládací prvky po dokončení navigace
     */
    enableNavigationControls() {
        // Enable breadcrumb links
        const breadcrumbLinks = this.controller.element.querySelectorAll('[data-action*="navigateToFolder"]')
        breadcrumbLinks.forEach(link => {
            link.style.pointerEvents = ''
            link.style.opacity = ''
        })
        
        // Enable back button
        if (this.controller.hasBackButtonTarget) {
            this.controller.backButtonTarget.disabled = false
        }
        
        // Enable folder double-clicks
        const folderElements = this.controller.element.querySelectorAll('[data-slozka-id]')
        folderElements.forEach(element => {
            element.style.pointerEvents = ''
            element.style.opacity = ''
        })
    }

    /**
     * Získá folder ID z URL parametru
     * @returns {number|null} Folder ID nebo null
     */
    getfolderIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search)
        const folderParam = urlParams.get(this.controller.urlParamName)
        return folderParam ? parseInt(folderParam) : null
    }

    /**
     * Přidá navigaci do historie
     * @param {Object} navigation - Navigation transaction
     */
    addToNavigationHistory(navigation) {
        this.navigationHistory.push({
            id: navigation.id,
            targetFolderId: navigation.targetFolderId,
            timestamp: navigation.startTime,
            duration: Date.now() - navigation.startTime
        })
        
        // Omez velikost historie
        if (this.navigationHistory.length > this.maxHistorySize) {
            this.navigationHistory.shift()
        }
    }

    /**
     * Getter pro stav navigace
     * @returns {boolean} True pokud probíhá navigace
     */
    get isNavigationInProgress() {
        return this.isNavigating
    }

    /**
     * Getter pro historii navigace
     * @returns {Array} Navigation history
     */
    get history() {
        return [...this.navigationHistory]
    }

    /**
     * Debug informace o stavu navigace
     * @returns {Object} Debug info
     */
    getDebugInfo() {
        return {
            isNavigating: this.isNavigating,
            currentNavigation: this.currentNavigation ? {
                id: this.currentNavigation.id,
                targetFolderId: this.currentNavigation.targetFolderId,
                cancelled: this.currentNavigation.cancelled
            } : null,
            historySize: this.navigationHistory.length,
            currentState: this.currentState,
            previousState: this.previousState
        }
    }
}
