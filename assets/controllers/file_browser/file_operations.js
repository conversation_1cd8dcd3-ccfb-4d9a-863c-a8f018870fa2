/**
 * Třída pro správu operací se soubory a slo<PERSON>kami (download, delete, rename, create folder)
 */
export class FileOperations {
    
    constructor(controller) {
        this.controller = controller
    }

    /**
     * Download souboru
     * @param {Event} event 
     */
    downloadFile(event) {
        event.preventDefault()
        
        const souborId = event.currentTarget.dataset.souborId
        const button = event.currentTarget
        
        console.log("Downloading file:", souborId)
        
        // Zobrazíme loading stav
        this.setButtonLoading(button, true)
        
        try {
            // Vytvoříme URL pro download
            const downloadUrl = `/soubor/${souborId}`
            
            // Otevřeme download v novém okně/tabu
            window.open(downloadUrl, '_blank')
            
            // Resetujeme loading stav po krátké chvíli
            setTimeout(() => {
                this.setButtonLoading(button, false)
            }, 1000)
            
        } catch (error) {
            console.error("Download failed:", error)
            this.setButtonLoading(button, false)
            
            // Importujeme NotificationHelper dynamicky
            import('./notification_helper.js').then(({ NotificationHelper }) => {
                NotificationHelper.showError("Nepodařilo se stáhnout soubor")
            })
        }
    }

    // === NAVIGATION METHODS ===
    // Poznámka: Navigace je implementována v hlavním file_browser_controller.js
    // - navigateToFolder(folderId) - skutečná navigace
    // - navigateToFolderFromEvent(event) - event wrapper
    // - goBackToParent() - zpět na parent
    // Tyto metody jsou delegované z hlavního controlleru

    // === CREATE FOLDER METHODS ===

    /**
     * Zobrazí modal pro vytvoření složky
     * @param {Event} event 
     */
    showCreateFolderModal(event) {
        event.preventDefault()
        
        if (this.controller.hasCreateFolderModalTarget) {
            // Vyčistíme input a validation message
            if (this.controller.hasCreateFolderInputTarget) {
                this.controller.createFolderInputTarget.value = ''
                
                // Focus na input po krátké chvíli (aby se modal stihl zobrazit)
                setTimeout(() => {
                    this.controller.createFolderInputTarget.focus()
                }, 100)
            }
            
            this.hideCreateFolderValidationMessage()
            this.updateCreateFolderConfirmButton()
            
            // Zobrazíme modal
            this.controller.createFolderModalTarget.classList.remove('hidden')
            
            // Přidáme event listener pro ESC klávěsu
            document.addEventListener('keydown', this.handleCreateFolderEscapeKey.bind(this))
        }
    }

    /**
     * Skryje modal pro vytvoření složky
     * @param {Event} event 
     */
    hideCreateFolderModal(event) {
        if (event) {
            event.preventDefault()
        }
        
        if (this.controller.hasCreateFolderModalTarget) {
            this.controller.createFolderModalTarget.classList.add('hidden')
            
            // Odstraníme event listener pro ESC klávěsu
            document.removeEventListener('keydown', this.handleCreateFolderEscapeKey.bind(this))
        }
    }

    /**
     * Handler pro ESC klávěsu v create folder modalu
     * @param {KeyboardEvent} event 
     */
    handleCreateFolderEscapeKey(event) {
        if (event.key === 'Escape') {
            this.hideCreateFolderModal()
        }
    }

    /**
     * Handler pro keydown v create folder input
     * @param {KeyboardEvent} event 
     */
    handleCreateFolderKeydown(event) {
        if (event.key === 'Enter') {
            event.preventDefault()
            this.confirmCreateFolder()
        }
    }

    /**
     * Validace create folder input
     * @param {Event} event 
     */
    validateCreateFolderInput(event) {
        const folderName = event.target.value.trim()
        const isValid = this.isValidFolderName(folderName)
        
        this.updateCreateFolderConfirmButton()
        
        if (!isValid && folderName !== '') {
            this.showCreateFolderValidationMessage('Název může obsahovat pouze písmena, číslice, mezery, pomlčky a podtržítka')
        } else {
            this.hideCreateFolderValidationMessage()
        }
    }

    /**
     * Kontrola validity názvu složky
     * @param {string} folderName 
     * @returns {boolean}
     */
    isValidFolderName(folderName) {
        // Regex pro povolené znaky - písmena, číslice, mezery, pomlčky, podtržítka
        const regex = /^[a-zA-ZáčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ0-9\s\-_]+$/u
        return regex.test(folderName) && folderName.length <= 100
    }

    /**
     * Aktualizuje stav confirm tlačítka
     */
    updateCreateFolderConfirmButton() {
        if (this.controller.hasCreateFolderConfirmButtonTarget && this.controller.hasCreateFolderInputTarget) {
            const folderName = this.controller.createFolderInputTarget.value.trim()
            const isValid = this.isValidFolderName(folderName)
            
            this.controller.createFolderConfirmButtonTarget.disabled = !isValid || folderName === ''
        }
    }

    /**
     * Zobrazí validation message
     * @param {string} message 
     */
    showCreateFolderValidationMessage(message) {
        if (this.controller.hasCreateFolderValidationMessageTarget) {
            this.controller.createFolderValidationMessageTarget.textContent = message
            this.controller.createFolderValidationMessageTarget.classList.remove('hidden')
        }
    }

    /**
     * Skryje validation message
     */
    hideCreateFolderValidationMessage() {
        if (this.controller.hasCreateFolderValidationMessageTarget) {
            this.controller.createFolderValidationMessageTarget.classList.add('hidden')
        }
    }

    /**
     * Potvrzení vytvoření složky
     * @param {Event} event 
     */
    async confirmCreateFolder(event) {
        if (event) {
            event.preventDefault()
        }
        
        if (!this.controller.hasCreateFolderInputTarget) {
            return
        }

        const folderName = this.controller.createFolderInputTarget.value.trim()
        
        if (!folderName || !this.isValidFolderName(folderName)) {
            this.showCreateFolderValidationMessage('Zadejte platný název složky')
            return
        }

        const parentSlozkaId = this.controller.currentFolderId

        this.hideCreateFolderModal()

        try {
            // Vygenerujeme create folder URL
            const createUrl = `/api/internal/v1/slozka/create-subfolder/${parentSlozkaId}`
            
            // Zavoláme POST API
            const response = await fetch(createUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    nazev: folderName
                })
            })

            const data = await response.json()

            if (response.ok && data.statusCode === 200) {
                // Úspěch - zobrazíme notifikaci
                import('./notification_helper.js').then(({ NotificationHelper }) => {
                    NotificationHelper.showSuccess(data.message || `Složka "${folderName}" byla úspěšně vytvořena`)
                })
                
                // Refresh file browseru
                this.controller.refresh()
                
            } else {
                // Chyba z API
                throw new Error(data.error || data.message || 'Nepodařilo se vytvořit složku')
            }

        } catch (error) {
            console.error('Create folder failed:', error)
            
            // Zobrazíme error notifikaci
            import('./notification_helper.js').then(({ NotificationHelper }) => {
                NotificationHelper.showError(`Chyba při vytváření složky "${folderName}": ${error.message}`)
            })
        }
    }

    /**
     * Nastavení loading stavu tlačítka
     * @param {HTMLElement} button 
     * @param {boolean} isLoading 
     */
    setButtonLoading(button, isLoading) {
        const icon = button.querySelector('i')
        
        if (isLoading) {
            button.disabled = true
            button.classList.add('opacity-50', 'cursor-not-allowed')
            if (icon) {
                icon.innerHTML = '<svg class="w-4 h-4 animate-spin" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>'
            }
        } else {
            button.disabled = false
            button.classList.remove('opacity-50', 'cursor-not-allowed')
            if (icon) {
                icon.innerHTML = '<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V4M7 14H5a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-2m-1-5-4 5-4-5m9 8h.01"/></svg>'
            }
        }
    }
}
