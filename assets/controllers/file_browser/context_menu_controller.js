/**
 * Třída pro správu kontextového menu file browseru
 */
export class ContextMenuController {
    
    constructor(controller) {
        this.controller = controller
        this.currentFileId = null
        this.currentFileName = null
        this.currentFolderId = null
        this.currentFolderName = null
        this.hideContextMenuHandler = this.hideContextMenuHandler.bind(this)
    }

    /**
     * Zobrazí kontextové menu
     * @param {Event} event 
     */
    showContextMenu(event) {
        event.preventDefault()
        
        // Skryjeme případné jiné context menu
        this.hideContextMenu()
        
        const souborId = event.currentTarget.dataset.souborId
        if (!souborId) {
            console.log("No file ID found for context menu")
            return
        }
        
        console.log("Showing context menu for file:", souborId)
        
        if (this.controller.hasContextMenuTarget) {
            // Uložíme ID aktuálního souboru
            this.currentFileId = souborId
            
            // Pokusíme se najít název souboru z DOM
            this.currentFileName = this.getFileNameFromDOM(event.currentTarget)
            
            // Pozicování menu podle pozice myši
            const x = event.clientX
            const y = event.clientY
            
            this.controller.contextMenuTarget.style.left = `${x}px`
            this.controller.contextMenuTarget.style.top = `${y}px`
            this.controller.contextMenuTarget.classList.remove('hidden')
            
            // Přidáme event listener pro kliknutí mimo menu
            document.addEventListener('click', this.hideContextMenuHandler)
        }
    }

    /**
     * Získá název souboru z DOM elementu
     * @param {HTMLElement} element 
     * @returns {string}
     */
    getFileNameFromDOM(element) {
        // Pokusíme se najít název souboru v různých strukturách
        let fileName = 'Neznámý soubor'
        
        // Pro list view - hledáme v td elementu
        const nameCell = element.querySelector('td .font-medium')
        if (nameCell) {
            fileName = nameCell.textContent.trim()
        } else {
            // Pro grid view - hledáme v div elementu
            const nameDiv = element.querySelector('.text-sm .truncate')
            if (nameDiv) {
                fileName = nameDiv.textContent.trim()
            }
        }
        
        return fileName
    }

    /**
     * Skryje kontextové menu
     */
    hideContextMenu() {
        if (this.controller.hasContextMenuTarget) {
            this.controller.contextMenuTarget.classList.add('hidden')
            this.currentFileId = null
            this.currentFileName = null
            
            // Odstraníme event listener
            document.removeEventListener('click', this.hideContextMenuHandler)
        }
    }

    /**
     * Handler pro skrytí menu při kliknutí mimo
     * @param {Event} event 
     */
    hideContextMenuHandler(event) {
        if (this.controller.hasContextMenuTarget && !this.controller.contextMenuTarget.contains(event.target)) {
            this.hideContextMenu()
        }
    }

    /**
     * Download souboru z context menu
     * @param {Event} event 
     */
    downloadFileFromContext(event) {
        event.preventDefault()
        
        // Uložíme ID před skrytím menu
        const fileId = this.currentFileId
        const fileName = this.currentFileName
        this.hideContextMenu()
        
        if (fileId) {
            // Použijeme novou metodu pro generování URL
            const downloadUrl = this.controller.generateDownloadUrl(fileId)
            window.open(downloadUrl, '_blank')
            
            // Importujeme NotificationHelper dynamicky
            import('./notification_helper.js').then(({ NotificationHelper }) => {
                NotificationHelper.showSuccess(`Stahuje se: ${fileName}`)
            })
        }
    }

    /**
     * Přejmenování souboru z context menu
     * @param {Event} event 
     */
    renameFileFromContext(event) {
        event.preventDefault()
        
        // Uložíme data před skrytím menu
        const fileId = this.currentFileId
        const fileName = this.currentFileName
        
        this.hideContextMenu()
        
        if (fileId) {
            // Dočasně obnovíme hodnoty pro modal
            this.currentFileId = fileId
            this.currentFileName = fileName
            
            this.showRenameModal()
        }
    }

    /**
     * Smazání souboru z context menu - zobrazí modal
     * @param {Event} event 
     */
    deleteFileFromContext(event) {
        event.preventDefault()
        
        // Uložíme data PŘED skrytím menu
        const fileId = this.currentFileId
        const fileName = this.currentFileName
        
        this.hideContextMenu()
        
        if (fileId) {
            // Dočasně obnovíme hodnoty pro modal
            this.currentFileId = fileId
            this.currentFileName = fileName
            
            this.showDeleteModal()
        }
    }

    // === RENAME MODAL METHODS ===

    /**
     * Zobrazí modal pro přejmenování
     */
    showRenameModal() {
        if (this.controller.hasRenameModalTarget) {
            // Nastavíme aktuální název souboru
            if (this.controller.hasRenameCurrentFileNameTarget) {
                this.controller.renameCurrentFileNameTarget.textContent = this.currentFileName || 'Neznámý soubor'
            }
            
            // Nastavíme hodnotu input fieldu (bez přípony)
            if (this.controller.hasRenameInputTarget) {
                const nameWithoutExtension = this.getFileNameWithoutExtension(this.currentFileName)
                this.controller.renameInputTarget.value = nameWithoutExtension
                
                // Focus a select text
                setTimeout(() => {
                    this.controller.renameInputTarget.focus()
                    this.controller.renameInputTarget.select()
                }, 100)
            }
            
            // Skryjeme validation message
            this.hideRenameValidationMessage()
            
            // Zobrazíme modal
            this.controller.renameModalTarget.classList.remove('hidden')
            
            // Přidáme event listener pro ESC klávěsu
            document.addEventListener('keydown', this.handleRenameEscapeKey.bind(this))
        }
    }

    /**
     * Skryje modal pro přejmenování
     */
    hideRenameModal() {
        if (this.controller.hasRenameModalTarget) {
            this.controller.renameModalTarget.classList.add('hidden')
            
            // Odstraníme event listener pro ESC klávěsu
            document.removeEventListener('keydown', this.handleRenameEscapeKey.bind(this))
        }
    }

    /**
     * Získá název souboru bez přípony
     * @param {string} fileName 
     * @returns {string}
     */
    getFileNameWithoutExtension(fileName) {
        const lastDotIndex = fileName.lastIndexOf('.')
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName
    }

    /**
     * Handler pro ESC klávěsu v rename modalu
     * @param {KeyboardEvent} event 
     */
    handleRenameEscapeKey(event) {
        if (event.key === 'Escape') {
            this.cancelRename()
        }
    }

    /**
     * Handler pro keydown v rename input
     * @param {KeyboardEvent} event 
     */
    handleRenameKeydown(event) {
        if (event.key === 'Enter') {
            event.preventDefault()
            this.confirmRename()
        }
    }

    /**
     * Validace rename input
     * @param {Event} event 
     */
    validateRenameInput(event) {
        const newName = event.target.value.trim()
        const isValid = this.isValidFileName(newName)
        
        if (this.controller.hasRenameConfirmButtonTarget) {
            this.controller.renameConfirmButtonTarget.disabled = !isValid || newName === ''
        }
        
        if (!isValid && newName !== '') {
            this.showRenameValidationMessage('Název může obsahovat pouze písmena (včetně českých znaků), číslice, mezery, pomlčky, podtržítka, tečky a závorky')
        } else {
            this.hideRenameValidationMessage()
        }
    }

    /**
     * Kontrola validity názvu souboru
     * @param {string} fileName 
     * @returns {boolean}
     */
    isValidFileName(fileName) {
        // Stejný regex jako v backend controlleru - s podporou českých znaků
        const regex = /^[a-zA-ZáčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ0-9\s\-_\.\(\)\[\]]+$/u
        return regex.test(fileName) && fileName.length <= 255
    }

    /**
     * Zobrazí validation message
     * @param {string} message 
     */
    showRenameValidationMessage(message) {
        if (this.controller.hasRenameValidationMessageTarget) {
            this.controller.renameValidationMessageTarget.textContent = message
            this.controller.renameValidationMessageTarget.classList.remove('hidden')
        }
    }

    /**
     * Skryje validation message
     */
    hideRenameValidationMessage() {
        if (this.controller.hasRenameValidationMessageTarget) {
            this.controller.renameValidationMessageTarget.classList.add('hidden')
        }
    }

    /**
     * Potvrzení přejmenování
     * @param {Event} event 
     */
    async confirmRename(event) {
        if (event) {
            event.preventDefault()
        }
        
        if (!this.controller.hasRenameInputTarget || !this.currentFileId) {
            return
        }

        const newName = this.controller.renameInputTarget.value.trim()
        
        if (!newName || !this.isValidFileName(newName)) {
            this.showRenameValidationMessage('Zadejte platný název souboru')
            return
        }

        const fileId = this.currentFileId
        const oldName = this.currentFileName

        this.hideRenameModal()

        try {
            // Vygenerujeme rename URL
            const renameUrl = this.controller.generateRenameUrl(fileId)
            
            // Zavoláme PUT API
            const response = await fetch(renameUrl, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    newName: newName
                })
            })

            const data = await response.json()

            if (response.ok && data.statusCode === 200) {
                // Úspěch - zobrazíme notifikaci
                import('./notification_helper.js').then(({ NotificationHelper }) => {
                    NotificationHelper.showSuccess(data.message || `Soubor byl úspěšně přejmenován`)
                })
                
                // Refresh file browseru
                this.controller.refresh()
                
            } else {
                // Chyba z API
                throw new Error(data.error || data.message || 'Nepodařilo se přejmenovat soubor')
            }

        } catch (error) {
            console.error('Rename failed:', error)
            
            // Zobrazíme error notifikaci
            import('./notification_helper.js').then(({ NotificationHelper }) => {
                NotificationHelper.showError(`Chyba při přejmenování souboru "${oldName}": ${error.message}`)
            })
        } finally {
            // Resetujeme stav
            this.currentFileId = null
            this.currentFileName = null
        }
    }

    /**
     * Zrušení přejmenování
     * @param {Event} event 
     */
    cancelRename(event) {
        if (event) {
            event.preventDefault()
        }
        
        this.hideRenameModal()
        
        // Resetujeme stav
        this.currentFileId = null
        this.currentFileName = null
    }

    // === DELETE MODAL METHODS ===

    /**
     * Zobrazí modal pro potvrzení smazání
     */
    showDeleteModal() {
        if (this.controller.hasDeleteModalTarget) {
            // Nastavíme název souboru v modalu
            if (this.controller.hasDeleteFileNameTarget) {
                this.controller.deleteFileNameTarget.textContent = this.currentFileName || 'Neznámý soubor'
            }
            
            // Zobrazíme modal
            this.controller.deleteModalTarget.classList.remove('hidden')
            
            // Přidáme event listener pro ESC klávěsu
            document.addEventListener('keydown', this.handleEscapeKey.bind(this))
        }
    }

    /**
     * Skryje modal pro potvrzení smazání
     */
    hideDeleteModal() {
        if (this.controller.hasDeleteModalTarget) {
            this.controller.deleteModalTarget.classList.add('hidden')
            
            // Odstraníme event listener pro ESC klávěsu
            document.removeEventListener('keydown', this.handleEscapeKey.bind(this))
        }
    }

    /**
     * Handler pro ESC klávěsu
     * @param {KeyboardEvent} event 
     */
    handleEscapeKey(event) {
        if (event.key === 'Escape') {
            this.cancelDelete()
        }
    }

    /**
     * Potvrzení smazání souboru
     * @param {Event} event 
     */
    async confirmDelete(event) {
        event.preventDefault()
        this.hideDeleteModal()
        
        if (!this.currentFileId) {
            return
        }

        const fileId = this.currentFileId
        const fileName = this.currentFileName

        try {
            // Vygenerujeme delete URL
            const deleteUrl = this.controller.generateDeleteUrl(fileId)
            
            // Zavoláme DELETE API
            const response = await fetch(deleteUrl, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })

            const data = await response.json()

            if (response.ok && data.statusCode === 200) {
                // Úspěch - zobrazíme notifikaci
                import('./notification_helper.js').then(({ NotificationHelper }) => {
                    NotificationHelper.showSuccess(data.message || `Soubor "${fileName}" byl úspěšně smazán`)
                })
                
                // Refresh file browseru
                this.controller.refresh()
                
            } else {
                // Chyba z API
                throw new Error(data.error || data.message || 'Nepodařilo se smazat soubor')
            }

        } catch (error) {
            console.error('Delete failed:', error)
            
            // Zobrazíme error notifikaci
            import('./notification_helper.js').then(({ NotificationHelper }) => {
                NotificationHelper.showError(`Chyba při mazání souboru "${fileName}": ${error.message}`)
            })
        } finally {
            // Resetujeme stav
            this.currentFileId = null
            this.currentFileName = null
        }
    }

    /**
     * Zrušení smazání souboru
     * @param {Event} event 
     */
    cancelDelete(event) {
        if (event) {
            event.preventDefault()
        }
        
        this.hideDeleteModal()
        
        // Resetujeme stav
        this.currentFileId = null
        this.currentFileName = null
    }

    // === FOLDER CONTEXT MENU METHODS ===

    /**
     * Zobrazí kontextové menu pro složku
     * @param {Event} event 
     */
    showFolderContextMenu(event) {
        event.preventDefault()
        
        // Skryjeme případné jiné context menu
        this.hideContextMenu()
        
        const slozkaId = event.currentTarget.dataset.slozkaId
        const slozkaNazev = event.currentTarget.dataset.slozkaNazev
        
        if (!slozkaId) {
            console.log("No folder ID found for context menu")
            return
        }
        
        console.log("Showing folder context menu for:", slozkaId, slozkaNazev)
        
        // Uložíme data složky
        this.currentFolderId = parseInt(slozkaId)
        this.currentFolderName = slozkaNazev || 'Neznámá složka'
        
        // Zobrazíme folder context menu (použijeme stejný target, ale jiný obsah)
        if (this.controller.hasContextMenuTarget) {
            // Pozicování menu podle pozice myši
            const x = event.clientX
            const y = event.clientY
            
            // Vygenerujeme obsah pro folder menu
            this.controller.contextMenuTarget.innerHTML = this.generateFolderContextMenuHTML()
            
            this.controller.contextMenuTarget.style.left = `${x}px`
            this.controller.contextMenuTarget.style.top = `${y}px`
            this.controller.contextMenuTarget.classList.remove('hidden')
            
            // Přidáme event listener pro kliknutí mimo menu
            document.addEventListener('click', this.hideContextMenuHandler)
        }
    }

    /**
     * Vygeneruje HTML pro folder context menu
     * @returns {string}
     */
    generateFolderContextMenuHTML() {
        return `
            <div class="py-1">
                <button data-action="click->file-browser#deleteFolderFromContext"
                        class="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 transition-colors">
                    <svg class="w-4 h-4 mr-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h16M7 8v8m4-8v8M7 1h4a1 1 0 0 1 1 1v3H6V2a1 1 0 0 1 1-1ZM3 5h12v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5Z"/>
                    </svg>
                    Smazat složku
                </button>
            </div>
        `
    }

    /**
     * Smazání složky z context menu
     * @param {Event} event 
     */
    deleteFolderFromContext(event) {
        event.preventDefault()
        
        // Uložíme data před skrytím menu
        const folderId = this.currentFolderId
        const folderName = this.currentFolderName
        
        this.hideContextMenu()
        
        if (folderId) {
            // Dočasně obnovíme hodnoty pro modal
            this.currentFolderId = folderId
            this.currentFolderName = folderName
            
            this.showDeleteFolderModal()
        }
    }

    /**
     * Zobrazí modal pro potvrzení smazání složky
     */
    showDeleteFolderModal() {
        if (this.controller.hasDeleteModalTarget) {
            // Nastavíme název složky v modalu
            if (this.controller.hasDeleteFileNameTarget) {
                this.controller.deleteFileNameTarget.textContent = this.currentFolderName || 'Neznámá složka'
            }
            
            // Zobrazíme modal (použijeme stejný jako pro soubory)
            this.controller.deleteModalTarget.classList.remove('hidden')
            
            // Přidáme event listener pro ESC klávěsu
            document.addEventListener('keydown', this.handleFolderDeleteEscapeKey.bind(this))
        }
    }

    /**
     * Handler pro ESC klávěsu při mazání složky
     * @param {KeyboardEvent} event 
     */
    handleFolderDeleteEscapeKey(event) {
        if (event.key === 'Escape') {
            this.cancelFolderDelete()
        }
    }

    /**
     * Potvrzení smazání složky
     * @param {Event} event 
     */
    async confirmFolderDelete(event) {
        event.preventDefault()
        this.hideDeleteModal()
        
        if (!this.currentFolderId) {
            return
        }

        const folderId = this.currentFolderId
        const folderName = this.currentFolderName

        try {
            // Zavoláme DELETE API pro složku
            const deleteUrl = `/api/internal/v1/slozka/delete/${folderId}`
            
            const response = await fetch(deleteUrl, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })

            const data = await response.json()

            if (response.ok && data.statusCode === 200) {
                // Úspěch - zobrazíme notifikaci
                import('./notification_helper.js').then(({ NotificationHelper }) => {
                    NotificationHelper.showSuccess(data.message || `Složka "${folderName}" byla úspěšně smazána`)
                })
                
                // Refresh file browseru
                this.controller.refresh()
                
            } else {
                // Chyba z API
                throw new Error(data.error || data.message || 'Nepodařilo se smazat složku')
            }

        } catch (error) {
            console.error('Folder delete failed:', error)
            
            // Zobrazíme error notifikaci
            import('./notification_helper.js').then(({ NotificationHelper }) => {
                NotificationHelper.showError(`Chyba při mazání složky "${folderName}": ${error.message}`)
            })
        } finally {
            // Resetujeme stav
            this.currentFolderId = null
            this.currentFolderName = null
        }
    }

    /**
     * Zrušení smazání složky
     * @param {Event} event 
     */
    cancelFolderDelete(event) {
        if (event) {
            event.preventDefault()
        }
        
        this.hideDeleteModal()
        
        // Resetujeme stav
        this.currentFolderId = null
        this.currentFolderName = null
    }
}
