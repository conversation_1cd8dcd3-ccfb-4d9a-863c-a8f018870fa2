import { FileIconHelper } from './file_icon_helper.js'

/**
 * Třída pro generování HTML obsahu file browseru (list/grid view)
 */
export class ViewRenderer {
    
    /**
     * Vygeneruje HTML obsah pro list nebo grid view
     * @param {Object} data - data se soubory a podsložkami
     * @param {boolean} isGridView - true pro grid view, false pro list view
     * @returns {string} HTML string
     */
    static generateContentHTML(data, isGridView) {
        const { soubory, podslozky } = data
        
        if (soubory.length === 0 && podslozky.length === 0) {
            return this.generateEmptyStateHTML()
        }
        
        if (isGridView) {
            return this.generateGridHTML(soubory, podslozky)
        } else {
            return this.generateListHTML(soubory, podslozky)
        }
    }

    /**
     * Vygeneruje HTML pro prázdný stav
     * @returns {string} HTML string
     */
    static generateEmptyStateHTML() {
        return `
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h6l2 4m-8-4V3a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v9h2m8 0V9a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1m8-8v8a1 1 0 0 1-1 1H11a1 1 0 0 1-1-1V9"/>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Prázdná složka
                </h3>
                <p class="text-gray-500 dark:text-gray-400">
                    Tato složka neobsahuje žádné soubory ani podsložky.
                </p>
            </div>
        `
    }

    /**
     * Vygeneruje HTML pro list view
     * @param {Array} soubory - pole souborů
     * @param {Array} podslozky - pole podsložek
     * @returns {string} HTML string
     */
    static generateListHTML(soubory, podslozky) {
        return `
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3 w-8">
                                <!-- Ikona -->
                            </th>
                            <th scope="col" class="px-4 py-3">
                                Název
                            </th>
                            <th scope="col" class="px-4 py-3 w-32">
                                <div class="flex items-center space-x-1">
                                    <span>Upraveno</span>
                                    <div class="flex flex-col ml-1">
                                        <button data-action="click->file-browser#sortByCreatedAt" 
                                                data-order="desc"
                                                data-file-browser-target="sortDescButton"
                                                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-0.5"
                                                title="Řadit od nejnovějších">
                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                        <button data-action="click->file-browser#sortByCreatedAt" 
                                                data-order="asc"
                                                data-file-browser-target="sortAscButton"
                                                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-0.5"
                                                title="Řadit od nejstarších">
                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        ${podslozky.map(podslozka => `
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors duration-200 group"
                                data-action="dblclick->file-browser#navigateToFolderFromEvent contextmenu->file-browser#showFolderContextMenu"
                                data-slozka-id="${podslozka.id}"
                                data-slozka-nazev="${podslozka.nazev}">
                                <td class="px-4 py-4">
                                    <svg class="w-5 h-5 text-yellow-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M3 8a2 2 0 0 1 2-2h3.93a2 2 0 0 0 1.66-.9l.82-1.2A2 2 0 0 1 13.07 3H19a2 2 0 0 1 2 2v13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8Z"/>
                                    </svg>
                                </td>
                                <td class="px-4 py-4 font-medium text-gray-900 dark:text-white">
                                    <div class="flex items-center">
                                        <span>${podslozka.nazev}</span>
                                        <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(složka)</span>
                                    </div>
                                </td>
                                <td class="px-4 py-4 text-gray-500 dark:text-gray-400">
                                    --
                                </td>
                            </tr>
                        `).join('')}
                        
                        ${soubory.map(soubor => `
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200 group"
                                data-soubor-id="${soubor.id}"
                                data-action="contextmenu->file-browser#showContextMenu"
                                draggable="true">
                                <td class="px-4 py-4">
                                    ${FileIconHelper.getFileIconSVGSmall(soubor)}
                                </td>
                                <td class="px-4 py-4 cursor-pointer"
                                    data-action="click->file-browser#downloadFile"
                                    data-soubor-id="${soubor.id}">
                                    <div class="font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                        ${soubor.originalFilename}
                                    </div>
                                </td>
                                <td class="px-4 py-4 text-gray-500 dark:text-gray-400">
                                    ${soubor.createdAt || '--'}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `
    }

    /**
     * Vygeneruje HTML pro grid view
     * @param {Array} soubory - pole souborů
     * @param {Array} podslozky - pole podsložek
     * @returns {string} HTML string
     */
    static generateGridHTML(soubory, podslozky) {
        return `
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                ${podslozky.map(podslozka => `
                    <div class="flex flex-col items-center p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors min-h-32"
                         data-action="dblclick->file-browser#navigateToFolderFromEvent contextmenu->file-browser#showFolderContextMenu"
                         data-slozka-id="${podslozka.id}"
                         data-slozka-nazev="${podslozka.nazev}">
                        <svg class="w-12 h-12 text-yellow-500 mb-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M3 8a2 2 0 0 1 2-2h3.93a2 2 0 0 0 1.66-.9l.82-1.2A2 2 0 0 1 13.07 3H19a2 2 0 0 1 2 2v13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8Z"/>
                        </svg>
                        <div class="text-sm text-center text-gray-900 dark:text-white font-medium w-full px-2" title="${podslozka.nazev}">
                            <div class="truncate">
                                ${podslozka.nazev}
                            </div>
                        </div>
                    </div>
                `).join('')}
                
                ${soubory.map(soubor => `
                    <div class="flex flex-col items-center p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 hover:shadow-md transition-all duration-200 group cursor-pointer min-h-32"
                         data-soubor-id="${soubor.id}"
                         data-action="click->file-browser#downloadFile contextmenu->file-browser#showContextMenu"
                         draggable="true">
                        
                        <div class="mb-3">
                            ${FileIconHelper.getFileIconSVG(soubor)}
                        </div>
                        
                        <div class="text-center w-full">
                            <div class="text-sm text-gray-900 dark:text-white font-medium hover:text-blue-600 dark:hover:text-blue-400 transition-colors px-2" title="${soubor.originalFilename}">
                                <div class="truncate">
                                    ${soubor.originalFilename}
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `
    }
}
