import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ["inputUserId", "userCsrfToken", "userNameSpan"];

    deleteModalDialog(event) {
        try {
            const button = event.currentTarget;
            const userId = button.getAttribute('data-user-id');
            const userToken = button.getAttribute('data-user-token');
            const userName = button.getAttribute('data-user-name');

            if (userId !== null) {
                this.inputUserIdTarget.value = userId;
                this.userCsrfTokenTarget.value = userToken;
                try {
                    this.userNameSpanTarget.textContent = userName;
                } catch (error) {
                    console.error("Error in deleteModalDialog userNameSpan:", error);
                }
            }
        } catch (error) {
            console.error("Error in deleteModalDialog:", error);
        }
    }

    confirmDeleteButton() {
        try {
            const userId = this.inputUserIdTarget.value;
            const userToken = this.userCsrfTokenTarget.value;

            // Získání URL pro smazání uživatele z hidden input field
            const deleteUrl = document.getElementById(`deleteUserUrl-${userId}`).value;

            var form = document.createElement('form');
            form.method = 'post';
            form.action = deleteUrl;

            var csrfTokenInput = document.createElement('input');
            csrfTokenInput.type = 'hidden';
            csrfTokenInput.name = '_token';
            csrfTokenInput.value = userToken;
            form.appendChild(csrfTokenInput);

            var userIdInput = document.createElement('input');
            userIdInput.type = 'hidden';
            userIdInput.name = 'id';
            userIdInput.value = userId;
            form.appendChild(userIdInput);

            document.body.appendChild(form);
            form.submit();
            
        } catch (error) {
            console.error("Error in confirmDeleteButton:", error);
        }
    }
};
