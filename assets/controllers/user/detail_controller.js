import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ["userNameSpan", "userEmailSpan", "userRoleSpan", "userStatusSpan", "userLastLoginSpan"];

    detailModalDialog(event) {
        console.log(true);
        try {
            const button = event.currentTarget;
            const userName = button.getAttribute('data-user-name');
            const userEmail = button.getAttribute('data-user-email');
            const userRole = button.getAttribute('data-user-role');
            const userStatus = button.getAttribute('data-user-status');
            const userLastLogin = button.getAttribute('data-user-last-login');

            try {
                this.userNameSpanTarget.textContent = userName;
                this.userEmailSpanTarget.textContent = userEmail;
                this.userRoleSpanTarget.textContent = userRole;
                this.userStatusSpanTarget.textContent = userStatus;
                this.userLastLoginSpanTarget.textContent = userLastLogin;
            } catch (error) {
                console.error("Error in detailModalDialog userNameSpan:", error);
            }
        } catch (error) {
            console.error("Error in detailModalDialog:", error);
        }
    }
};
