import { Controller } from "@hotwired/stimulus"
import { NotificationHelper } from './file_browser/notification_helper.js'
import { ViewRenderer } from './file_browser/view_renderer.js'
import { ContextMenuController } from './file_browser/context_menu_controller.js'
import { UploadController } from './file_browser/upload_controller.js'
import { FileOperations } from './file_browser/file_operations.js'
import { globalDragDropManager } from './file_browser/drag_drop_manager.js'
import { ErrorHandler } from './file_browser/error_handler.js'
import { NavigationStateManager } from './file_browser/navigation_state_manager.js'

// Zakážeme auto-discover pro Dropzone
import Dropzone from 'dropzone'
Dropzone.autoDiscover = false

export default class extends Controller {
    static targets = ["content", "loading", "backButton", "listViewButton", "gridViewButton", "uploadZone", "dropzoneForm", "uploadButton", "contextMenu", "deleteModal", "deleteFileName", "renameModal", "renameInput", "renameCurrentFileName", "renameValidationMessage", "renameConfirmButton", "sortDescButton", "sortAscButton", "createFolderModal", "createFolderInput", "createFolderValidationMessage", "createFolderConfirmButton", "breadcrumb"]
    static values = { 
        slozkaId: Number,
        viewMode: String,
        folderApiUrl: String,
        uploadApiUrl: String,
        uploadUrlPattern: String,
        downloadUrlPattern: String,
        deleteUrlPattern: String,
        renameUrlPattern: String
    }

    connect() {
        console.log("🚀 File browser controller connected")
        console.log("Slozka ID:", this.slozkaIdValue)
        console.log("View mode:", this.viewModeValue)
        console.log("Available targets:", this.targets)
        
        // Inicializace navigation state
        this.initializeNavigation()
        
        // Inicializace sort state
        this.currentSort = this.loadSortFromStorage()
        
        // Inicializace modulů
        this.contextMenuController = new ContextMenuController(this)
        this.uploadController = new UploadController(this)
        this.fileOperations = new FileOperations(this)
        this.navigationStateManager = new NavigationStateManager(this)
        
        // Inicializace view mode tlačítek (až budou viditelná)
        this.updateViewModeButtons()
        
        // Inicializace Dropzone (pokud existuje)
        this.uploadController.initializeDropzone()
        
        // Registrace do DragDropManager pro drag & drop funkcionalitu
        this.registerWithDragDropManager()
        
        // 🔧 OPRAVA: Automaticky načteme obsah pomocí ViewRenderer
        // aby se použily správné event listenery i při prvním načtení
        console.log("🔄 Auto-refreshing content to ensure proper event listeners")
        setTimeout(() => {
            this.refresh()
        }, 100) // Krátké zpoždění, aby se DOM stihl inicializovat
    }

    /**
     * Disconnect handler - vyčištění při odpojení controlleru
     */
    disconnect() {
        console.log("🔌 File browser controller disconnecting")
        
        // Odregistrování z DragDropManager
        this.unregisterFromDragDropManager()
    }

    // === DRAG & DROP METODY ===

    /**
     * Registrace této instance do DragDropManager
     */
    registerWithDragDropManager() {
        // Použijeme slozkaId jako unikátní identifikátor instance
        const instanceId = `fb_${this.slozkaIdValue}`
        
        globalDragDropManager.registerFileBrowser(instanceId, this)
        
        // Uložíme si ID pro pozdější odregistrování
        this.dragDropInstanceId = instanceId
        
        console.log(`🎯 Registered with DragDropManager as: ${instanceId}`)
    }

    /**
     * Odregistrování z DragDropManager
     */
    unregisterFromDragDropManager() {
        if (this.dragDropInstanceId) {
            globalDragDropManager.unregisterFileBrowser(this.dragDropInstanceId)
            console.log(`🗑️ Unregistered from DragDropManager: ${this.dragDropInstanceId}`)
            this.dragDropInstanceId = null
        }
    }

    // === NAVIGATION METODY ===

    /**
     * Inicializace navigačního stavu
     */
    initializeNavigation() {
        // Root složka - původní složka ze Symfony controlleru
        this.rootFolderId = this.slozkaIdValue
        
        // Aktuální složka - může být jiná při navigaci
        this.currentFolderId = this.rootFolderId
        
        // Breadcrumb stack pro navigaci
        this.breadcrumbStack = []
        
        // URL parameter name pro tuto instanci
        this.urlParamName = `folder_${this.rootFolderId}`
        
        // Načteme aktuální složku z URL pokud existuje
        this.loadCurrentFolderFromUrl()
        
        // Pokud jsme načetli jinou složku z URL, aktualizujeme API URL
        if (this.currentFolderId !== this.rootFolderId) {
            this.updateApiUrlForCurrentFolder()
        }
        
        console.log("🧭 Navigation initialized:")
        console.log("  Root folder ID:", this.rootFolderId)
        console.log("  Current folder ID:", this.currentFolderId)
        console.log("  URL param name:", this.urlParamName)
        console.log("  API URL:", this.folderApiUrlValue)
    }

    /**
     * Načte aktuální složku z URL parametru
     */
    loadCurrentFolderFromUrl() {
        const urlParams = new URLSearchParams(window.location.search)
        const folderFromUrl = urlParams.get(this.urlParamName)
        
        if (folderFromUrl && folderFromUrl !== this.rootFolderId.toString()) {
            this.currentFolderId = parseInt(folderFromUrl)
            console.log("📍 Loaded folder from URL:", this.currentFolderId)
        }
    }

    /**
     * Aktualizuje URL s aktuální složkou
     */
    updateUrlWithCurrentFolder() {
        const url = new URL(window.location)
        
        if (this.currentFolderId === this.rootFolderId) {
            // Jsme v root složce - odstraníme parametr
            url.searchParams.delete(this.urlParamName)
        } else {
            // Jsme v podsložce - přidáme parametr
            url.searchParams.set(this.urlParamName, this.currentFolderId.toString())
        }
        
        // Aktualizujeme URL bez refresh stránky
        window.history.pushState({}, '', url.toString())
        console.log("🔗 URL updated:", url.toString())
    }

    /**
     * Navigace do složky - deleguje na NavigationStateManager
     */
    async navigateToFolder(folderId) {
        return await this.navigationStateManager.navigateToFolder(folderId)
    }

    /**
     * Zpět na předchozí úroveň
     */
    async goBackToParent() {
        console.log("⬅️ Going back to parent folder")
        
        // Pokud jsme v root složce, nemůžeme jít výš
        if (this.currentFolderId === this.rootFolderId) {
            console.log("Already at root folder")
            return
        }
        
        try {
            // Načteme parent folder ID z API nebo breadcrumb
            const parentFolderId = await this.getParentFolderId(this.currentFolderId)
            
            if (parentFolderId) {
                await this.navigateToFolder(parentFolderId)
            } else {
                // Fallback - jdi na root
                await this.navigateToFolder(this.rootFolderId)
            }
            
        } catch (error) {
            console.error("Go back failed:", error)
            // Fallback - jdi na root
            await this.navigateToFolder(this.rootFolderId)
        }
    }

    /**
     * Získá parent folder ID z breadcrumb dat
     */
    async getParentFolderId(folderId) {
        // Pokud máme breadcrumb data z posledního API volání, použijeme je
        if (this.lastFolderData && this.lastFolderData.breadcrumb) {
            const breadcrumb = this.lastFolderData.breadcrumb
            
            // Najdeme aktuální složku v breadcrumb
            const currentIndex = breadcrumb.findIndex(item => 
                item.slozka && item.slozka.id === folderId
            )
            
            // Pokud existuje předchozí položka, vrátíme její ID
            if (currentIndex > 0) {
                return breadcrumb[currentIndex - 1].slozka.id
            }
        }
        
        // Fallback - vrátíme root
        return this.rootFolderId
    }

    /**
     * Aktualizuje API URL pro aktuální složku
     */
    updateApiUrlForCurrentFolder() {
        // Aktualizujeme folderApiUrlValue pro aktuální složku
        this.folderApiUrlValue = this.folderApiUrlValue.replace(
            /\/folder\/\d+/,
            `/folder/${this.currentFolderId}`
        )
        console.log("🔗 Updated API URL:", this.folderApiUrlValue)
        
        // Aktualizujeme také upload URL
        this.updateUploadUrlForCurrentFolder()
    }

    /**
     * Aktualizuje upload API URL pro aktuální složku
     */
    updateUploadUrlForCurrentFolder() {
        console.log("🔍 BEFORE Upload URL update - Current folder:", this.currentFolderId)
        console.log("🔍 Old upload URL:", this.uploadApiUrlValue)
        
        // Vygenerujeme novou upload URL pomocí pattern helper
        const newUploadUrl = this.generateUploadUrl(this.currentFolderId)
        console.log("📤 New upload URL:", newUploadUrl)
        
        // Aktualizujeme Dropzone URL pokud existuje
        if (this.uploadController && this.uploadController.getDropzone()) {
            console.log("🔍 Dropzone exists, updating URL...")
            this.uploadController.getDropzone().options.url = newUploadUrl
            console.log("📤 Dropzone URL updated to:", this.uploadController.getDropzone().options.url)
        } else {
            console.log("⚠️ Dropzone not found or not initialized yet")
        }
    }

    /**
     * Aktualizuje navigační UI (zpět tlačítko, breadcrumb)
     */
    updateNavigationUI() {
        // Aktualizujeme zpět tlačítko
        this.updateBackButton()
        
        // Aktualizujeme breadcrumb
        if (this.lastFolderData && this.lastFolderData.breadcrumb) {
            this.updateBreadcrumb(this.lastFolderData.breadcrumb)
        }
    }

    /**
     * Aktualizuje zpět tlačítko
     */
    updateBackButton() {
        if (this.hasBackButtonTarget) {
            if (this.currentFolderId === this.rootFolderId) {
                // Jsme v root - skryj zpět tlačítko
                this.backButtonTarget.classList.add('hidden')
            } else {
                // Jsme v podsložce - zobraz zpět tlačítko
                this.backButtonTarget.classList.remove('hidden')
            }
        }
    }

    /**
     * Aktualizuje breadcrumb HTML
     * @param {Array} breadcrumbData - data z API
     */
    updateBreadcrumb(breadcrumbData) {
        if (!this.hasBreadcrumbTarget) {
            console.warn('Breadcrumb target not found')
            return
        }

        console.log('🍞 Updating breadcrumb with data:', breadcrumbData)

        // Vygenerujeme nový breadcrumb HTML
        const breadcrumbHTML = this.generateBreadcrumbHTML(breadcrumbData)
        
        // Aktualizujeme DOM
        this.breadcrumbTarget.innerHTML = breadcrumbHTML
        
        console.log('✅ Breadcrumb updated')
    }

    /**
     * Vygeneruje HTML pro breadcrumb
     * @param {Array} breadcrumbData 
     * @returns {string}
     */
    generateBreadcrumbHTML(breadcrumbData) {
        if (!breadcrumbData || breadcrumbData.length === 0) {
            return '<div class="text-sm text-gray-500">Žádná cesta</div>'
        }

        let html = '<nav class="flex" aria-label="Breadcrumb"><ol class="inline-flex items-center space-x-1 md:space-x-3">'
        
        breadcrumbData.forEach((item, index) => {
            html += '<li class="inline-flex items-center">'
            
            // Přidáme šipku (kromě prvního prvku)
            if (index > 0) {
                html += `
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                `
            }
            
            if (item.is_current) {
                // Aktuální složka - neklikatelná, modrá
                html += `
                    <span class="ms-1 text-sm font-semibold text-blue-600 dark:text-blue-400 md:ms-2 inline-flex items-center">
                        <svg class="w-4 h-4 text-blue-500 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M3 8a2 2 0 0 1 2-2h3.93a2 2 0 0 0 1.66-.9l.82-1.2A2 2 0 0 1 13.07 3H19a2 2 0 0 1 2 2v13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8Z"/>
                        </svg>
                        ${item.nazev}
                    </span>
                `
            } else {
                // Parent složka - klikatelná, žlutá
                html += `
                    <a href="#" 
                       data-action="click->file-browser#navigateToFolderFromEvent"
                       data-slozka-id="${item.slozka.id}"
                       class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white transition-colors">
                        <svg class="w-4 h-4 text-yellow-500 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M3 8a2 2 0 0 1 2-2h3.93a2 2 0 0 0 1.66-.9l.82-1.2A2 2 0 0 1 13.07 3H19a2 2 0 0 1 2 2v13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8Z"/>
                        </svg>
                        ${item.nazev}
                    </a>
                `
            }
            
            html += '</li>'
        })
        
        html += '</ol></nav>'
        
        return html
    }

    // === SORT METODY ===

    /**
     * Načte sort nastavení z localStorage
     */
    loadSortFromStorage() {
        return ErrorHandler.handleNonCritical(() => {
            const saved = localStorage.getItem('fileBrowser.sort')
            return saved ? JSON.parse(saved) : { field: 'createdAt', order: 'desc' }
        }, {
            operation: 'loadSortFromStorage',
            fallback: { field: 'createdAt', order: 'desc' }
        })
    }

    /**
     * Uloží sort nastavení do localStorage
     */
    saveSortToStorage(sortConfig) {
        ErrorHandler.handleNonCritical(() => {
            localStorage.setItem('fileBrowser.sort', JSON.stringify(sortConfig))
        }, {
            operation: 'saveSortToStorage'
        })
    }

    /**
     * Řazení podle createdAt
     */
    sortByCreatedAt(event) {
        event.preventDefault()
        const order = event.currentTarget.dataset.order
        
        console.log(`Sorting by createdAt ${order}`)
        
        this.currentSort = { field: 'createdAt', order: order }
        this.saveSortToStorage(this.currentSort)
        this.updateSortButtons()
        this.refresh()
    }

    /**
     * Aktualizuje vzhled sort tlačítek
     */
    updateSortButtons() {
        if (this.hasSortDescButtonTarget && this.hasSortAscButtonTarget) {
            // Reset všech tlačítek
            this.sortDescButtonTarget.classList.remove('text-blue-600', 'dark:text-blue-400')
            this.sortDescButtonTarget.classList.add('text-gray-400')
            
            this.sortAscButtonTarget.classList.remove('text-blue-600', 'dark:text-blue-400')
            this.sortAscButtonTarget.classList.add('text-gray-400')
            
            // Zvýrazni aktivní tlačítko
            if (this.currentSort.order === 'desc') {
                this.sortDescButtonTarget.classList.remove('text-gray-400')
                this.sortDescButtonTarget.classList.add('text-blue-600', 'dark:text-blue-400')
            } else {
                this.sortAscButtonTarget.classList.remove('text-gray-400')
                this.sortAscButtonTarget.classList.add('text-blue-600', 'dark:text-blue-400')
            }
        }
    }

    // === API METODY ===

    /**
     * Refresh obsahu složky
     */
    async refresh(event) {
        if (event) {
            event.preventDefault()
        }
        
        console.log("Refreshing folder content...")
        this.showLoading()
        
        try {
            // Přidáme sort parametry do URL
            const url = new URL(this.folderApiUrlValue, window.location.origin)
            url.searchParams.set('sort', this.currentSort.field)
            url.searchParams.set('order', this.currentSort.order)
            
            const response = await fetch(url.toString())
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }
            
            const data = await response.json()
            console.log("API Response:", data)
            
            if (data.statusCode === 200 && data.status === 'ok') {
                this.updateFolderContent(data.data)
                console.log("Folder refreshed successfully")
            } else {
                throw new Error(data.message || 'Nepodařilo se načíst obsah složky')
            }
        } catch (error) {
            console.error("Refresh failed:", error)
            NotificationHelper.showError("Nepodařilo se obnovit obsah složky")
        } finally {
            this.hideLoading()
        }
    }

    /**
     * Aktualizace obsahu složky z AJAX dat
     */
    updateFolderContent(data) {
        if (!this.hasContentTarget) {
            console.error("Content target not found")
            return
        }

        console.log("📝 Updating folder content with data:", data)
        
        // Uložíme data pro view mode přepínání
        this.lastFolderData = data
        
        // Aktualizujeme sort state z API response
        if (data.sort) {
            this.currentSort = data.sort
            this.saveSortToStorage(this.currentSort)
        }
        
        // Určíme aktuální view mode
        const isGridView = this.viewModeValue === 'grid'
        
        // Vygenerujeme nový HTML obsah pomocí ViewRenderer
        const newContent = ViewRenderer.generateContentHTML(data, isGridView)
        console.log("🎨 Generated HTML content length:", newContent.length)
        
        // Aktualizujeme DOM
        this.contentTarget.innerHTML = newContent
        console.log("✅ DOM updated with new content")
        
        // Aktualizujeme sort tlačítka
        this.updateSortButtons()
        
        // Aktualizujeme navigační UI
        this.updateNavigationUI()
        
        // Zobrazíme úspěšnou zprávu pouze pokud to není automatický refresh při inicializaci
        if (data.showSuccessMessage !== false) {
            NotificationHelper.showSuccess("Obsah složky byl obnoven")
        }
    }

    /**
     * Obnovení aktuálního view módu bez AJAX volání
     */
    refreshCurrentView() {
        if (!this.hasContentTarget) {
            console.error("Content target not found")
            return
        }

        console.log("Refreshing current view mode to:", this.viewModeValue)
        
        // Pokud máme uložená data, použijeme je
        if (this.lastFolderData) {
            this.updateFolderContent(this.lastFolderData)
        } else {
            // Jinak zavoláme refresh ze serveru
            this.refresh()
        }
    }

    // === VIEW MODE METODY ===

    /**
     * Přepnutí na list view
     */
    switchToListView(event) {
        event.preventDefault()
        console.log("Switching to list view")
        
        if (this.viewModeValue !== 'list') {
            this.viewModeValue = 'list'
            this.updateViewModeButtons()
            this.refreshCurrentView()
        }
    }

    /**
     * Přepnutí na grid view
     */
    switchToGridView(event) {
        event.preventDefault()
        console.log("Switching to grid view")
        
        if (this.viewModeValue !== 'grid') {
            this.viewModeValue = 'grid'
            this.updateViewModeButtons()
            this.refreshCurrentView()
        }
    }

    /**
     * Aktualizace vzhledu view mode tlačítek
     */
    updateViewModeButtons() {
        if (this.hasListViewButtonTarget && this.hasGridViewButtonTarget) {
            if (this.viewModeValue === 'list') {
                this.listViewButtonTarget.classList.add('bg-blue-100', 'text-blue-600', 'dark:bg-blue-900', 'dark:text-blue-300')
                this.listViewButtonTarget.classList.remove('text-gray-500', 'dark:text-gray-400')
                
                this.gridViewButtonTarget.classList.remove('bg-blue-100', 'text-blue-600', 'dark:bg-blue-900', 'dark:text-blue-300')
                this.gridViewButtonTarget.classList.add('text-gray-500', 'dark:text-gray-400')
            } else {
                this.gridViewButtonTarget.classList.add('bg-blue-100', 'text-blue-600', 'dark:bg-blue-900', 'dark:text-blue-300')
                this.gridViewButtonTarget.classList.remove('text-gray-500', 'dark:text-gray-400')
                
                this.listViewButtonTarget.classList.remove('bg-blue-100', 'text-blue-600', 'dark:bg-blue-900', 'dark:text-blue-300')
                this.listViewButtonTarget.classList.add('text-gray-500', 'dark:text-gray-400')
            }
        }
    }

    // === DELEGOVANÉ METODY K MODULŮM ===

    // File Operations
    downloadFile(event) { this.fileOperations.downloadFile(event) }
    
    // Navigation Methods - delegované na hlavní controller
    navigateToFolderFromEvent(event) { 
        const folderId = parseInt(event.currentTarget.dataset.slozkaId)
        this.navigateToFolder(folderId)
    }
    goBack(event) { 
        this.goBackToParent()
    }
    
    // Create Folder Methods
    showCreateFolderModal(event) { this.fileOperations.showCreateFolderModal(event) }
    hideCreateFolderModal(event) { this.fileOperations.hideCreateFolderModal(event) }
    handleCreateFolderKeydown(event) { this.fileOperations.handleCreateFolderKeydown(event) }
    validateCreateFolderInput(event) { this.fileOperations.validateCreateFolderInput(event) }
    confirmCreateFolder(event) { this.fileOperations.confirmCreateFolder(event) }

    // Upload Controller
    showUpload(event) { this.uploadController.showUpload(event) }
    hideUpload(event) { this.uploadController.hideUpload(event) }
    initializeDropzone() { this.uploadController.initializeDropzone() }

    // Context Menu Controller
    showContextMenu(event) { this.contextMenuController.showContextMenu(event) }
    showFolderContextMenu(event) { this.contextMenuController.showFolderContextMenu(event) }
    hideContextMenu() { this.contextMenuController.hideContextMenu() }
    downloadFileFromContext(event) { this.contextMenuController.downloadFileFromContext(event) }
    renameFileFromContext(event) { this.contextMenuController.renameFileFromContext(event) }
    deleteFileFromContext(event) { this.contextMenuController.deleteFileFromContext(event) }
    deleteFolderFromContext(event) { this.contextMenuController.deleteFolderFromContext(event) }

    // Delete Modal Methods
    confirmDelete(event) { 
        // Rozlišíme mezi souborem a složkou
        if (this.contextMenuController.currentFolderId) {
            this.contextMenuController.confirmFolderDelete(event)
        } else {
            this.contextMenuController.confirmDelete(event)
        }
    }
    cancelDelete(event) { 
        // Rozlišíme mezi souborem a složkou
        if (this.contextMenuController.currentFolderId) {
            this.contextMenuController.cancelFolderDelete(event)
        } else {
            this.contextMenuController.cancelDelete(event)
        }
    }

    // Rename Modal Methods
    confirmRename(event) { this.contextMenuController.confirmRename(event) }
    cancelRename(event) { this.contextMenuController.cancelRename(event) }
    handleRenameKeydown(event) { this.contextMenuController.handleRenameKeydown(event) }
    validateRenameInput(event) { this.contextMenuController.validateRenameInput(event) }

    // === POMOCNÉ METODY ===

    /**
     * Zobrazí loading overlay
     */
    showLoading() {
        if (this.hasLoadingTarget) {
            this.loadingTarget.classList.remove("hidden")
        }
    }

    /**
     * Skryje loading overlay
     */
    hideLoading() {
        if (this.hasLoadingTarget) {
            this.loadingTarget.classList.add("hidden")
        }
    }

    // === GETTERY PRO MODULY ===

    /**
     * Getter pro dropzone instanci
     */
    get dropzone() {
        return this.uploadController ? this.uploadController.getDropzone() : null
    }

    /**
     * Vygeneruje download URL pro soubor
     * @param {string} fileId - ID souboru
     * @returns {string} - URL pro download
     */
    generateDownloadUrl(fileId) {
        return this.downloadUrlPatternValue.replace('__ID__', fileId)
    }

    /**
     * Vygeneruje delete URL pro soubor
     * @param {string} fileId - ID souboru
     * @returns {string} - URL pro delete
     */
    generateDeleteUrl(fileId) {
        return this.deleteUrlPatternValue.replace('__ID__', fileId)
    }

    /**
     * Vygeneruje rename URL pro soubor
     * @param {string} fileId - ID souboru
     * @returns {string} - URL pro rename
     */
    generateRenameUrl(fileId) {
        return this.renameUrlPatternValue.replace('__ID__', fileId)
    }

    /**
     * Vygeneruje upload URL pro složku
     * @param {string} folderId - ID složky
     * @returns {string} - URL pro upload
     */
    generateUploadUrl(folderId) {
        return this.uploadUrlPatternValue.replace('__FOLDER_ID__', folderId)
    }
}
