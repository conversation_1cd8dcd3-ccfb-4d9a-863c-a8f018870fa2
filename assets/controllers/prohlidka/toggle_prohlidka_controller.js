// assets/controllers/toggle_task_controller.js
import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ["checkbox1", "toggleProhlidkaDiv", "toggleZpusobOpravyDiv", "selectZpusobOpravy"]

    connect() {
        this.toggle(); // Initial check on load
        this.checkbox1Target.addEventListener('change', this.toggle.bind(this));
        this.selectZpusobOpravyTarget.addEventListener('change', this.toggle.bind(this));

    }

    toggle() {
        if (this.checkbox1Target.checked) {
            this.toggleProhlidkaDivTarget.style.display = "block";
        } else {
            this.toggleProhlidkaDivTarget.style.display = "none";
        }

        // Toggle visibility based on selectbox
        if (this.selectZpusobOpravyTarget.value) {
            this.toggleZpusobOpravyDivTarget.style.display = "block";
        } else {
            this.toggleZpusobOpravyDivTarget.style.display = "none";
        }
    }

    disconnect() {
        this.checkbox1Target.removeEventListener('change', this.toggle.bind(this));
        this.selectZpusobOpravyTarget.removeEventListener('change', this.toggle.bind(this));
    }

}
