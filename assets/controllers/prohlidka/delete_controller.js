import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static values = {
        prohlidkaId: Number,
        deleteProhlidkaUrl: String
    };

    connect() {
        // Inicializace controlleru
    }

    // Smaže prohlídku
    delete(event) {
        event.preventDefault();
        
        if (!confirm('Opravdu chcete smazat tuto prohlídku?')) {
            return;
        }
        
        fetch(this.deleteProhlidkaUrlValue, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Chyba při ma<PERSON>án<PERSON> prohlídky');
            }
            return response.json();
        })
        .then(response => {
            // Po úspěšném smazání obnovíme stránku
            window.location.reload();
            
            // Zobrazení flash zprávy
            this.showFlashMessage('Prohlídka byla úspěšně smazána', 'success');
        })
        .catch(error => {
            console.error('Chyba:', error);
            this.showFlashMessage('Chyba při ma<PERSON>án<PERSON> prohlídky', 'error');
        });
    }
    
    // Pomocná metoda pro zobrazení flash zprávy
    showFlashMessage(message, type) {
        const event = new CustomEvent('flash-message', {
            detail: {
                message: message,
                type: type
            }
        });
        document.dispatchEvent(event);
    }
}
