import { Controller } from '@hotwired/stimulus';
import Datepicker from 'flowbite-datepicker/Datepicker';
import cs from 'flowbite-datepicker/locales/cs';
Object.assign(Datepicker.locales, cs);

export default class extends Controller {
    static targets = ['datumInput'];
    static values = {
        prohlidkaId: Number,
        updateDatumUrl: String
    };

    connect() {
        // Inicializace controlleru

        const datepickerEl1 = this.datumInputTarget;
        const datepicker1 = new Datepicker(datepickerEl1, {
            format: 'dd.mm.yyyy',
            orientation: 'bottom',
            autohide: true,
            todayBtn: true,
            language: 'cs',
            onHide: () => {
                let value = this.datumInputTarget.value;
                console.log('value');
            }
        });

        console.log('CONNECT');

        // Event listener pro změnu data
        this.datumInputTarget.addEventListener('changeDate', this.handleDateChange.bind(this));

    }

    // Handler pro změnu data
    handleDateChange(event) {
        const selectedDateValue = this.datumInputTarget.value;

        const spinner = document.getElementById('prohlidka-spinner');
        spinner.classList.remove("hidden");
        
        this.updateDatum(selectedDateValue);

    }


    // Aktualizuje datum prohlídky
    updateDatum(selectedDate) {
        // Formátování data pro API
        //const formattedDate = selectedDate.toISOString().split('T')[0];
        const formattedDate = selectedDate;

        fetch(this.updateDatumUrlValue, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                datum: formattedDate
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Chyba při aktualizaci data prohlídky');
                }
                return response.json();
            })
            .then(response => {
                // Po úspěšné aktualizaci obnovíme stránku pro zobrazení nového data
                window.location.reload();

                // Zobrazení flash zprávy
                this.showFlashMessage('Datum prohlídky bylo úspěšně aktualizováno', 'success');
            })
            .catch(error => {
                console.error('Chyba:', error);
                this.showFlashMessage('Chyba při aktualizaci data prohlídky', 'error');

                // Obnovení stránky pro zobrazení původního stavu
                window.location.reload();
            });
    }

    // Pomocná metoda pro zobrazení flash zprávy
    showFlashMessage(message, type) {
        const event = new CustomEvent('flash-message', {
            detail: {
                message: message,
                type: type
            }
        });
        document.dispatchEvent(event);
    }

}
