import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
    static targets = ['displayButton', 'formContainer', 'selectInput'];
    static values = {
        pojistnaUdalostId: Number,
        getUsersUrl: String,
        saveProhlidkaUrl: String
    };

    connect() {
        // Inicializace controlleru
    }

    // Zobrazí formulář pro přidání prohlídky
    showForm(event) {
        event.preventDefault();
        this.displayButtonTarget.classList.add('hidden');
        this.formContainerTarget.classList.remove('hidden');

        // Načtení seznamu uživatelů
        this.loadUsers();
    }

    // Skryje formul<PERSON>ř a zobrazí tlačítko
    hideForm(event) {
        event.preventDefault();
        this.displayButtonTarget.classList.remove('hidden');
        this.formContainerTarget.classList.add('hidden');
    }

    // Načte seznam uživatelů (techniků) pomocí AJAX
    loadUsers() {
        // Zobrazíme spinner
        document.getElementById('prohlidka-spinner').classList.remove('hidden');

        fetch(this.getUsersUrlValue, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Chyba při načítání uživatelů');
            }
            return response.json();
        })
        .then(data => {
            // Vyčistíme select
            this.selectInputTarget.innerHTML = '<option value="">Vyberte technika</option>';

            // Přidáme uživatele do selectu
            data.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} ${user.surname}`;
                this.selectInputTarget.appendChild(option);
            });

            // Skryjeme spinner
            document.getElementById('prohlidka-spinner').classList.add('hidden');
        })
        .catch(error => {
            console.error('Chyba při načítání uživatelů:', error);
            this.showFlashMessage('Chyba při načítání uživatelů', 'error');

            // Skryjeme spinner i v případě chyby
            document.getElementById('prohlidka-spinner').classList.add('hidden');
        });
    }

    // Uloží novou prohlídku
    save(event) {
        const technikId = this.selectInputTarget.value;

        // Pokud není vybrán technik, nic neděláme
        if (!technikId) {
            return;
        }

        // Zobrazíme spinner
        document.getElementById('prohlidka-spinner').classList.remove('hidden');

        fetch(this.saveProhlidkaUrlValue, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                technikId: technikId,
                pojistnaUdalostId: this.pojistnaUdalostIdValue
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Chyba při ukládání prohlídky');
            }
            return response.json();
        })
        .then(response => {
            // Zobrazení flash zprávy
            this.showFlashMessage('Prohlídka byla úspěšně přidána', 'success');

            // Po úspěšném uložení obnovíme stránku pro zobrazení nové prohlídky
            // Spinner se skryje při načtení nové stránky
            window.location.reload();
        })
        .catch(error => {
            console.error('Chyba:', error);
            this.showFlashMessage('Chyba při ukládání prohlídky', 'error');

            // Skryjeme spinner i v případě chyby
            document.getElementById('prohlidka-spinner').classList.add('hidden');
        });
    }

    // Zruší přidání prohlídky
    cancel(event) {
        event.preventDefault();
        this.hideForm(event);
    }

    // Pomocná metoda pro zobrazení flash zprávy
    showFlashMessage(message, type) {
        const event = new CustomEvent('flash-message', {
            detail: {
                message: message,
                type: type
            }
        });
        document.dispatchEvent(event);
    }
}
