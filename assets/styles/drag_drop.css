/**
 * CSS styly pro drag & drop funkcionalitu file browseru
 * KROK 2: Základní visual feedback pro drag operace
 */

/* Styl pro element během drag operace */
.dragging {
    opacity: 0.5;
    transform: scale(0.95);
    transition: all 0.2s ease;
    border: 2px dashed #3b82f6 !important;
    background-color: #eff6ff !important;
}

/* Dark mode varianta */
.dark .dragging {
    background-color: #1e3a8a !important;
    border-color: #60a5fa !important;
}

/* Cursor během drag operace */
.dragging * {
    cursor: grabbing !important;
}

/* Drop zone styly - KROK 3 */
.drop-zone-active {
    background-color: #dbeafe !important;
    border: 2px dashed #3b82f6 !important;
    border-radius: 8px;
    position: relative;
    transition: all 0.2s ease;
}

/* Dark mode varianta pro drop zone */
.dark .drop-zone-active {
    background-color: #1e3a8a !important;
    border-color: #60a5fa !important;
}

/* Overlay efekt pro drop zone */
.drop-zone-active::before {
    content: "📁 Přetáhněte sem soubor";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(59, 130, 246, 0.9);
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    z-index: 10;
    pointer-events: none;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Dark mode overlay */
.dark .drop-zone-active::before {
    background-color: rgba(96, 165, 250, 0.9);
    color: #1f2937;
}

.drop-zone-hover {
    /* Rezervováno pro budoucí použití */
}

/* Animace pro úspěšný drop */
.drop-success {
    /* Bude implementováno v KROKU 5 */
}
