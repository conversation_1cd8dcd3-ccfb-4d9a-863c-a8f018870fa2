image: php:8.3-cli

pipelines:
  pull-requests:
    '**':    # Pro pull requesty do jakékoliv větve
     - step:
         name: Build and Test
         script:
           - apt-get update
           # install dependencies
           - apt-get install -y libfreetype6-dev libjpeg62-turbo-dev libpng-dev git gcc make musl-dev zlib1g-dev libzip-dev
           - docker-php-ext-install gd mysqli pdo pdo_mysql zip
           # composer tool
           - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
           # build
           - export COMPOSER_ALLOW_SUPERUSER=1
           - composer install --no-interaction
           - vendor/bin/phpstan analyse --memory-limit=2G
  
  branches:
    demo:
      - step:
          name: Build and Test
          script:
            - apt-get update
            # install dependencies
            - apt-get install -y libfreetype6-dev libjpeg62-turbo-dev libpng-dev git gcc make musl-dev zlib1g-dev libzip-dev
            - docker-php-ext-install gd mysqli pdo pdo_mysql zip
            # composer tool
            - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
            # build
            - export COMPOSER_ALLOW_SUPERUSER=1
            - composer install --no-interaction --dev
            - vendor/bin/phpstan analyse --memory-limit=2G
      - step:
          name: Deploy to Demo
          deployment: demo
          script:
            - apt-get update
            # install dependencies
            - apt-get install -y libfreetype6-dev libjpeg62-turbo-dev libpng-dev git gcc make musl-dev zlib1g-dev libzip-dev openssh-client
            - docker-php-ext-install gd mysqli pdo pdo_mysql zip
            # install node and npm for assets compilation
            - curl -sL https://deb.nodesource.com/setup_18.x | bash -
            - apt-get install -y nodejs
            # composer tool
            - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
            # build
            - export COMPOSER_ALLOW_SUPERUSER=1
            - composer install --no-interaction --dev
            # setup SSH for deployment (using Bitbucket Access keys)
            - mkdir -p ~/.ssh
            - ssh-keyscan -t rsa tesarik.uwserver.cz >> ~/.ssh/known_hosts
            # deploy using Deployer
            - vendor/bin/dep deploy demo -vvv
            # run database migrations
            - vendor/bin/dep database:migrate demo
            # load demo fixtures
            - vendor/bin/dep database:fixtures demo
  