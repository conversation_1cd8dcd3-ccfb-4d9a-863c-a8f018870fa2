###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###

###> symfony/webpack-encore-bundle ###
/node_modules/
/public/build/
npm-debug.log
yarn-error.log
###< symfony/webpack-encore-bundle ###
###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###

###> phpstorm ###
.idea
###< phpstorm ###

###> local-project-config ###
php.ini
launch.json
.php-version
###< local-project-config ###

###> local-storage ###
/var/storage/slozky/
###< local-storage ###