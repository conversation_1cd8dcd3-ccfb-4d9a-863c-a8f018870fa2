<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250501174225 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Přidání nových sloupců do tabulky poznamka a nastavení with_task na true pro záznamy s resitelem nebo termínem';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE poznamka ADD with_task TINYINT(1) NOT NULL, ADD headline VARCHAR(255) NOT NULL, ADD pinned TINYINT(1) NOT NULL, ADD notify_zadavatel TINYINT(1) NOT NULL');
    }

    public function postUp(Schema $schema): void
    {
        // Nastavení with_task na true pro záznamy, kde je resitel !== null nebo termin !== null
        $this->write('Aktualizace stávaj<PERSON><PERSON><PERSON><PERSON>ů - nastavení with_task na true pro záznamy s resitelem nebo termínem');
        $this->connection->executeQuery('UPDATE poznamka SET with_task = 1 WHERE resitel_id IS NOT NULL OR termin IS NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE poznamka DROP with_task, DROP headline, DROP pinned, DROP notify_zadavatel');
    }
}
