<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250418130721 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE prohlidka (id INT AUTO_INCREMENT NOT NULL, technik_id INT NOT NULL, pojistna_udalost_id INT NOT NULL, datum DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_880852363F1A3B93 (technik_id), INDEX IDX_8808523625F24836 (pojistna_udalost_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE prohlidka ADD CONSTRAINT FK_880852363F1A3B93 FOREIGN KEY (technik_id) REFERENCES `user` (id)');
        $this->addSql('ALTER TABLE prohlidka ADD CONSTRAINT FK_8808523625F24836 FOREIGN KEY (pojistna_udalost_id) REFERENCES pojistna_udalost (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE prohlidka DROP FOREIGN KEY FK_880852363F1A3B93');
        $this->addSql('ALTER TABLE prohlidka DROP FOREIGN KEY FK_8808523625F24836');
        $this->addSql('DROP TABLE prohlidka');
    }
}
