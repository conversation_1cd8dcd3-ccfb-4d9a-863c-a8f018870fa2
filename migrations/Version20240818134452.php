<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240818134452 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE soubor (id INT AUTO_INCREMENT NOT NULL, slozka_id INT NOT NULL, autor_id INT NOT NULL, nazev VARCHAR(255) NOT NULL, typ ENUM(\'local\', \'video\', \'dokument\', \'PDF\', \'jine\'), velikost VARCHAR(255) NOT NULL, identifikator VARCHAR(255) NOT NULL, uloziste ENUM(\'local\', \'S3\', \'FTP\', \'SAMBA\'), datum_vytvoreni DATE NOT NULL, INDEX IDX_6CCA16C88B5C86F9 (slozka_id), INDEX IDX_6CCA16C814D45BBE (autor_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE soubor ADD CONSTRAINT FK_6CCA16C88B5C86F9 FOREIGN KEY (slozka_id) REFERENCES slozka (id)');
        $this->addSql('ALTER TABLE soubor ADD CONSTRAINT FK_6CCA16C814D45BBE FOREIGN KEY (autor_id) REFERENCES `user` (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE soubor DROP FOREIGN KEY FK_6CCA16C88B5C86F9');
        $this->addSql('ALTER TABLE soubor DROP FOREIGN KEY FK_6CCA16C814D45BBE');
        $this->addSql('DROP TABLE soubor');
    }
}
