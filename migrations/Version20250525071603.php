<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250525071603 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE slozka ADD parent_id INT DEFAULT NULL, ADD cesta VARCHAR(500) DEFAULT NULL');
        $this->addSql('ALTER TABLE slozka ADD CONSTRAINT FK_E629ADF1727ACA70 FOREIGN KEY (parent_id) REFERENCES slozka (id)');
        $this->addSql('CREATE INDEX IDX_E629ADF1727ACA70 ON slozka (parent_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE slozka DROP FOREIGN KEY FK_E629ADF1727ACA70');
        $this->addSql('DROP INDEX IDX_E629ADF1727ACA70 ON slozka');
        $this->addSql('ALTER TABLE slozka DROP parent_id, DROP cesta');
    }
}
