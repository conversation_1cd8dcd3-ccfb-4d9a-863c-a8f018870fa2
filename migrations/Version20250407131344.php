<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250407131344 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost ADD popis_vzniku LONGTEXT DEFAULT NULL, ADD rozsah_poskozeni LONGTEXT DEFAULT NULL, CHANGE misto_pu misto_pu VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost DROP popis_vzniku, DROP rozsah_pos<PERSON><PERSON>i, CHANGE misto_pu misto_pu VARCHAR(255) NOT NULL');
    }
}
