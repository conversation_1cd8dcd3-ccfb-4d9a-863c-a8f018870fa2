<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250522111119 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost ADD pszrizika_id INT DEFAULT NULL, ADD psvpp_id INT DEFAULT NULL, ADD psnazev VARCHAR(255) DEFAULT NULL, ADD psplatnost_od DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', ADD psmisto_pojisteni VARCHAR(255) DEFAULT NULL, ADD pszpojisteni VARCHAR(255) DEFAULT NULL, ADD psvarianta_pojisteni VARCHAR(255) DEFAULT NULL, ADD pspojisteno VARCHAR(255) DEFAULT NULL, ADD pslimit_riziko_na VARCHAR(255) DEFAULT NULL, CHANGE castka_pojistna castka_pojistna INT DEFAULT NULL, CHANGE castka_spoluucast castka_spoluucast INT DEFAULT NULL, CHANGE castka_limit_riziko castka_limit_riziko INT DEFAULT NULL, CHANGE castka_zpusob_opravy castka_zpusob_opravy INT DEFAULT NULL');
        $this->addSql('ALTER TABLE pojistna_udalost ADD CONSTRAINT FK_F9E7E865B5E2D14 FOREIGN KEY (pszrizika_id) REFERENCES rizika (id)');
        $this->addSql('ALTER TABLE pojistna_udalost ADD CONSTRAINT FK_F9E7E86C26436E2 FOREIGN KEY (psvpp_id) REFERENCES pojistne_podminky (id)');
        $this->addSql('CREATE INDEX IDX_F9E7E865B5E2D14 ON pojistna_udalost (pszrizika_id)');
        $this->addSql('CREATE INDEX IDX_F9E7E86C26436E2 ON pojistna_udalost (psvpp_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost DROP FOREIGN KEY FK_F9E7E865B5E2D14');
        $this->addSql('ALTER TABLE pojistna_udalost DROP FOREIGN KEY FK_F9E7E86C26436E2');
        $this->addSql('DROP INDEX IDX_F9E7E865B5E2D14 ON pojistna_udalost');
        $this->addSql('DROP INDEX IDX_F9E7E86C26436E2 ON pojistna_udalost');
        $this->addSql('ALTER TABLE pojistna_udalost DROP pszrizika_id, DROP psvpp_id, DROP psnazev, DROP psplatnost_od, DROP psmisto_pojisteni, DROP pszpojisteni, DROP psvarianta_pojisteni, DROP pspojisteno, DROP pslimit_riziko_na, CHANGE castka_pojistna castka_pojistna VARCHAR(64) DEFAULT NULL, CHANGE castka_zpusob_opravy castka_zpusob_opravy VARCHAR(64) DEFAULT NULL, CHANGE castka_spoluucast castka_spoluucast VARCHAR(64) DEFAULT NULL, CHANGE castka_limit_riziko castka_limit_riziko VARCHAR(64) DEFAULT NULL');
    }
}
