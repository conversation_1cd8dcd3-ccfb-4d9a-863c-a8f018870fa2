<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241023092518 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE soubor CHANGE typ typ ENUM(\'pdf\', \'jpeg\', \'jpg\', \'png\', \'gif\', \'doc\', \'docx\'), CHANGE uloziste uloziste ENUM(\'local\', \'S3\', \'FTP\', \'SAMBA\')');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE soubor CHANGE typ typ VARCHAR(255) DEFAULT NULL, CHANGE uloziste uloziste VARCHAR(255) DEFAULT NULL');
    }
}
