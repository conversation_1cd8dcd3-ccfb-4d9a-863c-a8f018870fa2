<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240504182455 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE pojistna_udalost (id INT AUTO_INCREMENT NOT NULL, zadavatel_id INT NOT NULL, likvidator_id INT DEFAULT NULL, cislo_pojistna_udalost VARCHAR(128) NOT NULL, cislo_skodni_udalost VARCHAR(128) NOT NULL, kategorie VARCHAR(128) NOT NULL, stav_likvidace VARCHAR(128) NOT NULL, prohlidka_typ VARCHAR(128) NOT NULL, prohlidka_datum DATE DEFAULT NULL, prohlidka_misto LONGTEXT DEFAULT NULL, datum_prijeti_od_pojistovny DATETIME DEFAULT NULL, datum_vzniku_skody DATETIME DEFAULT NULL, datum_ukonceni_likvidace DATETIME DEFAULT NULL, rozsah_poskozeni LONGTEXT DEFAULT NULL, zpusob_opravy VARCHAR(64) DEFAULT NULL, castka_pojistna VARCHAR(64) DEFAULT NULL, castka_spoluucast VARCHAR(64) DEFAULT NULL, castka_limit_riziko VARCHAR(64) DEFAULT NULL, vinkulace TINYINT(1) DEFAULT NULL, regres TINYINT(1) DEFAULT NULL, regres_udaje LONGTEXT DEFAULT NULL, poznamka LONGTEXT DEFAULT NULL, servis_nazev LONGTEXT DEFAULT NULL, servis_cislo_uctu VARCHAR(128) DEFAULT NULL, stanoveni_pravniho_zakaladu TINYINT(1) DEFAULT NULL, typ_vpp VARCHAR(128) DEFAULT NULL, typ_pojisteni VARCHAR(128) DEFAULT NULL, datum_nahlaseni_skody DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', datum_vytvoreni DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_F9E7E86D989EBDA (zadavatel_id), INDEX IDX_F9E7E86FC23928C (likvidator_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE pojistna_udalost ADD CONSTRAINT FK_F9E7E86D989EBDA FOREIGN KEY (zadavatel_id) REFERENCES zadavatel (id)');
        $this->addSql('ALTER TABLE pojistna_udalost ADD CONSTRAINT FK_F9E7E86FC23928C FOREIGN KEY (likvidator_id) REFERENCES `user` (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost DROP FOREIGN KEY FK_F9E7E86D989EBDA');
        $this->addSql('ALTER TABLE pojistna_udalost DROP FOREIGN KEY FK_F9E7E86FC23928C');
        $this->addSql('DROP TABLE pojistna_udalost');
    }
}
