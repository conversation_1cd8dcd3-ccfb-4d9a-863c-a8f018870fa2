<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241114221805 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE notifikace (id INT AUTO_INCREMENT NOT NULL, uzivatel_id INT NOT NULL, titulek VARCHAR(255) NOT NULL, obsah LONGTEXT NOT NULL, typ VARCHAR(63) NOT NULL, datum_vytvoreni DATETIME NOT NULL, precteno TINYINT(1) NOT NULL, INDEX IDX_5FF01419B3651C6 (uzivatel_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE notifikace ADD CONSTRAINT FK_5FF01419B3651C6 FOREIGN KEY (uzivatel_id) REFERENCES `user` (id)');
        $this->addSql('ALTER TABLE soubor CHANGE typ typ ENUM(\'pdf\', \'jpeg\', \'jpg\', \'png\', \'gif\', \'doc\', \'docx\'), CHANGE velikost velikost INT NOT NULL, CHANGE uloziste uloziste ENUM(\'local\', \'S3\', \'FTP\', \'SAMBA\')');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE notifikace DROP FOREIGN KEY FK_5FF01419B3651C6');
        $this->addSql('DROP TABLE notifikace');
        $this->addSql('ALTER TABLE soubor CHANGE typ typ VARCHAR(255) DEFAULT NULL, CHANGE velikost velikost VARCHAR(255) NOT NULL, CHANGE uloziste uloziste VARCHAR(255) DEFAULT NULL');
    }
}
