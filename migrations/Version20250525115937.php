<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migrace pro doplnění parent_id a cesta u existuj<PERSON>c<PERSON><PERSON> složek
 */
final class Version20250525115937 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Doplnění parent_id a cesta pro existující složky';
    }

    public function up(Schema $schema): void
    {
        // Žádné změny schématu - pouze data
    }

    public function down(Schema $schema): void
    {
        // Žádné změny schématu - pouze data
    }
    
    public function postUp(Schema $schema): void
    {
        $this->write('Začínám doplňování parent_id a cesta pro existující složky...');
        
        // 1. Najdi všechny root složky a nastav jim cestu
        $this->write('Krok 1: Nastavování cest pro root složky...');
        $rootSlozky = $this->connection->executeQuery(
            "SELECT id, nazev, identifikator FROM slozka WHERE druh_slozky = 'root'"
        )->fetchAllAssociative();
        
        $rootCount = 0;
        foreach ($rootSlozky as $root) {
            $cesta = $root['nazev'] . '_' . $root['identifikator'];
            $this->connection->executeQuery(
                'UPDATE slozka SET cesta = ? WHERE id = ?',
                [$cesta, $root['id']]
            );
            $rootCount++;
        }
        $this->write("Nastaveno cest pro {$rootCount} root složek");
        
        // 2. Pro každou pojistnou událost najdi root a přiřaď ostatním složkám
        $this->write('Krok 2: Nastavování parent_id a cest pro pod-složky...');
        $pojistneUdalosti = $this->connection->executeQuery(
            "SELECT DISTINCT pojistna_udalost_id FROM slozka"
        )->fetchAllAssociative();
        
        $childCount = 0;
        foreach ($pojistneUdalosti as $pu) {
            // Najdi root pro tuto PU
            $rootData = $this->connection->executeQuery(
                "SELECT id, nazev, identifikator FROM slozka WHERE pojistna_udalost_id = ? AND druh_slozky = 'root'",
                [$pu['pojistna_udalost_id']]
            )->fetchAssociative();
            
            if ($rootData) {
                $rootCesta = $rootData['nazev'] . '_' . $rootData['identifikator'];
                
                // Najdi všechny non-root složky pro tuto PU
                $childSlozky = $this->connection->executeQuery(
                    "SELECT id, nazev, identifikator FROM slozka WHERE pojistna_udalost_id = ? AND druh_slozky != 'root'",
                    [$pu['pojistna_udalost_id']]
                )->fetchAllAssociative();
                
                foreach ($childSlozky as $child) {
                    $childCesta = $rootCesta . '/' . $child['nazev'] . '_' . $child['identifikator'];
                    
                    $this->connection->executeQuery(
                        "UPDATE slozka SET parent_id = ?, cesta = ? WHERE id = ?",
                        [$rootData['id'], $childCesta, $child['id']]
                    );
                    $childCount++;
                }
            } else {
                $this->write("VAROVÁNÍ: Nenalezena root složka pro pojistnou událost ID: " . $pu['pojistna_udalost_id']);
            }
        }
        
        $this->write("Nastaveno parent_id a cest pro {$childCount} pod-složek");
        $this->write('Dokončeno doplňování parent_id a cesta pro existující složky.');
        
        // 3. Kontrolní výpis
        $totalSlozky = $this->connection->executeQuery("SELECT COUNT(*) as count FROM slozka")->fetchAssociative();
        $slozkyWithCesta = $this->connection->executeQuery("SELECT COUNT(*) as count FROM slozka WHERE cesta IS NOT NULL")->fetchAssociative();
        $slozkyWithParent = $this->connection->executeQuery("SELECT COUNT(*) as count FROM slozka WHERE parent_id IS NOT NULL")->fetchAssociative();
        
        $this->write("Kontrola: Celkem složek: {$totalSlozky['count']}, s cestou: {$slozkyWithCesta['count']}, s parent: {$slozkyWithParent['count']}");
    }
}
