<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250321134611 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE pojistna_udalost_user (pojistna_udalost_id INT NOT NULL, user_id INT NOT NULL, INDEX IDX_15E5BDFA25F24836 (pojistna_udalost_id), INDEX IDX_15E5BDFAA76ED395 (user_id), PRIMARY KEY(pojistna_udalost_id, user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE pojistna_udalost_user ADD CONSTRAINT FK_15E5BDFA25F24836 FOREIGN KEY (pojistna_udalost_id) REFERENCES pojistna_udalost (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE pojistna_udalost_user ADD CONSTRAINT FK_15E5BDFAA76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost_user DROP FOREIGN KEY FK_15E5BDFA25F24836');
        $this->addSql('ALTER TABLE pojistna_udalost_user DROP FOREIGN KEY FK_15E5BDFAA76ED395');
        $this->addSql('DROP TABLE pojistna_udalost_user');
    }
}
