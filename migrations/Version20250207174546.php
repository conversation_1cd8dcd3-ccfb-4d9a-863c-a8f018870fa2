<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250207174546 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE pojistne_podminky_soubor (id INT AUTO_INCREMENT NOT NULL, pojistne_podminky_id INT NOT NULL, filename VARCHAR(255) NOT NULL, original_filename VARCHAR(255) NOT NULL, UNIQUE INDEX UNIQ_9236AC40B648664E (pojistne_podminky_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE pojistne_podminky_soubor ADD CONSTRAINT FK_9236AC40B648664E FOREIGN KEY (pojistne_podminky_id) REFERENCES pojistne_podminky (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistne_podminky_soubor DROP FOREIGN KEY FK_9236AC40B648664E');
        $this->addSql('DROP TABLE pojistne_podminky_soubor');
    }
}
