<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240605075451 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE slozka (id INT AUTO_INCREMENT NOT NULL, pojistna_udalost_id INT DEFAULT NULL, nazev VARCHAR(255) NOT NULL, sdileni_zapis TINYINT(1) NOT NULL, sdileni_cteni TINYINT(1) NOT NULL, identifikator VARCHAR(255) NOT NULL, uloziste VARCHAR(16) NOT NULL, typ_slozky VARCHAR(16) NOT NULL, UNIQUE INDEX UNIQ_E629ADF125F24836 (pojistna_udalost_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE slozka ADD CONSTRAINT FK_E629ADF125F24836 FOREIGN KEY (pojistna_udalost_id) REFERENCES pojistna_udalost (id)');
        $this->addSql('ALTER TABLE pojistna_udalost ADD slozka_root_id INT DEFAULT NULL, ADD slozka_pojistovna_zadani_id INT DEFAULT NULL, ADD slozka_klient_id INT DEFAULT NULL, ADD slozka_technik_id INT DEFAULT NULL, ADD slozka_pojistovna_ukonceni_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE pojistna_udalost ADD CONSTRAINT FK_F9E7E869FC44900 FOREIGN KEY (slozka_root_id) REFERENCES slozka (id)');
        $this->addSql('ALTER TABLE pojistna_udalost ADD CONSTRAINT FK_F9E7E86D17AACF8 FOREIGN KEY (slozka_pojistovna_zadani_id) REFERENCES slozka (id)');
        $this->addSql('ALTER TABLE pojistna_udalost ADD CONSTRAINT FK_F9E7E8683830268 FOREIGN KEY (slozka_klient_id) REFERENCES slozka (id)');
        $this->addSql('ALTER TABLE pojistna_udalost ADD CONSTRAINT FK_F9E7E862357EA38 FOREIGN KEY (slozka_technik_id) REFERENCES slozka (id)');
        $this->addSql('ALTER TABLE pojistna_udalost ADD CONSTRAINT FK_F9E7E86C1F9E0DE FOREIGN KEY (slozka_pojistovna_ukonceni_id) REFERENCES slozka (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_F9E7E869FC44900 ON pojistna_udalost (slozka_root_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_F9E7E86D17AACF8 ON pojistna_udalost (slozka_pojistovna_zadani_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_F9E7E8683830268 ON pojistna_udalost (slozka_klient_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_F9E7E862357EA38 ON pojistna_udalost (slozka_technik_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_F9E7E86C1F9E0DE ON pojistna_udalost (slozka_pojistovna_ukonceni_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost DROP FOREIGN KEY FK_F9E7E869FC44900');
        $this->addSql('ALTER TABLE pojistna_udalost DROP FOREIGN KEY FK_F9E7E86D17AACF8');
        $this->addSql('ALTER TABLE pojistna_udalost DROP FOREIGN KEY FK_F9E7E8683830268');
        $this->addSql('ALTER TABLE pojistna_udalost DROP FOREIGN KEY FK_F9E7E862357EA38');
        $this->addSql('ALTER TABLE pojistna_udalost DROP FOREIGN KEY FK_F9E7E86C1F9E0DE');
        $this->addSql('ALTER TABLE slozka DROP FOREIGN KEY FK_E629ADF125F24836');
        $this->addSql('DROP TABLE slozka');
        $this->addSql('DROP INDEX UNIQ_F9E7E869FC44900 ON pojistna_udalost');
        $this->addSql('DROP INDEX UNIQ_F9E7E86D17AACF8 ON pojistna_udalost');
        $this->addSql('DROP INDEX UNIQ_F9E7E8683830268 ON pojistna_udalost');
        $this->addSql('DROP INDEX UNIQ_F9E7E862357EA38 ON pojistna_udalost');
        $this->addSql('DROP INDEX UNIQ_F9E7E86C1F9E0DE ON pojistna_udalost');
        $this->addSql('ALTER TABLE pojistna_udalost DROP slozka_root_id, DROP slozka_pojistovna_zadani_id, DROP slozka_klient_id, DROP slozka_technik_id, DROP slozka_pojistovna_ukonceni_id');
    }
}
