<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250508125326 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Změna sloupce vyreseno v tabulce poznamka na NOT NULL s vý<PERSON><PERSON><PERSON> hodnotou false';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE poznamka CHANGE vyreseno vyreseno TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE poznamka CHANGE vyreseno vyreseno TINYINT(1) DEFAULT NULL');
    }
    
    public function preUp(Schema $schema): void
    {
        // Aktualizace stávaj<PERSON><PERSON><PERSON>ch záznamů - nastavení vyreseno na false (0) pro záznamy, kde bylo NULL
        $this->write('Aktualizace stávajících záznamů - nastavení vyreseno na false (0) pro záznamy, kde bylo NULL');
        $this->connection->executeQuery('UPDATE poznamka SET vyreseno = 0 WHERE vyreseno IS NULL');
    }
}
