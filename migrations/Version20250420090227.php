<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250420090227 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE ucastnik ADD adresa VARCHAR(255) DEFAULT NULL, CHANGE jmeno jmeno VARCHAR(128) DEFAULT NULL, CHANGE prijmeni prijmeni VARCHAR(128) DEFAULT NULL, <PERSON>AN<PERSON> rodne_cislo rodne_cislo VARCHAR(32) DEFAULT NULL, CHANGE email email VARCHAR(128) DEFAULT NULL, CHANGE cislo_uctu cislo_uctu VARCHAR(64) DEFAULT NULL, CHANGE pojisteny platce_dph TINYINT(1) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE ucastnik DROP adresa, CHANGE jmeno jmeno VARCHAR(128) NOT NULL, CHANGE prijmeni prijmeni VARCHAR(128) NOT NULL, CHANGE rodne_cislo rodne_cislo VARCHAR(32) NOT NULL, CHANGE email email VARCHAR(128) NOT NULL, CHANGE cislo_uctu cislo_uctu VARCHAR(64) NOT NULL, CHANGE platce_dph pojisteny TINYINT(1) NOT NULL');
    }
}
