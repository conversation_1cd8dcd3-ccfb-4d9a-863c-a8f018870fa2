<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250209153221 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost CHANGE cislo_skodni_udalost cislo_skodni_udalost VARCHAR(128) DEFAULT NULL, CHANGE rozsah_poskozeni popis_vzniku LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost CHANGE cislo_skodni_udalost cislo_skodni_udalost VARCHAR(128) NOT NULL, CHANGE popis_vzniku rozsah_poskozeni LONGTEXT DEFAULT NULL');
    }
}
