<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240619183847 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE vozidlo (id INT AUTO_INCREMENT NOT NULL, ucastnik_id INT NOT NULL, kod_rz VARCHAR(255) NOT NULL, kod_vin VARCHAR(255) NOT NULL, vykon VARCHAR(255) NOT NULL, obsah VARCHAR(255) NOT NULL, druh VARCHAR(128) NOT NULL, typ VARCHAR(255) NOT NULL, INDEX IDX_45A1FC997C3910B4 (ucastnik_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE vozidlo ADD CONSTRAINT FK_45A1FC997C3910B4 FOREIGN KEY (ucastnik_id) REFERENCES ucastnik (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE vozidlo DROP FOREIGN KEY FK_45A1FC997C3910B4');
        $this->addSql('DROP TABLE vozidlo');
    }
}
