<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250522120748 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE limit_rizika (id INT AUTO_INCREMENT NOT NULL, pojistna_udalost_id INT NOT NULL, z_rizika_id INT NOT NULL, pojistene_riziko VARCHAR(255) NOT NULL, castka_limit_rizika INT NOT NULL, limit_rizika_na VARCHAR(255) NOT NULL, INDEX IDX_79BA1B5325F24836 (pojistna_udalost_id), INDEX IDX_79BA1B53BD7F2D72 (z_rizika_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE limit_rizika ADD CONSTRAINT FK_79BA1B5325F24836 FOREIGN KEY (pojistna_udalost_id) REFERENCES pojistna_udalost (id)');
        $this->addSql('ALTER TABLE limit_rizika ADD CONSTRAINT FK_79BA1B53BD7F2D72 FOREIGN KEY (z_rizika_id) REFERENCES rizika (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE limit_rizika DROP FOREIGN KEY FK_79BA1B5325F24836');
        $this->addSql('ALTER TABLE limit_rizika DROP FOREIGN KEY FK_79BA1B53BD7F2D72');
        $this->addSql('DROP TABLE limit_rizika');
    }
}
