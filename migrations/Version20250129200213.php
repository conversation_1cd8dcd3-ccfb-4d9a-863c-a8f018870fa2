<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250129200213 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE soubor CHANGE typ typ ENUM(\'pdf\', \'jpeg\', \'jpg\', \'png\', \'gif\', \'doc\', \'docx\'), CHANGE uloziste uloziste ENUM(\'local\', \'S3\', \'FTP\', \'SAMBA\')');
        $this->addSql('ALTER TABLE ucastnik CHANGE pojistovna pojistovna VARCHAR(128) DEFAULT NULL, CHANGE cislo_smlouvy cislo_smlouvy VARCHAR(128) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE soubor CHANGE typ typ VARCHAR(255) DEFAULT NULL, CHANGE uloziste uloziste VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE ucastnik CHANGE pojistovna pojistovna VARCHAR(128) NOT NULL, CHANGE cislo_smlouvy cislo_smlouvy VARCHAR(128) NOT NULL');
    }
}
