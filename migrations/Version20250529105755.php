<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250529105755 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs

    }

    public function postUp(Schema $schema): void
    {
        // Aktualizace názvů složek podle jejich druhu
        $this->write('Aktualizace názvů složek podle jejich druhu');
        
        $this->connection->executeQuery("UPDATE slozka SET nazev = 'Od technika' WHERE druh_slozky = 'technik'");
        $this->connection->executeQuery("UPDATE slozka SET nazev = 'Od klienta' WHERE druh_slozky = 'klient'");
        $this->connection->executeQuery("UPDATE slozka SET nazev = 'Pro pojišťovnu' WHERE druh_slozky = 'pojistovna_ukonceni'");
        $this->connection->executeQuery("UPDATE slozka SET nazev = 'Od pojišťovny' WHERE druh_slozky = 'pojistovna_zadani'");
        $this->connection->executeQuery("UPDATE slozka SET nazev = CONCAT('PU-', pojistna_udalost_id, '_root') WHERE druh_slozky = 'root'");
    }
}
