<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240802130059 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE pojistna_udalost_rizika (pojistna_udalost_id INT NOT NULL, rizika_id INT NOT NULL, INDEX IDX_89BA967D25F24836 (pojistna_udalost_id), INDEX IDX_89BA967D64635F82 (rizika_id), PRIMARY KEY(pojistna_udalost_id, rizika_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE pojistna_udalost_rizika ADD CONSTRAINT FK_89BA967D25F24836 FOREIGN KEY (pojistna_udalost_id) REFERENCES pojistna_udalost (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE pojistna_udalost_rizika ADD CONSTRAINT FK_89BA967D64635F82 FOREIGN KEY (rizika_id) REFERENCES rizika (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE pojistna_udalost ADD castka_zpusob_opravy VARCHAR(64) DEFAULT NULL, ADD typ_fotodokumentace VARCHAR(128) NOT NULL, ADD cislo_pojistne_smlouvy VARCHAR(200) DEFAULT NULL, CHANGE stanoveni_pravniho_zakaladu stanoveni_pravniho_zakladu TINYINT(1) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost_rizika DROP FOREIGN KEY FK_89BA967D25F24836');
        $this->addSql('ALTER TABLE pojistna_udalost_rizika DROP FOREIGN KEY FK_89BA967D64635F82');
        $this->addSql('DROP TABLE pojistna_udalost_rizika');
        $this->addSql('ALTER TABLE pojistna_udalost DROP castka_zpusob_opravy, DROP typ_fotodokumentace, DROP cislo_pojistne_smlouvy, CHANGE stanoveni_pravniho_zakladu stanoveni_pravniho_zakaladu TINYINT(1) DEFAULT NULL');
    }
}
