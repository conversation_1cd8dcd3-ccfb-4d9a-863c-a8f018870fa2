<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240528101854 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE ucastnik (id INT AUTO_INCREMENT NOT NULL, pojistna_udalost_id INT NOT NULL, role_ucastnika VARCHAR(32) NOT NULL, jmeno VARCHAR(128) NOT NULL, prijmeni VARCHAR(128) NOT NULL, rodne_cislo VARCHAR(32) NOT NULL, telefon VARCHAR(32) NOT NULL, email VARCHAR(128) NOT NULL, cislo_uctu VARCHAR(64) NOT NULL, poznamka LONGTEXT DEFAULT NULL, pojistovna VARCHAR(128) NOT NULL, cislo_smlouvy VARCHAR(128) NOT NULL, firma VARCHAR(128) DEFAULT NULL, ico VARCHAR(128) DEFAULT NULL, sidlo LONGTEXT DEFAULT NULL, INDEX IDX_8E5DE9DA25F24836 (pojistna_udalost_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE ucastnik ADD CONSTRAINT FK_8E5DE9DA25F24836 FOREIGN KEY (pojistna_udalost_id) REFERENCES pojistna_udalost (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE ucastnik DROP FOREIGN KEY FK_8E5DE9DA25F24836');
        $this->addSql('DROP TABLE ucastnik');
    }
}
