<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use App\Enum\PuListColumn;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250412230236 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add pu_shown_cols column to user table with default values from PuListColumn enum';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user ADD pu_shown_cols JSON NOT NULL');

        // Získání hodnot z Enumu pomocí existující metody values()
        $defaultColumns = json_encode(PuListColumn::values());
        
        // Aktualizace všech existujících záznamů
        $this->addSql('UPDATE user SET pu_shown_cols = :defaultColumns', [
            'defaultColumns' => $defaultColumns
        ]);        
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE `user` DROP pu_shown_cols');
    }
}
