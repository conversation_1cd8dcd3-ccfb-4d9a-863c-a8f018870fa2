<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250514140938 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE ucastnik CHANGE telefon telefon VARCHAR(32) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE ucastnik CHANGE telefon telefon VARCHAR(32) NOT NULL');
    }

    public function preDown(Schema $schema): void
    {
            // Nastavení hodnoty telefon na prazdny string
            $this->write('Nastavení hodnoty telefon na prazdny string pro záznamy, které mají NULL');
            $this->connection->executeQuery('UPDATE ucastnik SET telefon = "" WHERE telefon IS NULL');

    }
}
