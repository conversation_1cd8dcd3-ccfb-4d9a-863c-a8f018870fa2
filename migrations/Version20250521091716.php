<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250521091716 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE vyzadany_dokument (id INT AUTO_INCREMENT NOT NULL, pojistna_udalost_id INT NOT NULL, autor_id INT NOT NULL, co_vyzadano LONGTEXT NOT NULL, stav VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_F14326F225F24836 (pojistna_udalost_id), INDEX IDX_F14326F214D45BBE (autor_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE vyzadany_dokument ADD CONSTRAINT FK_F14326F225F24836 FOREIGN KEY (pojistna_udalost_id) REFERENCES pojistna_udalost (id)');
        $this->addSql('ALTER TABLE vyzadany_dokument ADD CONSTRAINT FK_F14326F214D45BBE FOREIGN KEY (autor_id) REFERENCES `user` (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE vyzadany_dokument DROP FOREIGN KEY FK_F14326F225F24836');
        $this->addSql('ALTER TABLE vyzadany_dokument DROP FOREIGN KEY FK_F14326F214D45BBE');
        $this->addSql('DROP TABLE vyzadany_dokument');
    }
}
