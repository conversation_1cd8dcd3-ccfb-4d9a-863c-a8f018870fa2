<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250315151003 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE soubor_for_poznamka (id INT AUTO_INCREMENT NOT NULL, poznamka_id INT NOT NULL, slozka_id INT NOT NULL, filename VARCHAR(255) NOT NULL, original_filename VARCHAR(255) NOT NULL, mime_type VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_74BA0AB57876D6ED (poznamka_id), INDEX IDX_74BA0AB58B5C86F9 (slozka_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE soubor_for_poznamka ADD CONSTRAINT FK_74BA0AB57876D6ED FOREIGN KEY (poznamka_id) REFERENCES poznamka (id)');
        $this->addSql('ALTER TABLE soubor_for_poznamka ADD CONSTRAINT FK_74BA0AB58B5C86F9 FOREIGN KEY (slozka_id) REFERENCES slozka (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE soubor_for_poznamka DROP FOREIGN KEY FK_74BA0AB57876D6ED');
        $this->addSql('ALTER TABLE soubor_for_poznamka DROP FOREIGN KEY FK_74BA0AB58B5C86F9');
        $this->addSql('DROP TABLE soubor_for_poznamka');
    }
}
