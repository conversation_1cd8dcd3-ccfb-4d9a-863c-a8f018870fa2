<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250514132730 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Přidání sloupce company do tabulky ucastnik';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE ucastnik ADD company TINYINT(1) DEFAULT 0 NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE ucastnik DROP company');
    }
    
    public function postUp(Schema $schema): void
    {
        // Nastavení hodnoty company na 1 pro záznamy, které mají vyplněnou firmu
        $this->write('Nastavení hodnoty company na 1 pro záznamy, které mají vyplněnou firmu');
        $this->connection->executeQuery('UPDATE ucastnik SET company = 1 WHERE firma IS NOT NULL AND firma != ""');
    }
}
