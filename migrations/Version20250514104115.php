<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250514104115 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost ADD regres_name VARCHAR(255) DEFAULT NULL, ADD regres_tel VARCHAR(255) DEFAULT NULL, ADD regres_adresa VARCHAR(255) DEFAULT NULL, ADD castka_rezerva INT DEFAULT NULL, DROP regres_udaje');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE pojistna_udalost ADD regres_udaje LONGTEXT DEFAULT NULL, DROP regres_name, DROP regres_tel, DROP regres_adresa, DROP castka_rezerva');
    }
}
