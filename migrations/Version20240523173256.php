<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240523173256 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE poznamka (id INT AUTO_INCREMENT NOT NULL, pojistna_udalost_id INT NOT NULL, autor_id INT NOT NULL, resitel_id INT DEFAULT NULL, obsah LONGTEXT NOT NULL, cas_vytvoreno DATETIME NOT NULL, cas_upraveno DATETIME NOT NULL, cas_vyreseni DATETIME DEFAULT NULL, hlidat_splneni TINYINT(1) NOT NULL, termin DATE DEFAULT NULL, termin_cas TIME DEFAULT NULL, vyreseno TINYINT(1) DEFAULT NULL, vytvoreno_systemem TINYINT(1) NOT NULL, INDEX IDX_471EBEC925F24836 (pojistna_udalost_id), INDEX IDX_471EBEC914D45BBE (autor_id), INDEX IDX_471EBEC912A2850E (resitel_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE poznamka ADD CONSTRAINT FK_471EBEC925F24836 FOREIGN KEY (pojistna_udalost_id) REFERENCES pojistna_udalost (id)');
        $this->addSql('ALTER TABLE poznamka ADD CONSTRAINT FK_471EBEC914D45BBE FOREIGN KEY (autor_id) REFERENCES `user` (id)');
        $this->addSql('ALTER TABLE poznamka ADD CONSTRAINT FK_471EBEC912A2850E FOREIGN KEY (resitel_id) REFERENCES `user` (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE poznamka DROP FOREIGN KEY FK_471EBEC925F24836');
        $this->addSql('ALTER TABLE poznamka DROP FOREIGN KEY FK_471EBEC914D45BBE');
        $this->addSql('ALTER TABLE poznamka DROP FOREIGN KEY FK_471EBEC912A2850E');
        $this->addSql('DROP TABLE poznamka');
    }
}
