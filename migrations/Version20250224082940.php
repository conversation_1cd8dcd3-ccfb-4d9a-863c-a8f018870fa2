<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250224082940 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE soubor DROP FOREIGN KEY FK_6CCA16C814D45BBE');
        $this->addSql('DROP INDEX IDX_6CCA16C814D45BBE ON soubor');
        $this->addSql('ALTER TABLE soubor ADD filename VARCHAR(255) NOT NULL, ADD original_filename VARCHAR(255) NOT NULL, ADD mime_type VARCHAR(255) NOT NULL, ADD created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', DROP autor_id, DROP nazev, DROP typ, DROP velikost, DROP identifikator, DROP uloziste, DROP datum_vytvoreni');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE soubor ADD autor_id INT NOT NULL, ADD nazev VARCHAR(255) NOT NULL, ADD typ VARCHAR(255) DEFAULT NULL, ADD velikost INT NOT NULL, ADD identifikator VARCHAR(255) NOT NULL, ADD uloziste VARCHAR(255) DEFAULT NULL, ADD datum_vytvoreni DATE NOT NULL, DROP filename, DROP original_filename, DROP mime_type, DROP created_at');
        $this->addSql('ALTER TABLE soubor ADD CONSTRAINT FK_6CCA16C814D45BBE FOREIGN KEY (autor_id) REFERENCES user (id)');
        $this->addSql('CREATE INDEX IDX_6CCA16C814D45BBE ON soubor (autor_id)');
    }
}
