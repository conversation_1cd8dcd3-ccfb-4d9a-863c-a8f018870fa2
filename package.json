{"devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.0", "@hotwired/stimulus": "^3.0.0", "@hotwired/turbo": "^7.1.1 || ^8.0", "@symfony/stimulus-bridge": "^3.2.0", "@symfony/webpack-encore": "^5.0.0", "autoprefixer": "^10.4.19", "core-js": "^3.38.0", "postcss": "^8.4.38", "postcss-loader": "^7.3.4", "quill": "^2.0.3", "regenerator-runtime": "^0.13.9", "tailwindcss": "^3.4.3", "webpack": "^5.74.0", "webpack-cli": "^5.1.0", "webpack-notifier": "^1.15.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"dropzone": "^6.0.0-beta.2", "flowbite": "^2.3.0", "flowbite-datepicker": "^1.2.6", "lg-thumbnail": "^1.2.1", "lg-zoom": "^1.3.0", "lightbox2": "^2.11.4", "lightgallery": "^2.7.2"}}