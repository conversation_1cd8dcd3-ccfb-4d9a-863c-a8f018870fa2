<?php
namespace App\EventListener;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;

class ExceptionListener
{
    private MailerInterface $mailer;

    private string $fromEmail;
    private string $toEmail;

    public function __construct(
        string $fromEmail,
        string $toEmail,
        MailerInterface $mailer
    ) {
        $this->fromEmail = $fromEmail;
        $this->toEmail = $toEmail;

        $this->mailer = $mailer;
    }

    public function __invoke(ExceptionEvent $event) : void
    {
        $exception = $event->getThrowable();

        $email = (new Email())
            ->from($this->fromEmail)
            ->to($this->toEmail)
            ->subject('ININ - Chyba v aplikaci')
            ->text(SELF::buildMessage($exception));

        $this->mailer->send($email);
    }

    private static function buildMessage(\Throwable $exception): string
    {
        return sprintf(
            "Chyba v aplikaci: %s\n\n%s\n\n%s",
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getTraceAsString()
        );
    }
}
