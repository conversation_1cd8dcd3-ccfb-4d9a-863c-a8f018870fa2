<?php
declare(strict_types=1);

namespace App\Enum\Log;

enum LogSeverity: string
{
    case BUSINESS_CRITICAL = 'business_critical';
    case INTEGRATION_ERROR = 'integration_error';
    case SECURITY_ISSUE = 'security_issue';
    case PERFORMANCE_ISSUE = 'performance_issue';
    case VALIDATION_FAILURE = 'validation_failure';

    public function toMonologLevel(): int
    {
        return match($this) {
            self::BUSINESS_CRITICAL => 600, // EMERGENCY
            self::INTEGRATION_ERROR => 400, // ERROR
            self::SECURITY_ISSUE => 500,    // CRITICAL
            self::PERFORMANCE_ISSUE => 300, // WARNING
            self::VALIDATION_FAILURE => 300 // WARNING
        };
    }

    public function requiresImediateAction(): bool
    {
        return in_array($this, [
            self::BUSINESS_CRITICAL,
            self::SECURITY_ISSUE
        ]);
    }

    public function toString(): string
    {
        return $this->value;
    }
}

