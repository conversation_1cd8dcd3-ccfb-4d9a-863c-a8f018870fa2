<?php
declare(strict_types=1);

namespace App\Enum\Log;

enum LogChannel: string
{
    case API_EXTERNAL_OUTGOING = 'api_external_outgoing';
    case API_EXTERNAL_INCOMING = 'api_external_incoming';
    case API_INTERNAL = 'api_internal';

    public function toString(): string
    {
        return $this->value;
    }

    /**
     * 
     * @return array<mixed> 
     */
    public static function getChannelsList(): array
    {
        return array_column(self::cases(), 'value');
    }
}
