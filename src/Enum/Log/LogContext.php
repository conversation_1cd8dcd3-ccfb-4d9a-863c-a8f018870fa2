<?php
declare(strict_types=1);


namespace App\Enum\Log;

enum LogContext: string
{
    case INBOUND = 'inbound';
    case OUTBOUND = 'outbound';
    case INTERNAL = 'internal';
    case SYSTEM = 'system';

    public function isExternal(): bool
    {
        return in_array($this, [self::INBOUND, self::OUTBOUND]);
    }

    public function toString(): string
    {
        return $this->value;
    }

    public function getDirectionLabel(): string
    {
        return match($this) {
            self::INBOUND => '←',
            self::OUTBOUND => '→',
            self::INTERNAL => '↔',
            self::SYSTEM => '⚙'
        };
    }
}

