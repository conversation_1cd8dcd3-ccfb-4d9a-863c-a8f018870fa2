<?php
declare(strict_types=1);

namespace App\Enum\Log;

enum LogType: string
{
    case API_REQUEST = 'api_request';
    case API_RESPONSE = 'api_response';
    case API_ERROR = 'api_error';
    
    case VALIDATION_ERROR = 'validation_error';
    case AUTHENTICATION = 'authentication';
    case AUTHORIZATION = 'authorization';
    case SYSTEM_EVENT = 'system_event';

    public function isError(): bool
    {
        return in_array($this, [
            self::API_ERROR,
            self::VALIDATION_ERROR
        ]);
    }

    public function toString(): string
    {
        return $this->value;
    }
}
