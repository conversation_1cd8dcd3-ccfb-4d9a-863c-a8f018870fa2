<?php

namespace App\Enum;

enum RoleUcastnika: string
{

    case ROLE_POJISTENY = 'Pojištěný';
    case ROLE_POSKOZENY = 'Poškozený';
    case ROLE_VINIK = 'Viník';
    case ROLE_POVERENA_OSOBA = 'Pověřená osoba';
    case ROLE_KONTAKTNI_OSOBA = 'Kontaktní osoba';

    public static function fromString(string $value): self
    {
        return match ($value) {
            'Pojištěný' => self::ROLE_POJISTENY,
            'Poškozený' => self::ROLE_POSKOZENY,
            'Viník' => self::ROLE_VINIK,
            'Pověřená osoba' => self::ROLE_POVERENA_OSOBA,
            'Kontaktní osoba' => self::ROLE_KONTAKTNI_OSOBA,
            default => throw new \InvalidArgumentException("Neplatný stav likvidace: $value")
        };
    }

    /**
     * Vrátí pole vhodné pro použití v ChoiceType formuláře
     * Klíče jsou popisky (labels) a hodnoty jsou hodnoty enumu
     * 
     * @return array<string, string>
     */
    public static function getChoices(): array
    {
        $choices = [];
        foreach (self::cases() as $case) {
            $choices[$case->value] = $case->value;
        }
        return $choices;
    }

    /**
     * Vrátí pole vhodné pro použití v ChoiceType formuláře bez možnosti ROLE_POJISTENY
     * Klíče jsou popisky (labels) a hodnoty jsou hodnoty enumu
     * 
     * @return array<string, string>
     */
    public static function getChoicesWithoutPojisteny(): array
    {
        $choices = [];
        foreach (self::cases() as $case) {
            if ($case !== self::ROLE_POJISTENY) {
                $choices[$case->value] = $case->value;
            }
        }
        return $choices;
    }

}
