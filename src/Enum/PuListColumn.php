<?php
namespace App\Enum;

enum PuListColumn: string
{
    case Zadavatel = 'zadavatel';
    case CisloPu = 'cisloPu';
    case CisloSu = 'cisloSu';
    case DatumVzniku = 'datumVzniku';
    case DatumPrijeti = 'datumPrijeti';
    case MistoPu = 'mistoPu';
    case Pojisteny = 'pojisteny';
    case Poskozeny = 'poskozeny';
    case Email = 'email';
    case Telefon = 'telefon';
    case Kategorie = 'kategorie';
    case Likvidator = 'likvidator';
    case Stav = 'stav';

    /** @return string[] */
    public static function values(): array
    {
        return array_map(fn(self $enum) => $enum->value, self::cases());
    }

    public static function fromString(string $value): SELF
    {
        return match ($value) {
            'zadavatel' => SELF::Zadavatel,
            'cisloPu' => SELF::CisloPu,
            'cisloSu' => SELF::CisloSu,
            'datumVzniku' => SELF::DatumVzniku,
            'datumPrijeti' => SELF::DatumPrijeti,
            'mistoPu' => SELF::MistoPu,
            'pojisteny' => SELF::Pojisteny,
            'poskozeny' => SELF::Poskozeny,
            'email' => SELF::Email,
            'telefon' => SELF::Telefon,
            'kategorie' => SELF::Kategorie,
            'likvidator' => SELF::Likvidator,
            'stav' => SELF::Stav,
            default => throw new \InvalidArgumentException("Invalid column: $value")
        };
    }

    /**
     * @param array<string> $values
     * @return array<PuListColumn>
     */
    public static function fromArray(array $values): array
    {
        return array_map(fn($value) => SELF::fromString($value), $values);
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::Zadavatel => 'Zadavatel',
            self::CisloPu => 'Číslo PU',
            self::CisloSu => 'Číslo ŠU',
            self::DatumVzniku => 'Datum vzniku PU',
            self::DatumPrijeti => 'Datum přijetí PU',
            self::MistoPu => 'Místo vzniku PU',
            self::Pojisteny => 'Pojistěný',
            self::Poskozeny => 'Poškozený',
            self::Email => 'E-mail',
            self::Telefon => 'Telefon',
            self::Kategorie => 'Kategorie',
            self::Likvidator => 'Likvidátor',
            self::Stav => 'Stav PU',
        };
    }
}