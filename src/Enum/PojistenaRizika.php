<?php

namespace App\Enum;

enum PojistenaRizika: string
{
    case STAVBA_DOMACNOST = 'stav_dom';
    case STAVBA = 'stavba';
    case DOMACNOST = 'domacnost';


    public function getLabel(): string
    {
        return match ($this) {
            self::STAVBA_DOMACNOST => 'stav.+dom.',
            self::STAVBA => 'stavba',
            self::DOMACNOST => 'domácnost',
        };
    }

    /**
     * @return array<string, string>
     */
    public static function getPojistenaRizikaFormChoices(): array
    {
        $result = [];
        foreach (self::cases() as $case) {
            $result[$case->getLabel()] = $case->value;
        }
        return $result;
    }

    public static function getDetailLabel(string $value):string
    {
        return match ($value) {
            'stav_dom' => 'Stavba + Domácnost',
            'stavba' => 'Stavba',
            'domacnost' => 'Domácnost',
            default => throw new \InvalidArgumentException("Neplatný typ PojistenehoRizika: $value")
        };
    }

}
