<?php

namespace App\Enum;

enum StavLikvidace: string
{
    case PRIJATO = 'prijato';
    case PROBIHA = 'probiha';
    case UZAVRENO = 'uzavreno';
    case PROHLIDKA = 'prohlidka';
    case ROZPOCET = 'rozpocet';
    case POSTUP = 'postup';
    case POZADAVEK = 'pozadavek';

    public function getLabel(): string
    {
        return match ($this) {
            self::PRIJATO => 'Přijato',
            self::PROBIHA => 'Probíhá',
            self::UZAVRENO => 'Ukončeno',
            self::PROHLIDKA => 'Prohlídka',
            self::ROZPOCET => 'Rozpočet',
            self::POSTUP => 'Postup',
            self::POZADAVEK => 'Požadavek',
        };
    }

    public static function fromString(string $value): self
    {
        return match ($value) {
            'prijato' => self::PRIJATO,
            'probiha' => self::PROBIHA,
            'uzavreno' => self::UZAVRENO,
            'prohlidka' => self::PROHLIDKA,
            'rozpocet' => self::ROZPOCET,
            'postup' => self::POSTUP,
            'pozadavek' => self::POZADAVEK,
            default => throw new \InvalidArgumentException("Neplatný stav likvidace: $value")
        };
    }

    /**
     * @return array<string, string>
     */
    public static function getStavyAsArray(): array
    {
        $result = [];
        foreach (self::cases() as $case) {
            $result[$case->getLabel()] = $case->value;
        }
        return $result;
    }

    /**
     * @return array<string, string>
     */
    public static function getStavyAsArrayExceptUzavreno(): array
    {
        $result = [];
        foreach (self::cases() as $case) {
            if ($case !== self::UZAVRENO) {
                $result[$case->getLabel()] = $case->value;
            }
        }
        return $result;
    }
}
