<?php
namespace App\Enum;

enum EventCode: string
{
    case Handover = 'HANDOVER';
    case DocumentUpdate = 'DOCUMENT_UPDATE';
    case ActivityUpdate = 'ACTIVITY_UPDATE';
    case HistoryLogUpdate = 'HISTORY_LOG_UPDATE';

    /** @return string[] */
    public static function values(): array
    {
        return array_map(fn($case) => $case->value, self::cases());
    }

    public static function fromString(?string $eventCode, bool $throw = true): ?SELF
    {
        return match ($eventCode) {
            'HANDOVER' => SELF::Handover,
            'DOCUMENT_UPDATE' => SELF::DocumentUpdate,
            'ACTIVITY_UPDATE' => SELF::ActivityUpdate,
            'HISTORY_LOG_UPDATE' => SELF::HistoryLogUpdate,
            default => !$throw ? null : throw new \InvalidArgumentException("Invalid event code: $eventCode"),
        };
    }
}