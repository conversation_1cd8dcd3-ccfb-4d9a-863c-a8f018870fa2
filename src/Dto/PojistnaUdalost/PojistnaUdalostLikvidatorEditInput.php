<?php

namespace App\Dto\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Entity\User;
use DateTimeInterface;

class PojistnaUdalostLikvidatorEditInput
{
    public ?User $likvidator = null;
    
    public static function createFromPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): self
    {
        $pojistnaUdalostLikvidatorEditInput = new self();
        $pojistnaUdalostLikvidatorEditInput->likvidator = $pojistnaUdalost->getLikvidator();
        
        return $pojistnaUdalostLikvidatorEditInput;
    }
}

