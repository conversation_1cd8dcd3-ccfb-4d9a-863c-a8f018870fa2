<?php

namespace App\Dto\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Entity\Zadavatel;
use App\Enum\StavLikvidace;
use DateTimeImmutable;
use DateTimeInterface;

class PojistnaUdalostEditInput
{
    public ?Zadavatel $zadavatel = null;
    public ?string $cisloPojistnaUdalost = null;
    public ?string $cisloSkodniUdalost = null;
    public  ?string $cisloPojistneSmlouvy = null;

    public ?DateTimeInterface $datumVznikuSkody = null;
    public ?DateTimeImmutable $datumNahlaseniSkody = null;

    public ?string $kategorie = null;

    public ?string $stavLikvidace = StavLikvidace::PRIJATO->value;
    public ?string $mistoPu = '';

    public ?string $popisVzniku = null;
    public ?string $rozsahPoskozeni = null;
    
    public static function createFromPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): self
    {
        $pojistnaUdalostEditInput = new self();
        $pojistnaUdalostEditInput->zadavatel = $pojistnaUdalost->getZadavatel();
        $pojistnaUdalostEditInput->cisloPojistnaUdalost = $pojistnaUdalost->getCisloPojistnaUdalost();
        $pojistnaUdalostEditInput->cisloSkodniUdalost = $pojistnaUdalost->getCisloSkodniUdalost();
        $pojistnaUdalostEditInput->cisloPojistneSmlouvy = $pojistnaUdalost->getCisloPojistneSmlouvy();
        
        
        $pojistnaUdalostEditInput->datumVznikuSkody = $pojistnaUdalost->getDatumVzniku();
        $pojistnaUdalostEditInput->datumNahlaseniSkody =  $pojistnaUdalost->getDatumNahlaseniSkody();

        $pojistnaUdalostEditInput->kategorie = $pojistnaUdalost->getKategorie();

        $pojistnaUdalostEditInput->stavLikvidace = $pojistnaUdalost->getStavLikvidace();
        $pojistnaUdalostEditInput->mistoPu =  $pojistnaUdalost->getMistoPu();

        $pojistnaUdalostEditInput->popisVzniku =  $pojistnaUdalost->getPopisVzniku();
        $pojistnaUdalostEditInput->rozsahPoskozeni =  $pojistnaUdalost->getRozsahPoskozeni();
        
        return $pojistnaUdalostEditInput;
    }
}
