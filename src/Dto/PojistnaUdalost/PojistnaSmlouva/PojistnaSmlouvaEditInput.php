<?php

namespace App\Dto\PojistnaUdalost\PojistnaSmlouva;

use App\Entity\PojistnaUdalost;
use App\Entity\PojistnePodminky;
use App\Entity\Rizika;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class PojistnaSmlouvaEditInput
{
    public ?string $PSNazev = null;
    public ?string $cisloPojistneSmlouvy = null;
    public ?DateTimeImmutable $PSPlatnostOd = null;
    public ?string $PSMistoPojisteni = null;
    public ?string $PSZPojisteni = null;
    public ?int $castkaPojistna = 0;
    public ?int $castkaSpoluucast = 0;
    public ?Rizika $PSZRizika = null;
    public ?int $castkaLimitRiziko = 0;
    public ?string $PSVariantaPojisteni = null;
    public ?PojistnePodminky $PSVPP = null;
    /**
     * 
     * @var array<string>
     */
    public array $PSPojisteno = [];
    public ?string $PSLimitRizikoNa = null;

    /**
     * @var Collection<int, LimitRizikaEditInput>
     */
    public Collection $limityRizikaStavba;

    /**
     * @var Collection<int, LimitRizikaEditInput>
     */
    public Collection $limityRizikaDomacnost;

    public function __construct()
    {
        $this->limityRizikaStavba = new ArrayCollection();
        $this->limityRizikaDomacnost = new ArrayCollection();
    }

    public static function createFromPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): self
    {
        $pojistnaSmlouvaEditInput = new self();
        $pojistnaSmlouvaEditInput->PSNazev = $pojistnaUdalost->getPSNazev();
        $pojistnaSmlouvaEditInput->cisloPojistneSmlouvy = $pojistnaUdalost->getCisloPojistneSmlouvy();
        $pojistnaSmlouvaEditInput->PSPlatnostOd = $pojistnaUdalost->getPSPlatnostOd();
        $pojistnaSmlouvaEditInput->PSMistoPojisteni = $pojistnaUdalost->getPSMistoPojisteni();
        $pojistnaSmlouvaEditInput->PSZPojisteni = $pojistnaUdalost->getPSZPojisteni();
        $pojistnaSmlouvaEditInput->castkaPojistna = $pojistnaUdalost->getCastkaPojistna();
        $pojistnaSmlouvaEditInput->castkaSpoluucast = $pojistnaUdalost->getCastkaSpoluucast();
        $pojistnaSmlouvaEditInput->PSZRizika = $pojistnaUdalost->getPSZRizika();
        $pojistnaSmlouvaEditInput->castkaLimitRiziko = $pojistnaUdalost->getCastkaLimitRiziko();
        $pojistnaSmlouvaEditInput->PSVariantaPojisteni = $pojistnaUdalost->getPSVariantaPojisteni();
        $pojistnaSmlouvaEditInput->PSVPP = $pojistnaUdalost->getPSVPP();
        $pojistnaSmlouvaEditInput->PSLimitRizikoNa = $pojistnaUdalost->getPSLimitRizikoNa();
        $pojistnaSmlouvaEditInput->PSPojisteno =  [$pojistnaUdalost->getPSPojisteno()];

        // Přidání limitů rizik rozdělených podle pojisteneRiziko
        foreach ($pojistnaUdalost->getLimityRizika() as $limitRizika) {
            $limitRizikaInput = LimitRizikaEditInput::createFromLimitRizika($limitRizika);
            
            if ($limitRizika->getPojisteneRiziko() === 'stavba') {
                $pojistnaSmlouvaEditInput->limityRizikaStavba->add($limitRizikaInput);
            } elseif ($limitRizika->getPojisteneRiziko() === 'domacnost') {
                $pojistnaSmlouvaEditInput->limityRizikaDomacnost->add($limitRizikaInput);
            }
        }

        return $pojistnaSmlouvaEditInput;
    }
}
