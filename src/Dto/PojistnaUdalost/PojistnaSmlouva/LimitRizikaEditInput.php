<?php

namespace App\Dto\PojistnaUdalost\PojistnaSmlouva;

use App\Entity\LimitRizika;
use App\Entity\Rizika;

class LimitRizikaEditInput
{
    public ?int $id = null;
    public ?string $pojisteneRiziko = null;
    public ?Rizika $zRizika = null;
    public ?int $castkaLimitRizika = 0;
    public ?string $limitRizikaNa = null;

    public static function createFromLimitRizika(LimitRizika $limitRizika): self
    {
        $input = new self();
        $input->id = $limitRizika->getId();
        $input->pojisteneRiziko = $limitRizika->getPojisteneRiziko();
        $input->zRizika = $limitRizika->getZRizika();
        $input->castkaLimitRizika = $limitRizika->getCastkaLimitRizika();
        $input->limitRizikaNa = $limitRizika->getLimitRizikaNa();
        
        return $input;
    }
}
