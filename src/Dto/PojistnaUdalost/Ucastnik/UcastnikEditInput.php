<?php

namespace App\Dto\PojistnaUdalost\Ucastnik;
use App\Entity\Ucastnik;

class UcastnikEditInput
{
    public string $roleUcastnika;
    public ?string $jmeno;
    public ?string $prijmeni;
    public ?string $adresa;
    public ?string $rodneCislo;
    public ?string $telefon;
    public ?string $email;
    public ?string $cisloUctu;
    public ?string $poznamka;
    public ?string $pojistovna;
    public ?string $cisloSmlouvy;

    public ?string $firma;
    public ?string $ico;
    public ?string $sidlo;

    public ?bool $platceDPH;
    public ?bool $isCompany;
    

    public static function createFromUcastnik(Ucastnik $ucastnik): SELF
    {
        $ucastnikEditInput = new self();
        $ucastnikEditInput->roleUcastnika = $ucastnik->getRoleUcastnika();
        $ucastnikEditInput->jmeno = $ucastnik->getJmeno();
        $ucastnikEditInput->prijmeni = $ucastnik->getPrijmeni();
        $ucastnikEditInput->adresa =  $ucastnik->getAdresa();
        $ucastnikEditInput->rodneCislo = $ucastnik->getRodneCislo();
        $ucastnikEditInput->telefon = $ucastnik->getTelefon();
        $ucastnikEditInput->email = $ucastnik->getEmail();
        $ucastnikEditInput->cisloUctu = $ucastnik->getCisloUctu();
        $ucastnikEditInput->poznamka = $ucastnik->getPoznamka();
        $ucastnikEditInput->pojistovna = $ucastnik->getPojistovna();
        $ucastnikEditInput->cisloSmlouvy = $ucastnik->getCisloSmlouvy();

        $ucastnikEditInput->firma = $ucastnik->getFirma();
        $ucastnikEditInput->ico = $ucastnik->getIco();
        $ucastnikEditInput->sidlo = $ucastnik->getSidlo();

        $ucastnikEditInput->platceDPH = $ucastnik->isPlatceDPH();
        $ucastnikEditInput->isCompany = $ucastnik->isCompany();

        return $ucastnikEditInput;
    }
}
