<?php

namespace App\Dto\PojistnaUdalost\Ucastnik;
use App\Entity\Vozidlo;

class UcastnikVozidloEditInput {
    public string $kodRZ;
    public string $kodVIN;
    public string $vykon;
    public string $obsah;
    public string $druh;
    public string $typ;

    public static function createFromUcastnikVozidlo(Vozidlo $vozidlo): self
    {
        $ucastnikVozidloEditInput = new self();
        $ucastnikVozidloEditInput->kodRZ = $vozidlo->getKodRZ();
        $ucastnikVozidloEditInput->kodVIN = $vozidlo->getKodVIN();
        $ucastnikVozidloEditInput->vykon = $vozidlo->getVykon();
        $ucastnikVozidloEditInput->obsah = $vozidlo->getObsah();
        $ucastnikVozidloEditInput->druh = $vozidlo->getDruh();
        $ucastnikVozidloEditInput->typ = $vozidlo->getTyp();
        return $ucastnikVozidloEditInput;
    }
}
