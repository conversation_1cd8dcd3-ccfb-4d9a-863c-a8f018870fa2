<?php

namespace App\Dto\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\PojistnaUdalostNewInput;
use App\Enum\RoleUcastnika;

class UcastnikNewInput
{
    public string $roleUcastnika;
    public ?string $jmeno;
    public ?string $prijmeni;
    public ?string $adresa;
    public ?string $rodneCislo;
    public ?string $telefon;
    public ?string $email;
    public ?string $cisloUctu;
    public ?string $poznamka;
    public ?string $pojistovna;
    public ?string $cisloSmlouvy;

    public ?string $firma;
    public ?string $ico;
    public ?string $sidlo;
    public ?bool $platceDPH;
    public bool $isCompany;


    public function __construct()
    {
        $this->roleUcastnika = RoleUcastnika::ROLE_POSKOZENY->value;
        $this->jmeno = '';
        $this->prijmeni = '';
        $this->adresa = '';
        $this->rodneCislo = '';
        $this->telefon = '';
        $this->email = '';
        $this->cisloUctu = '';
        $this->poznamka = null;
        $this->pojistovna = '';
        $this->cisloSmlouvy = '';

        $this->firma = null;
        $this->ico = null;
        $this->sidlo = null;

        $this->platceDPH = false;
        $this->isCompany = false;
    }



    public function assignUcastnikDataByRoleForNewPojistnaUdalostForm(
        PojistnaUdalostNewInput $pojistnaUdalostNewInput,
        RoleUcastnika $roleUcastnika
    ): self {
        switch ($roleUcastnika) {
            case RoleUcastnika::ROLE_KONTAKTNI_OSOBA:
                $this->assignKontaktniOsobaData($pojistnaUdalostNewInput);
                break;
            case RoleUcastnika::ROLE_POVERENA_OSOBA:
                $this->assignPoverenaOsobaData($pojistnaUdalostNewInput);
                break;
            case RoleUcastnika::ROLE_POSKOZENY:
                $this->assignPoskozenyData(
                    $pojistnaUdalostNewInput,
                    !$pojistnaUdalostNewInput->poskozeny_different_from_pojisteny
                );
                break;
            case RoleUcastnika::ROLE_POJISTENY:
                $this->assignPojistenyData($pojistnaUdalostNewInput);
                break;
        }


        return $this;
    }

    private function assignPojistenyData(PojistnaUdalostNewInput $pojistnaUdalostNewInput): self
    {
        $this->roleUcastnika =  RoleUcastnika::ROLE_POJISTENY->value;

        if ($pojistnaUdalostNewInput->pojisteny_isCompany) {
            $this->assignFirmaFromPojisteny($pojistnaUdalostNewInput);
            $this->cisloUctu =  $pojistnaUdalostNewInput->pojisteny_cisloUctu;
        } else {
            $this->assignPersonFromPojisteny($pojistnaUdalostNewInput);
            $this->adresa =  $pojistnaUdalostNewInput->pojisteny_adresa;
            $this->rodneCislo =  $pojistnaUdalostNewInput->pojisteny_rodneCislo;
            $this->cisloUctu =  $pojistnaUdalostNewInput->pojisteny_cisloUctu;
        }
        return $this;
    }


    private function assignPoskozenyData(PojistnaUdalostNewInput $pojistnaUdalostNewInput, bool $copyFromPojisteny): self
    {
        $this->roleUcastnika =  RoleUcastnika::ROLE_POSKOZENY->value;

        if ($copyFromPojisteny) {
            // copy from Pojisteny
            if ($pojistnaUdalostNewInput->pojisteny_isCompany) {
                $this->assignFirmaFromPojisteny($pojistnaUdalostNewInput);
                $this->cisloUctu =  $pojistnaUdalostNewInput->pojisteny_cisloUctu;
            } else {
                $this->assignPersonFromPojisteny($pojistnaUdalostNewInput);
                $this->adresa =  $pojistnaUdalostNewInput->pojisteny_adresa;
                $this->rodneCislo =  $pojistnaUdalostNewInput->pojisteny_rodneCislo;
                $this->cisloUctu =  $pojistnaUdalostNewInput->pojisteny_cisloUctu;
            }
            return $this;
        }

        // not from Pojisteny
        if ($pojistnaUdalostNewInput->poskozeny_isCompany) {
            $this->firma =  $pojistnaUdalostNewInput->poskozeny_firma;
            $this->sidlo =  $pojistnaUdalostNewInput->poskozeny_sidlo;
            $this->telefon = $pojistnaUdalostNewInput->poskozeny_telefon ?? '';
            $this->ico = $pojistnaUdalostNewInput->poskozeny_ico;
            $this->email =  $pojistnaUdalostNewInput->poskozeny_email;
            $this->platceDPH =  $pojistnaUdalostNewInput->poskozeny_platceDPH;
            $this->cisloUctu =  $pojistnaUdalostNewInput->poskozeny_cisloUctu;
            $this->isCompany =  true;
        } else {
            $this->jmeno =  $pojistnaUdalostNewInput->poskozeny_jmeno;
            $this->prijmeni =  $pojistnaUdalostNewInput->poskozeny_prijmeni;
            $this->telefon =   $pojistnaUdalostNewInput->poskozeny_telefon ?? '';
            $this->email =  $pojistnaUdalostNewInput->poskozeny_email;
            $this->adresa =  $pojistnaUdalostNewInput->poskozeny_adresa;
            $this->rodneCislo =  $pojistnaUdalostNewInput->poskozeny_rodneCislo;
            $this->cisloUctu =  $pojistnaUdalostNewInput->poskozeny_cisloUctu;
        }
        return $this;
    }



    private function assignPoverenaOsobaData(PojistnaUdalostNewInput $pojistnaUdalostNewInput): self
    {
        $this->roleUcastnika =  RoleUcastnika::ROLE_POVERENA_OSOBA->value;

        // not from Pojisteny
        if ($pojistnaUdalostNewInput->poverena_osoba_isCompany) {
            $this->firma =  $pojistnaUdalostNewInput->poverena_osoba_firma;
            $this->sidlo =  $pojistnaUdalostNewInput->poverena_osoba_sidlo;
            $this->telefon = $pojistnaUdalostNewInput->poverena_osoba_telefon ?? '';
            $this->ico = $pojistnaUdalostNewInput->poverena_osoba_ico;
            $this->email =  $pojistnaUdalostNewInput->poverena_osoba_email;
            $this->platceDPH =  $pojistnaUdalostNewInput->poverena_osoba_platceDPH;
            $this->isCompany =  true;
        } else {
            $this->jmeno =  $pojistnaUdalostNewInput->poverena_osoba_jmeno;
            $this->prijmeni =  $pojistnaUdalostNewInput->poverena_osoba_prijmeni;
            $this->telefon =   $pojistnaUdalostNewInput->poverena_osoba_telefon ?? '';
            $this->email =  $pojistnaUdalostNewInput->poverena_osoba_email;
        }
        return $this;
    }



    private function assignKontaktniOsobaData(PojistnaUdalostNewInput $pojistnaUdalostNewInput): self
    {
        $this->roleUcastnika =  RoleUcastnika::ROLE_KONTAKTNI_OSOBA->value;

        // not from Pojisteny
        if ($pojistnaUdalostNewInput->kontaktni_osoba_isCompany) {
            $this->firma =  $pojistnaUdalostNewInput->kontaktni_osoba_firma;
            $this->sidlo =  $pojistnaUdalostNewInput->kontaktni_osoba_sidlo;
            $this->telefon = $pojistnaUdalostNewInput->kontaktni_osoba_telefon ?? '';
            $this->ico = $pojistnaUdalostNewInput->kontaktni_osoba_ico;
            $this->email =  $pojistnaUdalostNewInput->kontaktni_osoba_email;
            $this->platceDPH =  $pojistnaUdalostNewInput->kontaktni_osoba_platceDPH;
            $this->isCompany =  true;
        } else {
            $this->jmeno =  $pojistnaUdalostNewInput->kontaktni_osoba_jmeno;
            $this->prijmeni =  $pojistnaUdalostNewInput->kontaktni_osoba_prijmeni;
            $this->telefon =   $pojistnaUdalostNewInput->kontaktni_osoba_telefon ?? '';
        }
        return $this;
    }


    private function assignFirmaFromPojisteny(PojistnaUdalostNewInput $pojistnaUdalostNewInput):void
    {
        $this->firma =  $pojistnaUdalostNewInput->pojisteny_firma;
        $this->sidlo =  $pojistnaUdalostNewInput->pojisteny_sidlo;
        $this->telefon = $pojistnaUdalostNewInput->pojisteny_telefon ?? '';
        $this->ico = $pojistnaUdalostNewInput->pojisteny_ico;
        $this->email =  $pojistnaUdalostNewInput->pojisteny_email;
        $this->platceDPH =  $pojistnaUdalostNewInput->pojisteny_platceDPH;
        $this->isCompany =  true;
    }

    private function assignPersonFromPojisteny(PojistnaUdalostNewInput $pojistnaUdalostNewInput):void
    {
        $this->jmeno =  $pojistnaUdalostNewInput->pojisteny_jmeno;
        $this->prijmeni =  $pojistnaUdalostNewInput->pojisteny_prijmeni;
        $this->telefon =   $pojistnaUdalostNewInput->pojisteny_telefon ?? '';
        $this->email =  $pojistnaUdalostNewInput->pojisteny_email;
        $this->platceDPH =  $pojistnaUdalostNewInput->pojisteny_platceDPH;
    }
}
