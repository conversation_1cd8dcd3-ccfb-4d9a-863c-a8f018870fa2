<?php

namespace App\Dto\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Entity\User;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class PojistnaUdalostDalsiUzivateleInput
{
    public ?PojistnaUdalost $pojistnaUdalost = null;
    
    /**
     * @var Collection<int, User>
     */
    public Collection $uzivatele;
    
    public function __construct()
    {
        $this->uzivatele = new ArrayCollection();
    }
    
    public static function createFromPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): self
    {
        $input = new self();
        $input->pojistnaUdalost = $pojistnaUdalost;
        
        // Add existing users to the collection
        foreach ($pojistnaUdalost->getDalsiUzivatele() as $uzivatel) {
            $input->uzivatele->add($uzivatel);
        }
        
        return $input;
    }
}
