<?php

namespace App\Dto\PojistnaUdalost;

use App\Entity\Zadavatel;
use App\Enum\RoleUcastnika;
use App\Enum\StavLikvidace;
use DateTimeImmutable;
use DateTimeInterface;


class PojistnaUdalostNewInput
{
    public ?Zadavatel $zadavatel = null;
    public ?string $cisloPojistnaUdalost = null;
    public ?string $cisloSkodniUdalost = null;
    public  ?string $cisloPojistneSmlouvy = null;

    public ?DateTimeInterface $datumVzniku = null;
    public ?DateTimeImmutable $datumNahlaseniSkody = null;
    public ?string $prohlidkaMisto = null;
    public ?string $kategorie = null;
    public ?string $rozsahPojisteni = null;
    public ?string $poznamka = null;
    public ?string $stavLikvidace = StavLikvidace::PRIJATO->value; 
    public ?string $mistoPu = '';

    public ?string $popisVzniku = null;
    public ?string $rozsahPoskozeni = null;


    public string $pojisteny_roleUcastnika = RoleUcastnika::ROLE_POJISTENY->value;
    public ?string $pojisteny_jmeno = '';
    public ?string $pojisteny_prijmeni = '';
    public ?string $pojisteny_adresa = '';
    public ?string $pojisteny_rodneCislo = '';
    public ?string $pojisteny_telefon = '';
    public ?string $pojisteny_email = '';
    public ?string $pojisteny_cisloUctu = '';

    public ?string $pojisteny_firma = '';
    public ?string $pojisteny_ico = '';
    public ?string $pojisteny_sidlo ='';    
    public bool $pojisteny_platceDPH =false; 

    public bool $pojisteny_isCompany = false;


    public string $poskozeny_roleUcastnika = RoleUcastnika::ROLE_POSKOZENY->value;
    public ?string $poskozeny_jmeno = '';
    public ?string $poskozeny_prijmeni = '';
    public ?string $poskozeny_adresa = '';
    public ?string $poskozeny_rodneCislo = '';
    public ?string $poskozeny_telefon = '';
    public ?string $poskozeny_email = '';
    public ?string $poskozeny_cisloUctu = '';

    public ?string $poskozeny_firma = '';
    public ?string $poskozeny_ico = '';
    public ?string $poskozeny_sidlo ='';    
    public bool $poskozeny_platceDPH =false; 

    public bool $poskozeny_isCompany = false;
    public bool $poskozeny_different_from_pojisteny = false;

    public string $poverena_osoba_roleUcastnika = RoleUcastnika::ROLE_POVERENA_OSOBA->value;
    public ?string $poverena_osoba_jmeno = '';
    public ?string $poverena_osoba_prijmeni = '';
    public ?string $poverena_osoba_telefon = '';
    public ?string $poverena_osoba_email = '';
    public bool $poverena_osoba_different_from_pojisteny = false;
    public bool $poverena_osoba_isCompany = false;

    public ?string $poverena_osoba_firma = '';
    public ?string $poverena_osoba_ico = '';
    public ?string $poverena_osoba_sidlo ='';    
    public bool $poverena_osoba_platceDPH =false; 

    public string $kontaktni_osoba_roleUcastnika = RoleUcastnika::ROLE_KONTAKTNI_OSOBA->value;
    public ?string $kontaktni_osoba_jmeno = '';
    public ?string $kontaktni_osoba_prijmeni = '';
    public ?string $kontaktni_osoba_telefon = '';    
    public ?string $kontaktni_osoba_email = '';  
    public bool $kontaktni_osoba_different_from_pojisteny = false;
    public bool $kontaktni_osoba_isCompany = false;

    public ?string $kontaktni_osoba_firma = '';
    public ?string $kontaktni_osoba_ico = '';
    public ?string $kontaktni_osoba_sidlo ='';    
    public bool $kontaktni_osoba_platceDPH =false;     

}
