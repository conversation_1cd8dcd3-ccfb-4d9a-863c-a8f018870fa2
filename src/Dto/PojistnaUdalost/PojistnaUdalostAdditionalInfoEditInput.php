<?php

namespace App\Dto\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use Symfony\Component\Validator\Constraints as Assert;

class PojistnaUdalostAdditionalInfoEditInput
{
    public ?string $popisVzniku = null;

    public ?string $rozsahPoskozeni = null;

    #[Assert\Choice(choices: [
        PojistnaUdalost::ZPUSOB_OPRAVY_ROZPOCTEM,
        PojistnaUdalost::ZPUSOB_OPRAVY_FAKTURA,
        null
    ], message: 'Neplatný způsob opravy')]
    public ?string $zpusobOpravy = null;

    public ?bool $regres = null;

    public ?string $regresName = null;

    public ?string $regresTel = null;

    public ?string $regresAdresa = null;

    private ?int $castkaRezerva = null;

    /**
     * Getter pro castkaRezerva
     */
    public function getCastkaRezerva(): ?int
    {
        return $this->castkaRezerva;
    }

    /**
     * Setter pro castkaRezerva, kter<PERSON> zpracovává string s tečkami
     * 
     * @param mixed $value 
     * @return PojistnaUdalostAdditionalInfoEditInput 
     */
    public function setCastkaRezerva($value): self
    {
        // Pokud je hodnota null nebo prázdný string, nastavíme null
        if ($value === null || $value === '') {
            $this->castkaRezerva = null;
            return $this;
        }

        // Pokud je hodnota string, odstraníme tečky a převedeme na integer
        if (is_string($value)) {
            $value = str_replace('.', '', $value);
        }

        // Převedení na integer
        $this->castkaRezerva = (int) $value;
        
        return $this;
    }



    public static function createFromPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): self
    {
        $dto = new self();
        $dto->popisVzniku = $pojistnaUdalost->getPopisVzniku();
        $dto->rozsahPoskozeni = $pojistnaUdalost->getRozsahPoskozeni();
        $dto->zpusobOpravy = $pojistnaUdalost->getZpusobOpravy();
        $dto->regres = $pojistnaUdalost->isRegres();
        $dto->regresName = $pojistnaUdalost->getRegresName();
        $dto->regresTel =  $pojistnaUdalost->getRegresTel();
        $dto->regresAdresa = $pojistnaUdalost->getRegresAdresa();
        $dto->castkaRezerva =  $pojistnaUdalost->getCastkaRezerva();

        return $dto;
    }
}
