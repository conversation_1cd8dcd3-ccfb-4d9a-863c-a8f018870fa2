<?php

namespace App\Dto\Zadavatel;

use App\Entity\Zadavatel;

class ZadavatelEditInput
{
    public string $nazevPojistovny;
    public string $kontaktniOsoba;
    public string $kontaktniTelefon;
    public string $kontaktniEmail;

    public static function createFromZadavatel(Zadavatel $zadavatel): self
    {
        $zadavatelEditInput = new self();
        $zadavatelEditInput->nazevPojistovny = $zadavatel->getNazevPojistovny();
        $zadavatelEditInput->kontaktniOsoba = $zadavatel->getKontaktniOsoba();
        $zadavatelEditInput->kontaktniTelefon = $zadavatel->getKontaktniTelefon();
        $zadavatelEditInput->kontaktniEmail = $zadavatel->getKontaktniEmail();

        return $zadavatelEditInput;
    }
}