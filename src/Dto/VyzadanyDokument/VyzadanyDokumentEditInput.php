<?php

namespace App\Dto\VyzadanyDokument;

use App\Entity\VyzadanyDokument;

class VyzadanyDokumentEditInput
{
    public ?string $coVyzadano = null;
    public ?string $stav = null;

    public static function createFromVyzadanyDokument(VyzadanyDokument $vyzadanyDokument): self
    {
        $vyzadanyDokumentEditInput = new self();
        $vyzadanyDokumentEditInput->coVyzadano = $vyzadanyDokument->getCoVyzadano();
        $vyzadanyDokumentEditInput->stav = $vyzadanyDokument->getStav();
        
        return $vyzadanyDokumentEditInput;
    }
}