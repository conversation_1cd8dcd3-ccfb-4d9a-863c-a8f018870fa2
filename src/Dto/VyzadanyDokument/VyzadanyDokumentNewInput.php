<?php

namespace App\Dto\VyzadanyDokument;

use App\Entity\PojistnaUdalost;
use App\Entity\Pozadavek;
use App\Entity\User;

class VyzadanyDokumentNewInput
{
    public ?PojistnaUdalost $pojistnaUdalost = null;
    public ?User $autor = null;
    public ?string $coVyzadano = null;
    public ?string $stav = null;
    /**
     * @var array<int, Pozadavek>
     */
    public array $pozadavky = [];
    public bool $isCustom = false;
}
