<?php

namespace App\Dto\Soubor;

use App\Entity\Slozka;
use App\Entity\User;
use Symfony\Component\Security\Core\User\UserInterface;

class SouborEditInput
{
    public ?string $nazev = null;
    public ?Slozka $slozka = null;
    public ?string $typ = null;
    public ?int $velikost = null;
    public ?string $identifikator = null;
    public ?string $uloziste = null;
    public ?UserInterface $autor = null;
    public ?\DateTimeInterface $datumVytvoreni = null;
}
