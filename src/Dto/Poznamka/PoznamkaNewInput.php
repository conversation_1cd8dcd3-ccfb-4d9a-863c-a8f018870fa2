<?php

namespace App\Dto\Poznamka;

use App\Entity\PojistnaUdalost;
use App\Entity\User;
use Symfony\Component\Validator\Constraints as Assert;

class PoznamkaNewInput
{
    public ?PojistnaUdalost $pojistnaUdalost = null;

    public ?User $autor = null;

    /**
     * @Assert\NotBlank
     */
    public ?string $obsah = null;

    public ?\DateTimeInterface $casVytvoreno = null;

    public ?\DateTimeInterface $casUpraveno = null;

    public ?\DateTimeInterface $casVyreseni = null;

    public ?bool $hlidatSplneni = null;

    public ?\DateTimeInterface $termin = null;

    public ?User $resitel = null;

    public bool $vyreseno = false;

    public ?bool $vytvorenoSystemem = null;

    public bool $withTask = false;

    /**
     * @Assert\NotBlank
     */
    public ?string $headline = null;

    public bool $pinned = false;

    public bool $notifyZadavatel = false;
}
