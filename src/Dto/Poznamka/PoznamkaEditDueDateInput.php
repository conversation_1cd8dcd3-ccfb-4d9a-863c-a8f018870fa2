<?php

namespace App\Dto\Poznamka;

use App\Entity\Poznamka;
use App\Entity\PojistnaUdalost;
use App\Entity\User;
use DateTimeInterface;

class PoznamkaEditDueDateInput
{
    public ?DateTimeInterface $termin = null;

    public static function createFromPoznamka(Poznamka $poznamka): self
    {
        $poznamkaEditInput = new self();
        $poznamkaEditInput->termin = $poznamka->getTermin();
        
        return $poznamkaEditInput;
    }
}
