<?php

namespace App\Dto\Poznamka;

use App\Entity\Poznamka;
use App\Entity\User;
use DateTimeInterface;

class PoznamkaEditInput
{
    public ?string $obsah = null;
    public ?bool $hlidatSplneni = null;
    public ?DateTimeInterface $termin = null;
    public ?User $resitel = null;
    public bool $vyreseno = false;
    public bool $withTask = false;
    public ?string $headline = null;
    public bool $pinned = false;
    public bool $notifyZadavatel = false;

    public static function createFromPoznamka(Poznamka $poznamka): self
    {
        $poznamkaEditInput = new self();
        $poznamkaEditInput->obsah = $poznamka->getObsah();
        $poznamkaEditInput->hlidatSplneni = $poznamka->isHlidatSplneni();
        $poznamkaEditInput->termin = $poznamka->getTermin();
        $poznamkaEditInput->resitel = $poznamka->getResitel();
        $poznamkaEditInput->vyreseno = $poznamka->isVyreseno();
        $poznamkaEditInput->withTask = $poznamka->isTask();
        $poznamkaEditInput->headline = $poznamka->getHeadline();
        $poznamkaEditInput->pinned = $poznamka->isPinned();
        $poznamkaEditInput->notifyZadavatel = $poznamka->isNotifyZadavatel();
        
        return $poznamkaEditInput;
    }
}
