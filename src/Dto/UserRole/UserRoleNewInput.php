<?php

namespace App\Dto\UserRole;

use Symfony\Component\Validator\Constraints as Assert;

class UserRoleNewInput
{
    #[Assert\NotBlank(message: 'Název role je povinný')]
    #[Assert\Length(
        min: 2,
        max: 255,
        minMessage: 'Název role musí mít alespoň {{ limit }} znaky',
        maxMessage: 'Název role může mít maximálně {{ limit }} znaků'
    )]
    #[Assert\Regex(
        pattern: '/^[\p{L}0-9_ ]+$/u',
        message: 'Název role může obsahovat pouze písmena (včetně háčků a čárek), č<PERSON><PERSON>, podtrž<PERSON>tko a mezery'
    )]
    public string $name = '';

    #[Assert\Length(
        max: 255,
        maxMessage: 'Popis role může mít maximálně {{ limit }} znaků'
    )]
    public ?string $description = null;

    #[Assert\NotNull(message: 'Status role je povinný')]
    public bool $active = true;
}
