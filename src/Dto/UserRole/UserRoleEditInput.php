<?php

namespace App\Dto\UserRole;

use App\Entity\UserRole;
use Symfony\Component\Validator\Constraints as Assert;

class UserRoleEditInput
{
    #[Assert\NotBlank(message: 'Název role je povinný')]
    #[Assert\Length(
        min: 2,
        max: 255,
        minMessage: 'Název role musí mít alespoň {{ limit }} znaky',
        maxMessage: 'Název role může mít maximálně {{ limit }} znaků'
    )]
    #[Assert\Regex(
        pattern: '/^[\p{L}0-9_ ]+$/u',
        message: 'Název role může obsahovat pouze písmena (včetně h<PERSON>čků a čárek), č<PERSON>la, podtržítko a mezery'
    )]    
    public string $name = '';

    #[Assert\Length(
        max: 255,
        maxMessage: 'Popis role může mít maximálně {{ limit }} znaků'
    )]
    public ?string $description = null;

    #[Assert\NotNull(message: 'Status role je povinný')]
    public bool $active = true;

    public static function fromEntity(UserRole $userRole): self
    {
        $dto = new self();
        $dto->name = $userRole->getName();
        $dto->description = $userRole->getDescription();
        $dto->active = $userRole->isActive();
        
        return $dto;
    }
}
