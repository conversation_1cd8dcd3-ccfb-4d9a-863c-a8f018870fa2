<?php

namespace App\Dto\User;

use App\Entity\User;

class ProfileEditInput
{

    public string $name;
    public string $surname;
    public string $email;
    public ?string $telephone = null;

    public static function createFromUser(User $user):ProfileEditInput
    {
        $profileEditInput = new self();
        $profileEditInput->name = $user->getName();
        $profileEditInput->surname = $user->getSurname();
        $profileEditInput->email = $user->getEmail();
        $profileEditInput->telephone = $user->getTelephone();
        return $profileEditInput;
    }

    public static function createFromUserEditInput(UserEditInput $userEditInput):ProfileEditInput
    {
        $profileEditInput = new self();
        $profileEditInput->name = $userEditInput->name;
        $profileEditInput->surname = $userEditInput->surname;
        $profileEditInput->email = $userEditInput->email;
        $profileEditInput->telephone = $userEditInput->telephone;
        return $profileEditInput;
    }
}
