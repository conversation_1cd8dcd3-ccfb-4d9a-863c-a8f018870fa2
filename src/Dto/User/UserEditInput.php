<?php

namespace App\Dto\User;

use App\Entity\User;

class UserEditInput
{

    public string $name;
    public string $surname;
    public string $email;
    public string $role;
    public ?string $telephone = null;

    public static function createFromUser(User $user):UserEditInput
    {
        $userEditInput = new self();
        $userEditInput->name = $user->getName();
        $userEditInput->surname = $user->getSurname();
        $userEditInput->email = $user->getEmail();
        $userEditInput->role = $user->getFirstRole();
        $userEditInput->telephone = $user->getTelephone();
        return $userEditInput;
    }
}
