<?php

namespace App\Form\VyzadanyDokument;

use App\Dto\VyzadanyDokument\VyzadanyDokumentEditInput;
use App\Entity\VyzadanyDokument;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class VyzadanyDokumentEditType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('coVyzadano', TextareaType::class, [
                'label' => 'Co vyžádáno',
                'required' => true,
                'attr' => [
                    'rows' => 5,
                ],
            ])
            ->add('stav', ChoiceType::class, [
                'label' => 'Stav',
                'choices' => [
                    'Vyžádáno' => VyzadanyDokument::STAV_VYZADANO,
                    'Dolo<PERSON>eno' => VyzadanyDokument::STAV_DOLOZENO,
                    'Už nežádáno' => VyzadanyDokument::STAV_UZ_NEZADANO,
                ],
                'required' => true,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => VyzadanyDokumentEditInput::class,
        ]);
    }
}