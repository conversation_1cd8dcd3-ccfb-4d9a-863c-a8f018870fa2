<?php

namespace App\Form\VyzadanyDokument;

use App\Dto\VyzadanyDokument\VyzadanyDokumentNewInput;
use App\Entity\Pozadavek;
use App\Entity\VyzadanyDokument;
use App\Repository\PozadavekRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class VyzadanyDokumentNewType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('isCustom', CheckboxType::class, [
                'label' => 'Vlastní požadavek',
                'required' => false,
                'attr' => [
                    'data-vyzadany-dokument--toggle-custom-target' => 'checkbox',
                ],
            ])
            ->add('pozadavky', EntityType::class, [
                'class' => Pozadavek::class,
                'choice_label' => 'typPozadavku',
                'multiple' => true,
                'expanded' => true,
                'required' => false,
                'label' => 'Přednastavené požadavky',
                'query_builder' => function (PozadavekRepository $er) {
                    return $er->createQueryBuilder('p')
                        ->orderBy('p.typPozadavku', 'ASC');
                },
                'attr' => [
                    'data-vyzadany-dokument--toggle-custom-target' => 'pozadavky',
                ],
            ])
            ->add('coVyzadano', TextareaType::class, [
                'label' => 'Co vyžádáno',
                'required' => false,
                'attr' => [
                    'data-vyzadany-dokument--toggle-custom-target' => 'customInput',
                    'rows' => 5,
                ],
            ])
            ->add('stav', ChoiceType::class, [
                'label' => 'Stav',
                'choices' => [
                    'Vyžádáno' => VyzadanyDokument::STAV_VYZADANO,
                    'Doloženo' => VyzadanyDokument::STAV_DOLOZENO,
                    'Už nežádáno' => VyzadanyDokument::STAV_UZ_NEZADANO,
                ],
                'required' => true,
                'data' => VyzadanyDokument::STAV_VYZADANO,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => VyzadanyDokumentNewInput::class,
        ]);
    }
}
