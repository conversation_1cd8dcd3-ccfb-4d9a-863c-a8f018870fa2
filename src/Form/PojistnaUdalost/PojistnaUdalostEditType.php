<?php

namespace App\Form\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Entity\Zadavatel;
use App\Dto\PojistnaUdalost\PojistnaUdalostEditInput;
use App\Enum\StavLikvidace;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;

class PojistnaUdalostEditType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('zadavatel', EntityType::class, [
                'class' => Zadavatel::class,
                'choice_label' => 'nazevPojistovny',
                'choice_value' => 'id',
                'label' => 'Zadavatel'
            ])
            ->add('cisloPojistnaUdalost', TextType::class)
            ->add('cisloSkodniUdalost', TextType::class)
            ->add('cisloPojistneSmlouvy', TextType::class, [
                'required' => false,
            ])            
            ->add('datumVznikuSkody', DateType::class, [
                'widget' => 'single_text',
                'html5' => false,
                'format' => 'dd.MM.yyyy',
                'required' => false,
            ])
            ->add('datumNahlaseniSkody', DateType::class, [
                'widget' => 'single_text',
                // prevents rendering it as type="date", to avoid HTML5 date pickers
                'html5' => false,
                'format' => 'dd.MM.yyyy',
                'required' => false,
            ])            
            ->add('mistoPu', TextType::class, ['required' => false])
            ->add('kategorie', ChoiceType::class, [
                'choices' => [
                    'Auto Havarijní' => PojistnaUdalost::CATEGORY_HAV,
                    'Auto Povinné ručení' => PojistnaUdalost::CATEGORY_POV,
                    'Majetek' => PojistnaUdalost::CATEGORY_MAJ,
                ]
            ])

            ->add('rozsahPoskozeni', TextareaType::class, [
                'required' => false,
            ])
            ->add('popisVzniku', TextareaType::class, [
                'required' => false,
            ])

            ->add('stavLikvidace', ChoiceType::class, [
                'choices' => StavLikvidace::getStavyAsArrayExceptUzavreno(),
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => PojistnaUdalostEditInput::class,
        ]);
    }
}
