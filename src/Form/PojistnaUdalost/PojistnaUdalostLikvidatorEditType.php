<?php

namespace App\Form\PojistnaUdalost;

use App\Entity\User;
use App\Dto\PojistnaUdalost\PojistnaUdalostLikvidatorEditInput;
use App\Repository\UserRepository;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;

class PojistnaUdalostLikvidatorEditType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('likvidator', EntityType::class, [
                'class' => User::class,
                'query_builder' => function (UserRepository $er): QueryBuilder {
                    return $er
                        ->createQueryBuilder('u')
                        ->andWhere('u.roles LIKE :role')
                        ->setParameter('role', '%'.User::ROLE_LIKVIDATOR.'%');
                },
                'choice_label' => function ($user) {
                    return $user->getName() . ' ' . $user->getSurname();
                },
                'choice_value' => 'id',
                'required' => false,
                'label' => 'Likvidátor',
                'placeholder' => 'bez likvidátora'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => PojistnaUdalostLikvidatorEditInput::class,
        ]);
    }
}
