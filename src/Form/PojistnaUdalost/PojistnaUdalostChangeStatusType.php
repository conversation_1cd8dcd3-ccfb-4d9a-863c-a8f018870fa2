<?php

namespace App\Form\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostChangeStatusInput;
use App\Enum\StavLikvidace;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class PojistnaUdalostChangeStatusType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('stavLikvidace', ChoiceType::class, [
                'choices' => StavLikvidace::getStavyAsArrayExceptUzavreno(),
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => PojistnaUdalostChangeStatusInput::class,
        ]);
    }
}
