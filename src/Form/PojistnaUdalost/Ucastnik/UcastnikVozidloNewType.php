<?php

namespace App\Form\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikVozidloNewInput;
use App\Entity\Vozidlo;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UcastnikVozidloNewType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('kodRZ', TextType::class, ['label' => 'kodRZ'])
            ->add('kodVIN', TextType::class, ['label' => 'kodVIN'])
            ->add('vykon', TextType::class, ['label' => 'Vykon'])
            ->add('obsah', TextType::class, ['label' => 'Obsah'])
            ->add('druh', ChoiceType::class, [
                'label' => 'Druh',
                'choices'  => [
                    'Osobní' => Vozidlo::CATEGORY_DRUH_OSOBNI,
                    'Nákladní' => Vozidlo::CATEGORY_DRUH_NAKLADNI,
                    'Přívěs' => Vozidlo::CATEGORY_DRUH_PRIVES,
                ],
            ])
            ->add('typ', TextType::class, ['label' => 'Typ']);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UcastnikVozidloNewInput::class,
        ]);
    }
}
