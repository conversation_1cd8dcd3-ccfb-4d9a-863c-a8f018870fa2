<?php

namespace App\Form\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikEditInput;
use App\Enum\RoleUcastnika;
use App\Validator\RodneCislo;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Regex;

class UcastnikEditType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('roleUcastnika', ChoiceType::class, [
                'choices'  => $this->getRoleChoices($options['is_pojisteny_disabled']),
                'label' => 'Role účastníka',
            ])            

            ->add('jmeno', TextType::class, [
                'label' => 'Jméno',
                'required' => false,
                ])
            ->add('prijmeni', TextType::class, [
                'label' => 'Příjmení',
                'required' => false,
                ])
            ->add('rodneCislo', TextType::class, [
                'label' => 'Rodné číslo',
                'required' => false,
                'constraints' => [
                    new RodneCislo(),
                ]
            ])
            ->add('adresa', TextType::class, [
                'label' => 'Adresa',
                'required' => false,
                ])

            ->add('telefon', TextType::class, [
                'required' => false,
                'constraints' => [
                    new Regex([
                        'pattern' => '/^\+\d{3} \d{3} \d{3} \d{3}$/',
                        'message' => 'Telefonní číslo musí být ve formátu +PPP NNN NNN NNN',
                    ]),
                ],
            ])
            ->add('email', EmailType::class, [
                'label' => 'E-mail',
                'required' => false,
                ])
            ->add('cisloUctu', TextType::class, [
                'label' => 'Číslo účtu',
                 'required' => false,
                 ]
                 )
            ->add('firma', TextType::class, [
                'required' => false,
                'label' => 'Název firmy',
            ])
            ->add('ico', TextType::class, [
                'required' => false,
                'label' => 'IČO',
            ])
            ->add('sidlo', TextType::class, [
                'required' => false,
                'label' => 'Sídlo',
            ])
            ->add('platceDPH', CheckboxType::class, [
                'required' => false,
                'label' => 'Plátce DPH',
            ])
            ->add('isCompany', CheckboxType::class, [
                'required' => false,
                'label' => 'Jedná se o firmu',
            ])
            ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            // Configure your form options here
            'data_class' => UcastnikEditInput::class,
            'is_pojisteny_disabled' => false,
        ]);
    }

    
    /**
     * Vrátí pole choices pro roleUcastnika podle toho, zda je pojištěný zakázán
     * 
     * @param bool $isPojistenyDisabled Zda je pojištěný zakázán
     * @return array<string, string> Pole choices pro roleUcastnika
     */
    private function getRoleChoices(bool $isPojistenyDisabled): array
    {
        return $isPojistenyDisabled 
            ? RoleUcastnika::getChoicesWithoutPojisteny() 
            : RoleUcastnika::getChoices();
    }    
}
