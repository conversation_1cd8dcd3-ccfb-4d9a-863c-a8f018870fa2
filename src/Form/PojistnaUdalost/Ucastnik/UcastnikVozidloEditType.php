<?php

namespace App\Form\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikVozidloEditInput;
use App\Entity\Vozidlo;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UcastnikVozidloEditType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('kodRZ', TextType::class, ['label' => 'Kód RZ'])
            ->add('kodVIN', TextType::class, ['label' => 'Kód VIN'])
            ->add('vykon', TextType::class, ['label' => 'Výkon'])
            ->add('obsah', TextType::class, ['label' => 'Obsah'])
            ->add('druh', ChoiceType::class, [
                'label' => 'Druh',
                'choices'  => [
                    'Osobní' => Vozidlo::CATEGORY_DRUH_OSOBNI,
                    'Nákladní' => Vozidlo::CATEGORY_DRUH_NAKLADNI,
                    'Přívěs' => Vozidlo::CATEGORY_DRUH_PRIVES,
                ],
                // Optionally, add 'expanded' => true, 'multiple' => false if needed
            ])
            ->add('typ', TextType::class, ['label' => 'Typ']);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UcastnikVozidloEditInput::class,
        ]);
    }
}
