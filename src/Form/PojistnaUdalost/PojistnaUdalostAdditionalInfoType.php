<?php

namespace App\Form\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostAdditionalInfoEditInput;
use App\Entity\PojistnaUdalost;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PojistnaUdalostAdditionalInfoType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('popisVzniku', TextareaType::class, [
                'label' => 'Popis vzniku',
                'required' => false,
                'attr' => [
                    'rows' => 5,
                ],
            ])
            ->add('rozsahPoskozeni', TextareaType::class, [
                'label' => 'Rozsah poš<PERSON>',
                'required' => false,
                'attr' => [
                    'rows' => 5,
                ],
            ])
            ->add('zpusobOpravy', ChoiceType::class, [
                'label' => 'Způsob opravy',
                'required' => false,
                'placeholder' => 'Vyberte způsob opravy',
                'choices' => [
                    'Rozpočtem' => PojistnaUdalost::ZPUSOB_OPRAVY_ROZPOCTEM,
                    'Faktura' => PojistnaUdalost::ZPUSOB_OPRAVY_FAKTURA,
                ],
            ])
            ->add('castkaRezerva', TextType::class, [
                'label' => 'Rezerva',
                'required' => false,
            ])
            ->add('regres', ChoiceType::class, [
                'label' => 'Regres',
                'required' => true,
                'choices' => [
                    'Ano' => true,
                    'Ne' => false,
                ],
                'attr' => [
                    'data-action' => 'change->pojistnaudalost--additional-info-form#toggleRegresDetails',
                    'data-pojistnaudalost--additional-info-form-target' => 'regresSelect',
                ],
            ])
            ->add('regresName', TextType::class, [
                'label' => 'jméno',
                'required' => false,
            ])
            ->add('regresTel', TextType::class, [
                'label' => 'telefon',
                'required' => false,
            ])
            ->add('regresAdresa', TextType::class, [
                'label' => 'adresa',
                'required' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => PojistnaUdalostAdditionalInfoEditInput::class,
        ]);
    }
}
