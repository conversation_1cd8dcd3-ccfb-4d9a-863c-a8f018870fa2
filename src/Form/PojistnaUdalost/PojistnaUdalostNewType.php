<?php

namespace App\Form\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostNewInput;
use App\Entity\PojistnaUdalost;
use App\Entity\Ucastnik;
use App\Entity\Zadavatel;
use App\Enum\RoleUcastnika;
use App\Enum\StavLikvidace;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;


class PojistnaUdalostNewType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder

            ->add('stavLikvidace', ChoiceType::class, [
                'disabled' => true,
                'choices' => StavLikvidace::getStavyAsArrayExceptUzavreno(),
                'row_attr' => ['class' => 'hidden'],
            ])
            ->add('zadavatel', EntityType::class, [
                'class' => Zadavatel::class,
                'choice_label' => 'nazevPojistovny',
                'choice_value' => 'id',
                'label' => 'Zadavatel'
            ])
            ->add('cisloPojistnaUdalost', TextType::class)
            ->add('cisloSkodniUdalost', TextType::class)
            ->add('cisloPojistneSmlouvy', TextType::class, [
                'required' => false,
            ])
            ->add('datumVzniku', DateType::class, [
                'widget' => 'single_text',
                // prevents rendering it as type="date", to avoid HTML5 date pickers
                'html5' => false,
                'format' => 'dd.MM.yyyy',
                'required' => false,
            ])
            ->add('datumNahlaseniSkody', DateType::class, [
                'widget' => 'single_text',
                // prevents rendering it as type="date", to avoid HTML5 date pickers
                'html5' => false,
                'format' => 'dd.MM.yyyy',
                'required' => false,
            ])
            ->add('mistoPu', TextType::class, [
                'required' => false,
            ])
            ->add('kategorie', ChoiceType::class, [
                'choices' => [
                    'Auto - HAV' => PojistnaUdalost::CATEGORY_HAV,
                    'Auto - POV' => PojistnaUdalost::CATEGORY_POV,
                    'Majetek' => PojistnaUdalost::CATEGORY_MAJ,
                    'Odpovědnost' => PojistnaUdalost::CATEGORY_ODP,
                ]
            ])

            ->add('rozsahPoskozeni', TextareaType::class, [
                'required' => false,
            ])
            ->add('popisVzniku', TextareaType::class, [
                'required' => false,
            ])

            ->add('pojisteny_jmeno', TextType::class, [
                'label' => 'Jméno',
                'required' => false,
            ])
            ->add('pojisteny_prijmeni', TextType::class, [
                'label' => 'Příjmení',
                'required' => false
            ])
            ->add('pojisteny_adresa', TextType::class, [
                'label' => 'Adresa',
                'required' => false
            ])
            ->add('pojisteny_rodneCislo', TextType::class, [
                'label' => 'Rodné číslo',
                'required' => false
            ])
            ->add('pojisteny_telefon', TextType::class, [
                'required' => false
            ])
            ->add('pojisteny_email', EmailType::class, [
                'label' => 'E-mail',
                'required' => false
            ])
            ->add('pojisteny_cisloUctu', TextType::class, [
                'label' => 'Číslo účtu',
                'required' => false
            ])
            ->add('pojisteny_firma', TextType::class, [
                'required' => false,
                'label' => 'Název firmy',
            ])
            ->add('pojisteny_ico', TextType::class, [
                'required' => false,
                'label' => 'IČO',
            ])
            ->add('pojisteny_sidlo', TextType::class, [
                'required' => false,
                'label' => 'Sídlo',
            ])
            ->add('pojisteny_platceDPH', CheckboxType::class, [
                'required' => false,
                'label' => 'Plátce DPH',
            ])
            ->add('pojisteny_isCompany', CheckboxType::class, [
                'required' => false,
                'label' => 'Pojištěný je právnická osoba',
            ])

            ->add('poskozeny_jmeno', TextType::class, [
                'label' => 'Jméno',
                'required' => false,
            ])
            ->add('poskozeny_prijmeni', TextType::class, [
                'label' => 'Příjmení',
                'required' => false
            ])
            ->add('poskozeny_adresa', TextType::class, [
                'label' => 'Adresa',
                'required' => false
            ])
            ->add('poskozeny_rodneCislo', TextType::class, [
                'label' => 'Rodné číslo',
                'required' => false
            ])
            ->add('poskozeny_telefon', TextType::class, [
                'required' => false
            ])
            ->add('poskozeny_email', EmailType::class, [
                'label' => 'E-mail',
                'required' => false
            ])
            ->add('poskozeny_cisloUctu', TextType::class, [
                'label' => 'Číslo účtu',
                'required' => false
            ])
            ->add('poskozeny_firma', TextType::class, [
                'required' => false,
                'label' => 'Název firmy',
            ])
            ->add('poskozeny_ico', TextType::class, [
                'required' => false,
                'label' => 'IČO',
            ])
            ->add('poskozeny_sidlo', TextType::class, [
                'required' => false,
                'label' => 'Sídlo',
            ])
            ->add('poskozeny_platceDPH', CheckboxType::class, [
                'required' => false,
                'label' => 'Plátce DPH',
            ])
            ->add('poskozeny_isCompany', CheckboxType::class, [
                'required' => false,
                'label' => 'Pojištěný je právnická osoba',
            ])    

            ->add('poverena_osoba_jmeno', TextType::class, [
                'label' => 'Jméno',
                'required' => false,
            ])
            ->add('poverena_osoba_prijmeni', TextType::class, [
                'label' => 'Příjmení',
                'required' => false
            ])
            ->add('poverena_osoba_telefon', TextType::class, [
                'required' => false
            ])
            ->add('poverena_osoba_email', EmailType::class, [
                'label' => 'E-mail',
                'required' => false
            ])  


            ->add('poverena_osoba_firma', TextType::class, [
                'required' => false,
                'label' => 'Název firmy',
            ])
            ->add('poverena_osoba_ico', TextType::class, [
                'required' => false,
                'label' => 'IČO',
            ])
            ->add('poverena_osoba_sidlo', TextType::class, [
                'required' => false,
                'label' => 'Sídlo',
            ])
            ->add('poverena_osoba_platceDPH', CheckboxType::class, [
                'required' => false,
                'label' => 'Plátce DPH',
            ])
            ->add('poverena_osoba_isCompany', CheckboxType::class, [
                'required' => false,
                'label' => 'Pověřená osoba je právnická osoba',
            ])   

            ->add('kontaktni_osoba_jmeno', TextType::class, [
                'label' => 'Jméno',
                'required' => false,
            ])
            ->add('kontaktni_osoba_prijmeni', TextType::class, [
                'label' => 'Příjmení',
                'required' => false
            ])
            ->add('kontaktni_osoba_telefon', TextType::class, [
                'required' => false
            ])  

            ->add('kontaktni_osoba_email', EmailType::class, [
                'label' => 'E-mail',
                'required' => false
            ])  

            ->add('kontaktni_osoba_firma', TextType::class, [
                'required' => false,
                'label' => 'Název firmy',
            ])
            ->add('kontaktni_osoba_ico', TextType::class, [
                'required' => false,
                'label' => 'IČO',
            ])
            ->add('kontaktni_osoba_sidlo', TextType::class, [
                'required' => false,
                'label' => 'Sídlo',
            ])
            ->add('kontaktni_osoba_platceDPH', CheckboxType::class, [
                'required' => false,
                'label' => 'Plátce DPH',
            ])
            ->add('kontaktni_osoba_isCompany', CheckboxType::class, [
                'required' => false,
                'label' => 'Kontaktní osoba je právnická osoba',
            ])  



            ->add('poskozeny_different_from_pojisteny', CheckboxType::class, [
                'required' => false,
                'label' => 'Poškozený odlišný od pojištěného',
            ])
            ->add('poverena_osoba_different_from_pojisteny', CheckboxType::class, [
                'required' => false,
                'label' => 'Pověřená osoba odlišná od pojištěného',
            ])
            ->add('kontaktni_osoba_different_from_pojisteny', CheckboxType::class, [
                'required' => false,
                'label' => 'Kontaktní osoba odlišná od pojištěného',
            ])            
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => PojistnaUdalostNewInput::class,
        ]);
    }
}
