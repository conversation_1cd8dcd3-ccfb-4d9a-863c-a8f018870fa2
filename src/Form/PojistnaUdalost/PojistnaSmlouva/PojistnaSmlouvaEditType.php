<?php

namespace App\Form\PojistnaUdalost\PojistnaSmlouva;

use App\Dto\PojistnaUdalost\PojistnaSmlouva\PojistnaSmlouvaEditInput;
use App\Entity\PojistnePodminky;
use App\Entity\Rizika;
use App\Enum\PojistenaRizika;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PojistnaSmlouvaEditType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('PSNazev', TextType::class, [
                'label' => 'Název',
                'required' => false,
            ])
            ->add('cisloPojistneSmlouvy', TextType::class, [
                'label' => 'Číslo',
                'required' => false,
            ])
            ->add('PSPlatnostOd', DateType::class, [
                'label' => 'Platnost od',
                'widget' => 'single_text',
                'html5' => false,
                'format' => 'dd.MM.yyyy',
                'required' => false,
            ])
            ->add('PSMistoPojisteni', TextType::class, [
                'label' => 'Místo pojištění',
                'required' => false,
            ])
            ->add('PSZPojisteni', ChoiceType::class, [
                'label' => 'Z pojištění',
                'required' => false,
                'choices' => [
                    'Stavba' => 'stavba',
                    'Domácnost' => 'domácnost',
                ],
            ])
            ->add('castkaPojistna', TextType::class, [
                'label' => 'Pojistná částka',
                'required' => false,
            ])
            ->add('castkaSpoluucast', TextType::class, [
                'label' => 'Spoluúčast',
                'required' => false,
            ])
            ->add('PSZRizika', EntityType::class, [
                'class' => Rizika::class,
                'choice_label' => 'typPojistneni',
                'label' => 'Z pojištěného rizika',
                'required' => false,
            ])
            ->add('castkaLimitRiziko', TextType::class, [
                'label' => 'Limit rizika',
                'required' => false,
            ])
            ->add('PSLimitRizikoNa', ChoiceType::class, [
                'label' => 'Na',
                'required' => false,
                'choices'  => [
                    'Rok' => 'rok',
                    'PU' => 'PU'
                ],
            ])
            ->add('PSPojisteno', ChoiceType::class, [
                'label' => 'Na',
                'required' => false,
                'choices'  => PojistenaRizika::getPojistenaRizikaFormChoices(),
                'expanded' => true,
                'multiple' => true,
                'attr' => [
                    'class' => 'flex',
                ],
            ])
            ->add('limityRizikaStavba', CollectionType::class, [
                'entry_type' => LimitRizikaType::class,
                'entry_options' => ['label' => false],
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'label' => false,
            ])
            ->add('limityRizikaDomacnost', CollectionType::class, [
                'entry_type' => LimitRizikaType::class,
                'entry_options' => ['label' => false],
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'label' => false,
            ])
            ->add('PSVariantaPojisteni', ChoiceType::class, [
                'label' => 'Varianta pojištění',
                'required' => false,
                'choices' => [
                    'Standard' => 'standard',
                    'Dominant' => 'dominant',
                    'Premiant' => 'premiant',
                    'Premiant plus' => 'premiant plus',
                ],
            ])
            ->add('PSVPP', EntityType::class, [
                'class' => PojistnePodminky::class,
                'choice_label' => 'typVpp',
                'label' => 'VPP',
                'required' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => PojistnaSmlouvaEditInput::class,
        ]);
    }
}
