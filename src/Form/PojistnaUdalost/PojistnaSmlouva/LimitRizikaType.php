<?php

namespace App\Form\PojistnaUdalost\PojistnaSmlouva;

use App\Dto\PojistnaUdalost\PojistnaSmlouva\LimitRizikaEditInput;
use App\Entity\Rizika;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class LimitRizikaType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('id', HiddenType::class, [
                'required' => false,
            ])
            ->add('pojisteneRiziko', HiddenType::class, [
                'required' => true,
            ])
            ->add('zRizika', EntityType::class, [
                'class' => Rizika::class,
                'choice_label' => 'typPojistneni',
                'label' => false,
                'required' => true,
            ])
            ->add('castkaLimitRizika', TextType::class, [
                'label' => false,
                'required' => true,
                'attr' => [
                    'data-pojistnaudalost--pojistna-smlouva-edit-target' => 'castkaLimitRizika',
                ],
            ])
            ->add('limitRizikaNa', ChoiceType::class, [
                'label' => false,
                'required' => true,
                'choices' => [
                    'Rok' => 'rok',
                    'PU' => 'PU'
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => LimitRizikaEditInput::class,
        ]);
    }
}
