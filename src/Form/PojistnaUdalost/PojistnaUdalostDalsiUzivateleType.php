<?php

namespace App\Form\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostDalsiUzivateleInput;
use App\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Doctrine\ORM\EntityRepository;

class PojistnaUdalostDalsiUzivateleType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('uzivatele', EntityType::class, [
                'class' => User::class,
                'choice_label' => function (User $user) {
                    return $user->getFullName() . ' (' . $user->getEmail() . ')';
                },
                'multiple' => true,
                'expanded' => false,
                'required' => false,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('u')
                        ->where('u.active = :active')
                        ->andWhere('u.dynamicRole IS NOT NULL')
                        ->setParameter('active', true)
                        ->orderBy('u.surname', 'ASC')
                        ->addOrderBy('u.name', 'ASC');
                },
                'attr' => [
                    'class' => 'select2-multiple'
                ],
                'label' => 'Uživatelé'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => PojistnaUdalostDalsiUzivateleInput::class,
        ]);
    }
}
