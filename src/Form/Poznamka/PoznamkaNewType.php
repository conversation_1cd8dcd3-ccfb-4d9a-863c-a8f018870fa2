<?php

namespace App\Form\Poznamka;

use App\Dto\Poznamka\PoznamkaNewInput;
use App\Entity\User;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Validator\Constraints\All;
use Symfony\Component\Validator\Constraints\File;

class PoznamkaNewType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('headline', TextType::class, [
                'label' => 'Nadpis'
            ])
            ->add('withTask', CheckboxType::class, [
                'label'    => 'Vytvořit úkol?',
                'required' => false,
                'attr' => [
                    'data-poznamka--toggle-task-target' => 'checkbox',
                ],
            ])
            ->add('pinned', CheckboxType::class, [
                'label'    => 'Připnout',
                'required' => false,
            ])
            ->add('notifyZadavatel', CheckboxType::class, [
                'label'    => 'Upozornit zadavatele',
                'required' => false,
            ])
            ->add('obsah', TextareaType::class, [
                'label' => 'Obsah',
                'required' => false,
            ])
            ->add('resitel', EntityType::class, [
                'class' => User::class,
                'choice_label' => function (User $user) {
                    return $user->getSurname() . ' ' . $user->getName();
                },
                'required' => false,
                'choice_value' => 'id',
                'label' => 'řešitel'
            ])
            ->add('termin', DateType::class, [
                'required' => false,
                'html5' => false,
                'widget' => 'single_text',
                'format' => 'dd.MM.yyyy',
                'attr' => [
                    'class' => 'datepicker', // Add this line
                ],
            ])
            ->add('soubory', FileType::class, [
                'mapped' => false,
                'required' => false,
                'multiple' => true,
                'constraints' => [
                    new All([
                        new File([
                            'mimeTypes' => [
                                'application/pdf',
                                'application/x-pdf',
                                'application/msword',
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'application/vnd.oasis.opendocument.text',
                                'image/jpeg',
                                'image/png',
                                'image/gif',
                            ],
                            'mimeTypesMessage' => 'Nahrávejte pouze PDF, DOC soubory nebo obrázky. Děkujeme.',
                        ])
                    ])
                ]
            ])
        ;

        $builder->addEventListener(FormEvents::POST_SUBMIT, function (FormEvent $event) {
            $data = $event->getData();

            if ($data->termin !== null) {
                $data->hlidatSplneni = true;
            } else {
                $data->hlidatSplneni = false;
            }
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => PoznamkaNewInput::class,
        ]);
    }
}
