<?php

namespace App\Form\Zadavatel;

use App\Dto\Zadavatel\ZadavatelNewInput;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;

class ZadavatelNewType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('nazevPojistovny', TextType::class, [
                'label' => 'Název pojišťovny',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('kontaktniOsoba', TextType::class, [
                'label' => 'Kontaktní osoba',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('kontaktniTelefon', TextType::class, [
                'label' => 'Kontaktní telefon',
                'attr' => ['class' => 'form-control'],
            ])
            ->add('kontaktniEmail', EmailType::class, [
                'label' => 'Kontaktní email',
                'attr' => ['class' => 'form-control'],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ZadavatelNewInput::class,
        ]);
    }
}
