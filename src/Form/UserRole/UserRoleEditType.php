<?php

namespace App\Form\UserRole;

use App\Dto\UserRole\UserRoleEditInput;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserRoleEditType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        // Check if users with this role exist
        $hasUsers = $options['has_users'];
        
        $nameFieldOptions = [
            'label' => 'Název role',
            'required' => true,
        ];
        
        // If users with this role exist, disable the name field
        if ($hasUsers) {
            $nameFieldOptions['disabled'] = true;
            $nameFieldOptions['help'] = 'Název role nelze změnit, protože existují uživatelé s touto rolí.';
            $nameFieldOptions['help_attr'] = [
                'class' => 'mt-1 text-sm text-red-600 dark:text-red-500'
            ];
        }
        
        $builder
            ->add('name', TextType::class, $nameFieldOptions)
            ->add('description', TextareaType::class, [
                'label' => 'Popis role',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Zadejte popis role',
                    'rows' => 4
                ]
            ])
            ->add('active', CheckboxType::class, [
                'label' => 'Aktivní',
                'required' => false
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UserRoleEditInput::class,
            'has_users' => false, // Default value
        ]);
        
        $resolver->setAllowedTypes('has_users', 'bool');
    }
}
