<?php

namespace App\Form\UserRole;

use App\Dto\UserRole\UserRoleNewInput;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserRoleNewType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Název role',
                'required' => true,
                'attr' => [
                    'placeholder' => 'Zadejte název role'
                ],

            ])
            ->add('description', TextareaType::class, [
                'label' => 'Popis role',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Zadejte popis role',
                    'rows' => 4
                ],
            ])
            ->add('active', CheckboxType::class, [
                'label' => 'Aktivní',
                'required' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UserRoleNewInput::class,
        ]);
    }
}
