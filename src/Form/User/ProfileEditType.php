<?php

namespace App\Form\User;

use App\Dto\User\ProfileEditInput;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ProfileEditType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class)
            ->add('surname', TextType::class)
            ->add('email', EmailType::class)
            ->add('telephone', TextType::class, [
                'attr' => [
                    'pattern' => '^(?:(?:\+420\s?)?(?:\d{3}\s?){2}\d{3}|\d{3}\s?\d{3}\s?\d{3}|\d{9}|\+420\d{9})$',
                    'title' => 'Vložte správný formát telefonního čísla'
                ]
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ProfileEditInput::class,
        ]);
    }
}
