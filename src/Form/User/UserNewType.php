<?php

namespace App\Form\User;

use App\Dto\User\UserNewInput;
use App\Service\UserRole\UserRoleFormChoicesLoader;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserNewType extends AbstractType
{


    public function __construct(
        private UserRoleFormChoicesLoader $userRoleFormChoicesLoader


    ) {}

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class)
            ->add('surname', TextType::class)
            ->add('email', EmailType::class)
            ->add('password', RepeatedType::class, [
                'type' => PasswordType::class,
                'invalid_message' => 'Hesla musí být shodná.',
                'options' => ['attr' => ['class' => 'password-field']],
                'required' => true,
                'first_options'  => ['label' => 'Heslo'],
                'second_options' => ['label' => 'Heslo znovu - kontrola']
            ])
            ->add('role', ChoiceType::class, [
                'choices'  => $this->userRoleFormChoicesLoader->loadChoices()
            ])
            ->add('telephone', TextType::class, [
                'attr' => [
                    'pattern' => '^(?:(?:\+420\s?)?(?:\d{3}\s?){2}\d{3}|\d{3}\s?\d{3}\s?\d{3}|\d{9}|\+420\d{9})$',
                    'title' => 'Vložte správný formát telefonního čísla'
                ]
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            // Configure your form options here
            'data_class' => UserNewInput::class,
        ]);
    }
}
