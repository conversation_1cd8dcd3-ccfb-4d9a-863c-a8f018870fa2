<?php

namespace App\Factory\Zadavatel;

use App\Dto\Zadavatel\ZadavatelNewInput;
use App\Entity\Zadavatel;

class ZadavatelNewFactory
{

    public function createZadavatel(ZadavatelNewInput $zadavatelNewInput): Zadavatel
    {
        return new Zadavatel(
            $zadavatelNewInput->nazevPojistovny,
            $zadavatelNewInput->kontaktniOsoba,
            $zadavatelNewInput->kontaktniTelefon,
            $zadavatelNewInput->kontaktniEmail,
        );
    }
}