<?php

namespace App\Factory\User;

use App\Entity\User;
use App\Entity\UserRole;
use App\Repository\UserRoleRepository;
use App\Service\UserRole\UserRoleProvidingService;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class UserFactory
{

    public function __construct(
        private UserPasswordHasherInterface $passwordHasher,
        private UserRoleProvidingService $userRoleProvidingService
    ) {}

    public function createUser(string $name, string $surname, string $plainPassword, string $email, string $role, string $telephone): User
    {
        $user = new User();
        $user->setName($name);
        $user->setSurname($surname);
        $user->setEmail($email);
        $user->setTelephone($telephone);
        $user->setRoles($this->userRoleProvidingService->getHardcodedRole($role));
        $user->assignDynamicRole($this->userRoleProvidingService->getDynamicRole($role));

        $hashedPassword = $this->passwordHasher->hashPassword($user, $plainPassword);
        $user->setPassword($hashedPassword);
        return $user;
    }
}
