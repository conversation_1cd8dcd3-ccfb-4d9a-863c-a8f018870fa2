<?php

namespace App\Factory\Slozka;

use App\Entity\PojistnaUdalost;
use App\Entity\Slozka;
use League\Flysystem\FilesystemOperator;
use InvalidArgumentException;
use RuntimeException;

class SlozkaFactory
{
    public function __construct(
        private FilesystemOperator $slozkyStorage
    ) {}

    /**
     * Vytvoří novou user pod-složku
     */
    public function createUserSubfolder(
        string $nazev,
        Slozka $parentSlozka,
        PojistnaUdalost $pojistnaUdalost,
        string $druhSlozky
    ): Slozka {
        return $this->createSubfolder($nazev, $parentSlozka, $pojistnaUdalost, Slozka::TYP_SLOZKY_USER, $druhSlozky);
    }

    /**
     * Vytvoří novou systémovou pod-složku
     */
    public function createSystemSubfolder(
        string $nazev,
        Slozka $parentSlozka,
        PojistnaUdalost $pojistnaUdalost,
        string $druhSlozky
    ): Slozka {
        return $this->createSubfolder($nazev, $parentSlozka, $pojistnaUdalost, Slozka::TYP_SLOZKY_SYSTEM, $druhSlozky);
    }

    /**
     * Společná metoda pro vytváření pod-složek
     */
    private function createSubfolder(
        string $nazev,
        Slozka $parentSlozka,
        PojistnaUdalost $pojistnaUdalost,
        string $typSlozky,
        string $druhSlozky
    ): Slozka {
        // Validace názvu
        if (empty(trim($nazev))) {
            throw new InvalidArgumentException('Název složky nemůže být prázdný');
        }

        // Vytvoření nové složky
        $newSlozka = new Slozka(
            $pojistnaUdalost,
            trim($nazev),
            $parentSlozka->getUloziste(),
            $typSlozky,
            $parentSlozka
        );

        // Nastavení druhu složky
        $this->setSlozkaType($newSlozka, $druhSlozky);


        // Vytvoření fyzické složky na disku
        $parentPath = $this->getPhysicalPath($parentSlozka);
        $newFolderPath = $parentPath . '/' . $newSlozka->getNazevSlozkyIncludingIdentificator();

        try {
            $this->slozkyStorage->createDirectory($newFolderPath);
        } catch (\Exception $e) {
            throw new RuntimeException('Nepodařilo se vytvořit složku na disku: ' . $e->getMessage());
        }

        // Nastavení cesty
        $newSlozka->setCesta($newFolderPath);

        // Přidání do parent složky
        $parentSlozka->addChild($newSlozka);

        return $newSlozka;
    }

    /**
     * Získá fyzickou cestu ke složce
     */
    private function getPhysicalPath(Slozka $slozka): string
    {
        if ($slozka->getCesta()) {
            return $slozka->getCesta();
        }

        // Fallback - sestaví cestu z hierarchie
        return $slozka->getFullPath();
    }

    /**
     * Nastaví druh složky podle konstanty
     */
    private function setSlozkaType(Slozka $slozka, string $druhSlozky): void
    {
        switch ($druhSlozky) {
            case Slozka::DRUH_SLOZKY_ROOT:
                $slozka->defineAsRootSlozka();
                break;
            case Slozka::DRUH_SLOZKY_KLIENT:
                $slozka->defineAsKlientSlozka();
                break;
            case Slozka::DRUH_SLOZKY_TECHNIK:
                $slozka->defineAsTechnikSlozka();
                break;
            case Slozka::DRUH_SLOZKY_UKOLY:
                $slozka->defineAsUkolySlozka();
                break;
            case Slozka::DRUH_SLOZKY_POJISTOVNA_ZADANI:
                $slozka->defineAsPojistovnaZadaniSlozka();
                break;
            case Slozka::DRUH_SLOZKY_POJISTOVNA_UKONCENI:
                $slozka->defineAsPojistovnaUkonceniSlozka();
                break;
            case Slozka::DRUH_SLOZKY_POJISTOVNA_UKONCENI_ODESLANO:
                $slozka->defineAsPojistovnaUkonceniOdeslanoSlozka();
                break;
            case Slozka::DRUH_SLOZKY_PU_NEURCENO:
                $slozka->defineAsPUNeurcenoSlozka();
                break;
            default:
                throw new InvalidArgumentException("Neznámý druh složky: {$druhSlozky}");
        }
    }

    /**
     * Validuje název složky
     * 
     * @param string $nazev 
     * @return array<string>
     */
    public function validateSlozkaName(string $nazev): array
    {
        $errors = [];

        if (empty(trim($nazev))) {
            $errors[] = 'Název složky nemůže být prázdný';
        }

        if (strlen($nazev) > 100) {
            $errors[] = 'Název složky je příliš dlouhý (max 100 znaků)';
        }

        // Kontrola zakázaných znaků
        if (preg_match('/[\/\\:*?"<>|]/', $nazev)) {
            $errors[] = 'Název složky obsahuje zakázané znaky';
        }

        return $errors;
    }
}
