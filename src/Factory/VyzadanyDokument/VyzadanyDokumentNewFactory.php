<?php

namespace App\Factory\VyzadanyDokument;

use App\Dto\VyzadanyDokument\VyzadanyDokumentNewInput;
use App\Entity\VyzadanyDokument;

class VyzadanyDokumentNewFactory
{
    public function createVyzadanyDokument(VyzadanyDokumentNewInput $vyzadanyDokumentNewInput): VyzadanyDokument
    {
        return new VyzadanyDokument(
            $vyzadanyDokumentNewInput->pojistnaUdalost,
            $vyzadanyDokumentNewInput->autor,
            $vyzadanyDokumentNewInput->coVyzadano,
            $vyzadanyDokumentNewInput->stav
        );
    }
}