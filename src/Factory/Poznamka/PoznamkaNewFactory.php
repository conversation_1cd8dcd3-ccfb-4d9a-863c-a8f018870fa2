<?php

namespace App\Factory\Poznamka;

use App\Dto\Poznamka\PoznamkaNewInput;
use App\Entity\Poznamka;

class PoznamkaNewFactory
{
    public function createPoznamka(PoznamkaNewInput $poznamkaNewInput):Poznamka
    {
        $poznamka = new Poznamka(
            $poznamkaNewInput->pojistnaUdalost,
            $poznamkaNewInput->autor,
            $poznamkaNewInput->obsah,
            $poznamkaNewInput->casVyreseni,
            $poznamkaNewInput->hlidatSplneni,
            $poznamkaNewInput->termin,
            $poznamkaNewInput->resitel,
            $poznamkaNewInput->vyreseno,
            $poznamkaNewInput->vytvorenoSystemem,
            $poznamkaNewInput->headline,
            $poznamkaNewInput->withTask,
            $poznamkaNewInput->pinned,
            $poznamkaNewInput->notifyZadavatel
        );

        return $poznamka;
    }
}
