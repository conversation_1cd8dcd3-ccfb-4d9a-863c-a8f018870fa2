<?php

namespace App\Factory\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostNewInput;
use App\Entity\PojistnaUdalost;

class PojistnaUdalostNewFactory
{

    public function createPojistnaUdalost(PojistnaUdalostNewInput $pojistnaUdalostNewInput):PojistnaUdalost
    {
        $pojistnaUdalost =  new PojistnaUdalost(
            $pojistnaUdalostNewInput->zadavatel,
            $pojistnaUdalostNewInput->cisloPojistnaUdalost,
            $pojistnaUdalostNewInput->cisloSkodniUdalost,
            $pojistnaUdalostNewInput->kategorie,
            $pojistnaUdalostNewInput->stavLikvidace,
            PojistnaUdalost::PROHLIDKA_TYP_NE,
            $pojistnaUdalostNewInput->prohlidkaMisto,
            $pojistnaUdalostNewInput->datumVzniku,
            null,
            $pojistnaUdalostNewInput->rozsahPojisteni,
            $pojistnaUdalostNewInput->poznamka,
            $pojistnaUdalostNewInput->mistoPu,
            $pojistnaUdalostNewInput->datumNahlaseniSkody,
            $pojistnaUdalostNewInput->popisVzniku,
            $pojistnaUdalostNewInput->rozsahPoskozeni,
            $pojistnaUdalostNewInput->cisloPojistneSmlouvy
        );
        return $pojistnaUdalost;

    }
}
