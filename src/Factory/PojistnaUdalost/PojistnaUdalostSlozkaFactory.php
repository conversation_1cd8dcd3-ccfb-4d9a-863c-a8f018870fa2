<?php

namespace App\Factory\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Entity\Slozka;
use League\Flysystem\FilesystemOperator;

class PojistnaUdalostSlozkaFactory
{

    public const SLOZKA_PREFIX_ROOT = 'PU-';

    private FilesystemOperator $storage;

    public function __construct(FilesystemOperator $slozkyStorage)
    {
        $this->storage = $slozkyStorage;
    }


    public function createRootSlozkaForPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): Slozka
    {
        $slozkaRoot = new Slozka(
            $pojistnaUdalost,
            $this->getRootFolderNameBaseForPojistnaUdalost($pojistnaUdalost).'_root',
            Slozka::ULOZISTE_LOCAL,
            Slozka::TYP_SLOZKY_SYSTEM
        );
        $fullName = $slozkaRoot->getNazevSlozkyIncludingIdentificator();
        $this->storage->createDirectory($fullName);
        $slozkaRoot->setCesta($fullName);
        $slozkaRoot->defineTypAsSystemSlozka();
        $slozkaRoot->defineAsRootSlozka();
        return $slozkaRoot;
    }

    public function createPojistovnaZadaniSlozkaForPojistnaUdalost(
        PojistnaUdalost $pojistnaUdalost,
        Slozka $rootSlozka
    ): Slozka {

        $slozkaPZ = new Slozka(
            $pojistnaUdalost,
            'Od pojišťovny',
            Slozka::ULOZISTE_LOCAL,
            Slozka::TYP_SLOZKY_SYSTEM,
            $rootSlozka
        );
        $slozkaRootFullName = $rootSlozka->getNazevSlozkyIncludingIdentificator();
        $slozkaPZName = $slozkaPZ->getNazevSlozkyIncludingIdentificator();
        $fullPath = $slozkaRootFullName . '/' . $slozkaPZName;

        $this->storage->createDirectory($fullPath);
        $slozkaPZ->setCesta($fullPath);
        $slozkaPZ->defineTypAsSystemSlozka();
        $slozkaPZ->defineAsPojistovnaZadaniSlozka();
        return $slozkaPZ;
    }

    public function createPojistovnaUkonceniSlozkaForPojistnaUdalost(
        PojistnaUdalost $pojistnaUdalost,
        Slozka $rootSlozka
    ): Slozka {

        $slozkaPUk = new Slozka(
            $pojistnaUdalost,
            'Pro pojišťovnu',
            Slozka::ULOZISTE_LOCAL,
            Slozka::TYP_SLOZKY_SYSTEM,
            $rootSlozka
        );
        $slozkaRootFullName = $rootSlozka->getNazevSlozkyIncludingIdentificator();
        $slozkaPUkName = $slozkaPUk->getNazevSlozkyIncludingIdentificator();
        $fullPath = $slozkaRootFullName . '/' . $slozkaPUkName;

        $this->storage->createDirectory($fullPath);
        $slozkaPUk->setCesta($fullPath);
        $slozkaPUk->defineTypAsSystemSlozka();
        $slozkaPUk->defineAsPojistovnaUkonceniSlozka();
        return $slozkaPUk;
    }

    public function createKlientSlozkaForPojistnaUdalost(
        PojistnaUdalost $pojistnaUdalost,
        Slozka $rootSlozka
    ): Slozka {

        $slozkaKli = new Slozka(
            $pojistnaUdalost,
            'Od klienta',
            Slozka::ULOZISTE_LOCAL,
            Slozka::TYP_SLOZKY_SYSTEM,
            $rootSlozka
        );
        $slozkaRootFullName = $rootSlozka->getNazevSlozkyIncludingIdentificator();
        $slozkaKliName = $slozkaKli->getNazevSlozkyIncludingIdentificator();
        $fullPath = $slozkaRootFullName . '/' . $slozkaKliName;

        $this->storage->createDirectory($fullPath);
        $slozkaKli->setCesta($fullPath);
        $slozkaKli->defineTypAsSystemSlozka();
        $slozkaKli->defineAsKlientSlozka();
        return $slozkaKli;
    }

    public function createTechnikSlozkaForPojistnaUdalost(
        PojistnaUdalost $pojistnaUdalost,
        Slozka $rootSlozka
    ): Slozka {

        $slozkaTech = new Slozka(
            $pojistnaUdalost,
            'Od technika',
            Slozka::ULOZISTE_LOCAL,
            Slozka::TYP_SLOZKY_SYSTEM,
            $rootSlozka
        );
        $slozkaRootFullName = $rootSlozka->getNazevSlozkyIncludingIdentificator();
        $slozkaTechName = $slozkaTech->getNazevSlozkyIncludingIdentificator();
        $fullPath = $slozkaRootFullName . '/' . $slozkaTechName;

        $this->storage->createDirectory($fullPath);
        $slozkaTech->setCesta($fullPath);
        $slozkaTech->defineTypAsSystemSlozka();
        $slozkaTech->defineAsTechnikSlozka();
        return $slozkaTech;
    }


    public function createUkolySlozkaForPojistnaUdalost(
        PojistnaUdalost $pojistnaUdalost,
        Slozka $rootSlozka
    ): Slozka {

        $slozkaUkoly = new Slozka(
            $pojistnaUdalost,
            'Ukoly',
            Slozka::ULOZISTE_LOCAL,
            Slozka::TYP_SLOZKY_SYSTEM,
            $rootSlozka
        );
        $slozkaRootFullName = $rootSlozka->getNazevSlozkyIncludingIdentificator();
        $slozkaUkolyName = $slozkaUkoly->getNazevSlozkyIncludingIdentificator();
        $fullPath = $slozkaRootFullName . '/' . $slozkaUkolyName;

        $this->storage->createDirectory($fullPath);
        $slozkaUkoly->setCesta($fullPath);
        $slozkaUkoly->defineTypAsSystemSlozka();
        $slozkaUkoly->defineAsUkolySlozka();
        return $slozkaUkoly;
    }


    private function getRootFolderNameBaseForPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): string
    {
        return (self::SLOZKA_PREFIX_ROOT . $pojistnaUdalost->getId());
    } 
}
