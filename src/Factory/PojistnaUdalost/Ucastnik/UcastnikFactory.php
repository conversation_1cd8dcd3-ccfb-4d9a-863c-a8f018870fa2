<?php

namespace App\Factory\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikNewInput;
use App\Entity\PojistnaUdalost;
use App\Entity\Ucastnik;
use App\Enum\RoleUcastnika;

class UcastnikFactory
{
    public function createUcastnik(
        PojistnaUdalost $pojistnaUdalost,
        string|RoleUcastnika $roleUcastnika,
        ?string $jmeno,
        ?string $prijmeni,
        ?string $adresa,
        ?string $rodneCislo,
        ?string $telefon,
        ?string $email,
        ?string $cisloUctu,
        ?string $poznamka,
        ?string $pojistovna,
        ?string $cisloSmlouvy,
        bool   $company,
        ?string $firma = null,
        ?string $ico = null,
        ?string $sidlo = null,
        ?bool   $platceDPH = false        

    ): Ucastnik {
        // Pokud je roleUcastnika instance enumu, převedeme ji na string
        if ($roleUcastnika instanceof RoleUcastnika) {
            $roleUcastnika = $roleUcastnika->value;
        }
        return new Ucastnik(
            $pojistnaUdalost,
            $roleUcastnika,
            $jmeno,
            $prijmeni,
            $adresa,
            $rodneCislo,
            $telefon,
            $email,
            $cisloUctu,
            $poznamka,
            $pojistovna,
            $cisloSmlouvy,
            $firma,
            $ico,
            $sidlo,
            $company,            
            $platceDPH
        );
    }

    public function createUcastnikFromInput(UcastnikNewInput $input, PojistnaUdalost $pojistnaUdalost): Ucastnik
    {
        return $this->createUcastnik(
            $pojistnaUdalost,
            $input->roleUcastnika,
            $input->jmeno,
            $input->prijmeni,
            $input->adresa,
            $input->rodneCislo,
            $input->telefon,
            $input->email,
            $input->cisloUctu,
            $input->poznamka,
            $input->pojistovna,
            $input->cisloSmlouvy,
            $input->isCompany,
            $input->firma,
            $input->ico,
            $input->sidlo,
            $input->platceDPH
        );
    }
}
