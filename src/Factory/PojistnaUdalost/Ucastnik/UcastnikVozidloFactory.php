<?php

namespace App\Factory\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikVozidloNewInput;
use App\Entity\Ucastnik;
use App\Entity\Vozidlo;

class UcastnikVozidloFactory
{
    public function createUcastnikVozidlo(        
        ?Ucastnik $ucastnik,
        ?string $kodRZ,
        ?string $kodVIN,
        ?string $vykon,
        ?string $obsah,   
        ?string $druh, 
        ?string $typ
    ): Vozidlo
    {
        return new Vozidlo(
            $ucastnik,
            $kodRZ,
            $kodVIN,
            $vykon,
            $obsah,
            $druh,
            $typ
        );
    }

    public function createUcastnikVozidloFromInput(UcastnikVozidloNewInput $input, ?Ucastnik $ucastnik): Vozidlo
    {
        return $this->createUcastnikVozidlo(
            $ucastnik,
            $input->kodRZ,
            $input->kodVIN,
            $input->vykon,
            $input->obsah,
            $input->druh,
            $input->typ
        );
    }
}
