<?php

namespace App\Service\Pozadavek;

use App\Dto\Pozadavek\PozadavekEditInput;
use App\Entity\Pozadavek;
use Doctrine\ORM\EntityManagerInterface;

class PozadavekModifierService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function updatePozadavek(Pozadavek $pozadavek, PozadavekEditInput $pozadavekEditInput): Pozadavek
    {
        $pozadavek->modify($pozadavekEditInput);

        $this->entityManager->flush();

        return $pozadavek;
    }
}