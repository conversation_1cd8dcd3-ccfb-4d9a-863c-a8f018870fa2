<?php

namespace App\Service\Rizika;

use App\Dto\Rizika\RizikaEditInput;
use App\Entity\Rizika;
use Doctrine\ORM\EntityManagerInterface;

class RizikaService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function updateRizika(Rizika $rizika, RizikaEditInput $rizikaEditInput): Rizika
    {
        $rizika->modify($rizikaEditInput);

        $this->entityManager->flush();

        return $rizika;
    }
}
