<?php

namespace App\Service\SouborForPoznamka;

use App\Entity\PojistnaUdalost;
use App\Entity\Poznamka;
use App\Entity\SouborForPoznamka;
use App\Service\Slozka\SlozkaGetter;
use Doctrine\ORM\EntityManagerInterface;
use League\Flysystem\FilesystemOperator;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\String\Slugger\SluggerInterface;

class SouborForPoznamkaUploader
{

    public function __construct(
        private SluggerInterface $slugger,
        private EntityManagerInterface $entityManager,
        private SlozkaGetter $slozkaGetter,
        private FilesystemOperator $slozkyStorage
    ) {}


    public function storeSouborForPoznamka(UploadedFile $soubor, Poznamka $poznamka): SouborForPoznamka
    {
        $pojistnaUdalost = $poznamka->getPojistnaUdalost();
        $slozkaUkol =  $this->slozkaGetter->getAllwaysUkolySlozka($pojistnaUdalost);


        $originalFilename = pathinfo($soubor->getClientOriginalName(), PATHINFO_FILENAME);
        $fullOriginalname = pathinfo($soubor->getClientOriginalName(), PATHINFO_BASENAME);
        // this is needed to safely include the file name as part of the URL
        $safeFilename = $this->slugger->slug($originalFilename);
        $newFilename = $safeFilename . '-' . uniqid() . '.' . $soubor->guessExtension();
        $mimeType = $soubor->getMimeType() ?? 'application/octet-stream';

        $rootSlozka = $pojistnaUdalost->getSlozkaRoot();
        $rootPath = $rootSlozka->getNazevSlozkyIncludingIdentificator();


        $slozkaFinalLocation =  $slozkaUkol;
        $slozkaLocationPath = $slozkaFinalLocation->getNazevSlozkyIncludingIdentificator();

        $fullPathFilename = $rootPath . '/' . $slozkaLocationPath . '/' . $newFilename;

        $stream = fopen($soubor->getPathname(), 'r');
        $this->slozkyStorage->writeStream(
            $fullPathFilename,
            $stream
        );
        if (is_resource($stream)) {
            fclose($stream);
        }

        //assign new file
        $newSoubor = new SouborForPoznamka(
            $newFilename,
            $fullOriginalname,
            $mimeType,
            $slozkaFinalLocation,
            $poznamka
        );
        $this->entityManager->persist($newSoubor);
        $this->entityManager->flush();

        return ($newSoubor);
    }
}
