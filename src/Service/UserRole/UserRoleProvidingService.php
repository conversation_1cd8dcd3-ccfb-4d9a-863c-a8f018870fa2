<?php

namespace App\Service\UserRole;

use App\Entity\User;
use App\Entity\UserRole;
use App\Repository\UserRoleRepository;

class UserRoleProvidingService
{

    public function __construct(        
        private UserRoleRepository $userRoleRepository
    ) {}

    /**
     * 
     * @param string $roleCode 
     * @return array<string>
     */
    public function getHardcodedRole(string $roleCode): array
    {

        $hardcodedRole = match ($roleCode) {
            User::ROLE_LIKVIDATOR => User::ROLE_LIKVIDATOR,
            User::ROLE_COM_ADMIN => User::ROLE_COM_ADMIN,
            default => '',
        };

        return [$hardcodedRole];
    }

    public function getDynamicRole(string $roleCode): ?UserRole
    {
        return $this->userRoleRepository->findRoleByCode($roleCode);
    }    
}
