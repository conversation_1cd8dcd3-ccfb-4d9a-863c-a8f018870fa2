<?php

namespace App\Service\UserRole;

use App\Entity\User;
use App\Entity\UserRole;
use App\Repository\UserRoleRepository;

class UserRoleFormChoicesLoader
{

    public function __construct(
        private UserRoleRepository $userRoleRepository
    )
    {    }

    /**
     * 
     * @return array<mixed>
     */
    public function loadChoices():array
    {
        $choices = [
            'Likvidátor' => User::ROLE_LIKVIDATOR,
            'Admin' => User::ROLE_COM_ADMIN
        ];

        $dynamicRoles =  $this->userRoleRepository->getAllActiveUserRoles();
        foreach ($dynamicRoles as $oneRole) {
            /**
             * @var UserRole $oneRole 
             */
            $choices[$oneRole->getName()] = $oneRole->getCode();
        }

        return $choices;
    }

}
