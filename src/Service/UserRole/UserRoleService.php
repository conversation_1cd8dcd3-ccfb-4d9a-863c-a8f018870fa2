<?php

namespace App\Service\UserRole;

use App\Dto\UserRole\UserRoleNewInput;
use App\Entity\UserRole;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\String\Slugger\SluggerInterface;

class UserRoleService
{
    private EntityManagerInterface $entityManager;
    private SluggerInterface $slugger;

    public function __construct(EntityManagerInterface $entityManager, SluggerInterface $slugger)
    {
        $this->entityManager = $entityManager;
        $this->slugger = $slugger;

    }

    public function createUserRole(UserRoleNewInput $input): UserRole
    {
        $userRole = new UserRole(
            $input->name,
            $input->description ?? '',
            $input->active
        );
        $slugged_name =  $this->slugger->slug($input->name,'_');
        $userRole->assignCodeWithRolePrefix($slugged_name);

        $this->entityManager->persist($userRole);
        $this->entityManager->flush();

        return $userRole;
    }
}
