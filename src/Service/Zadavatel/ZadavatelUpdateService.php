<?php

namespace App\Service\Zadavatel;

use App\Dto\Zadavatel\ZadavatelEditInput;
use App\Entity\Zadavatel;
use Doctrine\ORM\EntityManagerInterface;

class ZadavatelUpdateService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function updateZadavatel(Zadavatel $zadavatel, ZadavatelEditInput $zadavatelEditInput): Zadavatel
    {
        $zadavatel->modify($zadavatelEditInput);

        $this->entityManager->flush();

        return $zadavatel;
    }
}
