<?php

namespace App\Service\Quill;

class QuillDeltaParser
{
    /**
     * Extracts plain text from Quill delta operations array
     *
     * @param string $deltaOpsJson JSON string containing Quill operations array
     * @return string Plain text extracted from delta
     */
    public function extractText(string $deltaOpsJson): string
    {
        try {
            $ops = json_decode($deltaOpsJson, true, 512, JSON_THROW_ON_ERROR);
            
            if (!is_array($ops)) {
                return '';
            }

            return $this->parseOps($ops);
        } catch (\JsonException $e) {
            // Handle invalid JSON
            return '';
        }
    }

    /**
     * Parses operations array and concatenates all insert texts
     *
     * @param array<mixed> $ops Array of Quill operations
     * @return string Concatenated text
     */
    private function parseOps(array $ops): string
    {
        $text = '';
        
        foreach ($ops as $op) {
            if (isset($op['insert']) && is_string($op['insert'])) {
                $text .= $op['insert'];
            }
        }

        return trim($text);
    }
}
