<?php

namespace App\Service\Soubor;

use App\Entity\AbstractSlozkaBasedSoubor;
use Exception;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToReadFile;

class SouborReader
{


    public function __construct(
        private FilesystemOperator $slozkyStorage
    ) {}



    /**
     * 
     * @param AbstractSlozkaBasedSoubor $soubor 
     * @return resource 
     * @throws Exception 
     * @throws UnableToReadFile 
     * @throws FilesystemException 
     */
    public function readSouborAsStream(AbstractSlozkaBasedSoubor $soubor)
    {
        $slozka =  $soubor->getSlozka();
        $pojistnaUdalost = $slozka->getPojistnaUdalost();
        $rootSlozka = $pojistnaUdalost->getSlozkaRoot();
        $rootPath = $rootSlozka->getNazevSlozkyIncludingIdentificator();

        $slozkaLocationPath = $slozka->getNazevSlozkyIncludingIdentificator();

        $fullPathFilename = $rootPath . '/' . $slozkaLocationPath . '/' . $soubor->getFilename();

        return $this->slozkyStorage->readStream($fullPathFilename);
    }    
}
