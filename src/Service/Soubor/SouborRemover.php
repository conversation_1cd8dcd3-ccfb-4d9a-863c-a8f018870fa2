<?php

namespace App\Service\Soubor;

use App\Entity\AbstractSlozkaBasedSoubor;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToDeleteFile;
use Psr\Log\LoggerInterface;

class SouborRemover
{


    public function __construct(
        private FilesystemOperator $slozkyStorage,
        private LoggerInterface $logger
    ) {}



    public function deleteFile(AbstractSlozkaBasedSoubor $soubor):void
    {
        $slozka =  $soubor->getSlozka();
        $pojistnaUdalost = $slozka->getPojistnaUdalost();
        $rootSlozka = $pojistnaUdalost->getSlozkaRoot();
        $rootPath = $rootSlozka->getNazevSlozkyIncludingIdentificator();

        $slozkaLocationPath = $slozka->getNazevSlozkyIncludingIdentificator();

        $fullPathFilename = $rootPath . '/' . $slozkaLocationPath . '/' . $soubor->getFilename();

        try {
            $this->slozkyStorage->delete($fullPathFilename);
        } catch (FilesystemException | UnableToDeleteFile $exception) {
            //throw $th;
            $this->logger->alert(sprintf('Problem deleting file:  "%s" : '.$exception->getMessage(), $soubor->getFilename()));
        }
    }
}
