<?php

namespace App\Service\Soubor;

use App\Entity\PojistnaUdalost;
use App\Entity\Slozka;
use App\Entity\Soubor;
use App\Helper\SouborUploadTypeHelper;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use League\Flysystem\FilesystemOperator;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\String\Slugger\SluggerInterface;

class SouborUploader
{

    public function __construct(
        private SluggerInterface $slugger,
        private EntityManagerInterface $entityManager,
        private FilesystemOperator $slozkyStorage
    ) {}


    public function storeSouborDoSlozky(
        UploadedFile $soubor,
        PojistnaUdalost $pojistnaUdalost,
        string $type,
        ?Slozka $targetSlozka = null
    ): ?Soubor {

        $originalFilename = pathinfo($soubor->getClientOriginalName(), PATHINFO_FILENAME);
        $fullOriginalname = pathinfo($soubor->getClientOriginalName(), PATHINFO_BASENAME);
        // this is needed to safely include the file name as part of the URL
        $safeFilename = $this->slugger->slug($originalFilename);
        $newFilename = $safeFilename . '-' . uniqid() . '.' . $soubor->guessExtension();
        $mimeType = $soubor->getMimeType() ?? 'application/octet-stream';

        // Použijeme targetSlozka pokud je zadána, jinak určíme podle typu
        $slozkaFinalLocation = $targetSlozka ?? $this->getSlozkaByType($type, $pojistnaUdalost);
        
        // Použijeme kompletní cestu ze Slozka entity - obsahuje celou hierarchii
        $fullPathFilename = $slozkaFinalLocation->getCesta() . '/' . $newFilename;

        $stream = fopen($soubor->getPathname(), 'r');
        $this->slozkyStorage->writeStream(
            $fullPathFilename,
            $stream
        );
        if (is_resource($stream)) {
            fclose($stream);
        }

        //assign new file
        $newSoubor = new Soubor(
            $newFilename,
            $fullOriginalname,
            $mimeType,
            $slozkaFinalLocation
        );
        $this->entityManager->persist($newSoubor);
        $this->entityManager->flush();

        return ($newSoubor);
    }


    private function getSlozkaByType(string $type, PojistnaUdalost $pojistnaUdalost): Slozka
    {
        $slozka = null;
        switch ($type) {
            case SouborUploadTypeHelper::TYPE_ODPOJISTOVNY:
                $slozka = $pojistnaUdalost->getSlozkaPojistovnaZadani();
                break;
            case SouborUploadTypeHelper::TYPE_ODKLIENTA:
                $slozka = $pojistnaUdalost->getSlozkaKlient();
                break;
            case SouborUploadTypeHelper::TYPE_ODTECHNIKA:
                $slozka = $pojistnaUdalost->getSlozkaTechnik();
                break;
            case SouborUploadTypeHelper::TYPE_PROPOJISTOVNU:
                $slozka = $pojistnaUdalost->getSlozkaPojistovnaUkonceni();
                break;
            case SouborUploadTypeHelper::TYPE_UKOLY:
                $slozka = $pojistnaUdalost->getSlozkaUkoly();
                break;
            default:
                throw new Exception("Neznamy typ uploadu pro soubor, nemohu nalezt slozku", 1);
        }

        return ($slozka);
    }
}
