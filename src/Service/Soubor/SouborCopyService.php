<?php

namespace App\Service\Soubor;

use App\Entity\LikvidacniProtokol;
use App\Entity\Slozka;
use App\Entity\Soubor;
use Doctrine\ORM\EntityManagerInterface;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\FilesystemException;
use RuntimeException;

class SouborCopyService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private FilesystemOperator $slozkyStorage,
        private FilesystemOperator $likvidacniProtokolStorage
    ) {}

    /**
     * Zkopíruje likvidační protokol do cílové složky
     */
    public function copyLikvidacniProtokolToFolder(LikvidacniProtokol $protokol, Slozka $targetSlozka): Soubor
    {
        // Získání původního názvu souboru
        $originalFilename = $protokol->getOriginalFilename();
        
        // Vygenerování unikátního názvu v cílové složce
        $uniqueFilename = $this->generateUniqueFilename($originalFilename, $targetSlozka);
        
        // Fyzické kopírování souboru
        $this->copyFilePhysically($protokol, $targetSlozka, $uniqueFilename);
        
        // Vytvoření nové entity Soubor
        $newSoubor = new Soubor(
            $uniqueFilename,
            $originalFilename,
            $protokol->getMimeType(),
            $targetSlozka
        );
        
        $this->entityManager->persist($newSoubor);
        $this->entityManager->flush();
        
        return $newSoubor;
    }

    /**
     * Fyzické kopírování souboru mezi storage systémy
     */
    private function copyFilePhysically(LikvidacniProtokol $protokol, Slozka $targetSlozka, string $targetFilename): void
    {
        $sourceFilename = $protokol->getFilename();
        $targetPath = $this->buildTargetFilePath($targetFilename, $targetSlozka);

        try {
            // Čtení ze source storage (likvidační protokoly)
            if (!$this->likvidacniProtokolStorage->fileExists($sourceFilename)) {
                throw new RuntimeException(
                    sprintf('Zdrojový soubor %s neexistuje', $sourceFilename)
                );
            }

            $sourceStream = $this->likvidacniProtokolStorage->readStream($sourceFilename);
            
            // Zápis do target storage (složky)
            $this->slozkyStorage->writeStream($targetPath, $sourceStream);
            
            // Uzavření streamu
            if (is_resource($sourceStream)) {
                fclose($sourceStream);
            }
            
        } catch (FilesystemException $e) {
            throw new RuntimeException(
                sprintf('Nepodařilo se zkopírovat soubor %s: %s', $sourceFilename, $e->getMessage())
            );
        }
    }

    /**
     * Vygeneruje unikátní název souboru v cílové složce
     */
    private function generateUniqueFilename(string $originalFilename, Slozka $targetSlozka): string
    {
        $pathInfo = pathinfo($originalFilename);
        $baseName = $pathInfo['filename'];
        $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '';
        
        $counter = 0;
        $newFilename = $originalFilename;
        
        while ($this->fileExistsInSlozka($newFilename, $targetSlozka)) {
            $counter++;
            $newFilename = $baseName . " ({$counter})" . $extension;
        }
        
        return $newFilename;
    }

    /**
     * Zkontroluje, zda soubor s daným názvem už existuje v cílové složce
     */
    private function fileExistsInSlozka(string $filename, Slozka $targetSlozka): bool
    {
        $targetPath = $this->buildTargetFilePath($filename, $targetSlozka);
        
        try {
            return $this->slozkyStorage->fileExists($targetPath);
        } catch (FilesystemException $e) {
            // V případě chyby předpokládáme, že soubor neexistuje
            return false;
        }
    }

    /**
     * Sestaví cestu k cílovému souboru na základě složky
     */
    private function buildTargetFilePath(string $filename, Slozka $targetSlozka): string
    {
        // Použití uložené cesty ze složky
        $slozkaPath = $targetSlozka->getCesta();
        
        if (!$slozkaPath) {
            // Fallback na původní logiku, pokud cesta není nastavená
            $rootSlozka = $targetSlozka->getPojistnaUdalost()->getSlozkaRoot();
            $rootPath = $rootSlozka->getNazevSlozkyIncludingIdentificator();
            $slozkaPath = $rootPath . '/' . $targetSlozka->getNazevSlozkyIncludingIdentificator();
        }
        
        // Kompletní cesta k souboru
        return $slozkaPath . '/' . $filename;
    }
}
