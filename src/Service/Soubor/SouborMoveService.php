<?php

namespace App\Service\Soubor;

use App\Entity\Slozka;
use App\Entity\Soubor;
use Doctrine\ORM\EntityManagerInterface;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\FilesystemException;
use League\Flysystem\UnableToMoveFile;
use RuntimeException;

class SouborMoveService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private FilesystemOperator $slozkyStorage
    ) {}

    /**
     * Přesune všechny soubory do cílové složky
     * 
     * @param Soubor[] $soubory
     */
    public function moveFilesToFolder(array $soubory, Slozka $targetSlozka): void
    {
        foreach ($soubory as $soubor) {
            $this->moveFilePhysically($soubor, $targetSlozka);
        }

        // Uložení změn do databáze
        $this->entityManager->flush();
    }

    /**
     * Přesune jeden soubor do cílové složky (fyzicky i v databázi)
     */
    public function moveFilePhysically(Soubor $soubor, Slozka $targetSlozka): void
    {
        $sourceSlozka = $soubor->getSlozka();
        
        // Sestavení cest
        $sourcePath = $this->buildFilePath($soubor, $sourceSlozka);
        $targetPath = $this->buildFilePath($soubor, $targetSlozka);

        try {
            // Fyzický přesun souboru - použití efektivní move() metody
            if ($this->slozkyStorage->fileExists($sourcePath)) {
                $this->slozkyStorage->move($sourcePath, $targetPath);
            }
        } catch (FilesystemException | UnableToMoveFile $e) {
            throw new RuntimeException(
                sprintf('Nepodařilo se přesunout soubor %s: %s', $soubor->getFilename(), $e->getMessage())
            );
        }

        // Aktualizace databáze - změna složky
        $this->updateSouborSlozka($soubor, $targetSlozka);
    }

    /**
     * Sestaví cestu k souboru na základě složky
     */
    private function buildFilePath(Soubor $soubor, Slozka $slozka): string
    {
        // Použití uložené cesty ze složky
        $slozkaPath = $slozka->getCesta();
        
        if (!$slozkaPath) {
            // Fallback na původní logiku, pokud cesta není nastavená
            $rootSlozka = $slozka->getPojistnaUdalost()->getSlozkaRoot();
            $rootPath = $rootSlozka->getNazevSlozkyIncludingIdentificator();
            $slozkaPath = $rootPath . '/' . $slozka->getNazevSlozkyIncludingIdentificator();
        }
        
        // Kompletní cesta k souboru
        return $slozkaPath . '/' . $soubor->getFilename();
    }

    /**
     * Aktualizuje složku u souboru v databázi
     */
    private function updateSouborSlozka(Soubor $soubor, Slozka $newSlozka): void
    {
        // Použití nové metody pro aktualizaci složky
        $soubor->updateSlozka($newSlozka);
    }
}
