<?php

namespace App\Service\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use Doctrine\ORM\EntityManagerInterface;

class PojistnaUdalostToggleArchiveService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }
    
    public function toggleArchivePojistnaUdalost(PojistnaUdalost $pojistnaUdalost):PojistnaUdalost
    {
        if ($pojistnaUdalost->isArchive()) $pojistnaUdalost->removeFromArchive();
            else $pojistnaUdalost->addToArchive();

        $this->entityManager->flush();
        return $pojistnaUdalost;

    }

}