<?php

namespace App\Service\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Repository\PojistnaUdalostRepository;
use App\Repository\UserRepository;
use App\Repository\ZadavatelRepository;
use DateTime;
use Doctrine\ORM\Query;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\User\UserInterface;

class FilterManager
{
    private PojistnaUdalostRepository $pojistnaUdalostRepository;
    private UserRepository $userRepository;
    private ZadavatelRepository $zadavatelRepository;

    public function __construct(PojistnaUdalostRepository $pojistnaUdalostRepository, UserRepository $userRepository, ZadavatelRepository $zadavatelRepository)
    {
        $this->pojistnaUdalostRepository = $pojistnaUdalostRepository;
        $this->userRepository = $userRepository;
        $this->zadavatelRepository = $zadavatelRepository;
    }

    /**
     * @return array<string>
     */
    public function getCategories(Request $request): array
    {
        $requestCategories = explode(',', $request->query->get('kategorie', ''));
        $categories = [];

        foreach ($requestCategories as $category) {
            if (!in_array($category, PojistnaUdalost::getCategories()))
                continue;

            $categories[] = $category;
        }

        return $categories;
    }

    /**
     * @return array<string|bool>
     */
    public function getDates(Request $request): array
    {
        $dateFrom = $request->query->get('od');
        $dateTo = $request->query->get('do');
        $dateType = $request->query->get('dateField');

        return [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'dateType' => $dateType,
            'set' => $dateFrom || $dateTo,
        ];
    }

    /**
     * @return array{likvidator: string|null, stav: string|null, zadavatel: string|null, technik: string|null}
     */
    public function getMisc(Request $request): array
    {
        return [
            'likvidator' => $request->query->get('likvidator'),
            'stav' => $request->query->get('stav'),
            'zadavatel' => $request->query->get('zadavatel'),
            'technik' => $request->query->get('technik'),
        ];
    }

    /**
     * @return array<string, mixed>|false
     */
    public function getFilterCriteria(Request $request): array|false
    {
        $dates = $this->getDates($request);
        $categories = $this->getCategories($request);
        $misc = $this->getMisc($request);

        $hasDates = !empty($dates['set']);
        $hasCategories = !empty($categories);
        $hasMisc = array_filter($misc, fn($value) => !empty($value));

        if (!$hasDates && !$hasCategories && empty($hasMisc)) {
            return false;
        }

        return [
            'dates'      => $dates,
            'categories' => $categories,
            'misc'       => $misc,
        ];
    }

    public function getQuery(?UserInterface $loggedUser, Request $request, string $sort, string $dir) : Query
    {
        $search = $request->query->get('search');

        if ($search)
            return $this->pojistnaUdalostRepository->getBySearchQuery($search);

        $filters = $this->getFilterCriteria($request);

        if ($filters)
            return $this->pojistnaUdalostRepository->getFilteredQuery($filters, $loggedUser);
        
        return $this->pojistnaUdalostRepository->getAllQuery($loggedUser, $sort, $dir);
    }

    public function getActiveFilter(Request $request): ?string
    {
        $filters = [];

        $dates = $this->getDates($request);
        $categories = $this->getCategories($request);
        $misc = $this->getMisc($request);

        // Dates
        if ($dates['set']) {
            if ($dates['dateFrom'] && $dates['dateTo']) {
                $filters[] = 'Datum od ' . self::convertDateFormat($dates['dateFrom']) . ' do ' . self::convertDateFormat($dates['dateTo']);
            } elseif ($dates['dateFrom']) {
                $filters[] = 'Datum od ' . self::convertDateFormat($dates['dateFrom']);
            } elseif ($dates['dateTo']) {
                $filters[] = 'Datum do ' . self::convertDateFormat($dates['dateTo']);
            }
        }

        // Categories
        if (!empty($categories)) {
            $filters[] = 'Kategorie: ' . implode(', ', $categories);
        }

        // Misc
        if (!empty($misc['likvidator'])) {
            $filters[] = 'Likvidátor: ' . $this->userRepository->findNameById($misc['likvidator']);
        }

        if (!empty($misc['technik'])) {
            $filters[] = 'Technik: ' . $this->userRepository->findNameById($misc['technik']);
        }

        if (!empty($misc['stav'])) {
            $filters[] = 'Stav: ' . $misc['stav'];
        }

        if (!empty($misc['zadavatel'])) {
            $filters[] = 'Zadavatel: ' . $this->zadavatelRepository->findNameById($misc['zadavatel']);
        }

        return !empty($filters) ? implode(', ', $filters) : null;
    }

    private static function convertDateFormat(string $date): string
    {
        $dateTime = DateTime::createFromFormat('Y-m-d', $date);
        return $dateTime->format('d.m.Y');
    }
}