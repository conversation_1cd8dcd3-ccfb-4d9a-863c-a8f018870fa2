<?php

namespace App\Service\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostDalsiUzivateleInput;
use App\Dto\PojistnaUdalost\PojistnaUdalostEditInput;
use App\Entity\PojistnaUdalost;
use App\Entity\User;
use App\Enum\StavLikvidace;
use Doctrine\ORM\EntityManagerInterface;

class PojistnaUdalostService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function updatePojistnaUdalost(PojistnaUdalost $pojistnaUdalost, PojistnaUdalostEditInput $pojistnaUdalostEditInput): PojistnaUdalost
    {
        $pojistnaUdalost->modify($pojistnaUdalostEditInput);
        $this->entityManager->flush();
        return $pojistnaUdalost;
    }
    
    public function changeStatus(PojistnaUdalost $pojistnaUdalost, StavLikvidace $newStatus): PojistnaUdalost
    {
        $pojistnaUdalost->defineStavLikvidaceFromEnum($newStatus);
        $this->entityManager->flush();
        
        return $pojistnaUdalost;
    }
    
    public function updateDalsiUzivatele(PojistnaUdalost $pojistnaUdalost, PojistnaUdalostDalsiUzivateleInput $input): PojistnaUdalost
    {
        // Clear existing users
        $existingUsers = $pojistnaUdalost->getDalsiUzivatele();
        foreach ($existingUsers as $user) {
            $pojistnaUdalost->removeDalsiUzivatele($user);
        }
        
        // Add selected users
        foreach ($input->uzivatele as $user) {
            $pojistnaUdalost->addDalsiUzivatele($user);
        }
        
        $this->entityManager->flush();
        
        return $pojistnaUdalost;
    }
}