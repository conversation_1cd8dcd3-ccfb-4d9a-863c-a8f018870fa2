<?php

namespace App\Service\PojistnaUdalost;

use App\Entity\User;
use App\Repository\NotifikaceRepository;
use App\Repository\PojistnaUdalostArchivRepository;
use App\Repository\PojistnaUdalostRepository;
use Symfony\Component\Security\Core\User\UserInterface;

class PojistnaUdalostDashboardService
{
    private PojistnaUdalostRepository $pojistnaUdalostRepository;
    private PojistnaUdalostArchivRepository $pojistnaUdalostArchivRepository;
    private NotifikaceRepository $notifikaceRepository;

    public function __construct(
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        PojistnaUdalostArchivRepository $pojistnaUdalostArchivRepository,
        NotifikaceRepository $notifikaceRepository
    ) {
        $this->pojistnaUdalostRepository = $pojistnaUdalostRepository;
        $this->pojistnaUdalostArchivRepository = $pojistnaUdalostArchivRepository;
        $this->notifikaceRepository = $notifikaceRepository;
    }

    /**
     * @return array<string, int>
     */
    public function getPojistnaUdalostCounts(UserInterface $user): ?array
    {
        /**
         * @var User $user
         */
        if (!$user->isAdministrator() AND !$user->isLikvidator())
            return null;

        if ($user->isAdministrator())
            return [
                'Dnes přijato' => $this->pojistnaUdalostRepository->getTodayPojistnaUdalostCount(),
                'Nezpracováno' => $this->pojistnaUdalostRepository->getWithoutNotesCount(),
                'Celkem' => $this->pojistnaUdalostRepository->getAllCount(),
                'Přijato' => $this->pojistnaUdalostRepository->getAllPrijatoCount(),
                'Probíhá' => $this->pojistnaUdalostRepository->getAllProbihaCount(),
                'Ukončeno' => $this->pojistnaUdalostRepository->getAllUzavrenoCount(),
                'Archiv' => $this->pojistnaUdalostArchivRepository->getAllCount(),
            ];

        return [
            'Dnes přijato' => $this->pojistnaUdalostRepository->getTodayPojistnaUdalostCount(),
            'Nezpracováno' => $this->pojistnaUdalostRepository->getWithoutNotesByLikvidatorCount($user),
            'Přijato' => $this->pojistnaUdalostRepository->getPrijatoCountByLikvidator($user),
            'Probíhá' => $this->pojistnaUdalostRepository->getProbihaCountByLikvidator($user),
        ];
    }

    /**
     * @return array<string, int>
     */
    public function getMessageCounts(UserInterface $user): ?array
    {
        /**
         * @var User $user
         */
        if (!$user->isAdministrator() AND !$user->isLikvidator())
            return null;

        return [
            'unread' => $this->notifikaceRepository->getUnreadCount($user),
            'all' => $this->notifikaceRepository->getAllCount($user),
        ];
    }
}
