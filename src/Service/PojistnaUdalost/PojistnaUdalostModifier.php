<?php
namespace App\Service\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostLikvidatorEditInput;
use App\Entity\PojistnaUdalost;
use App\Entity\User;
use App\Service\Notifikace\NotifikaceCreator;

class PojistnaUdalostModifier
{
    private NotifikaceCreator $notifikaceCreator;

    public function __construct(
        NotifikaceCreator $notifikaceCreator,
    ) {
        $this->notifikaceCreator = $notifikaceCreator;
    }

    public function modifyLikvidator(PojistnaUdalost $pojistnaUdalost, PojistnaUdalostLikvidatorEditInput $liEditInput): PojistnaUdalost
    {
        $oldLikvidator = $pojistnaUdalost->getLikvidator();
        if ($oldLikvidator === $liEditInput->likvidator)
            return $pojistnaUdalost;

        $pojistnaUdalost->modifyLikvidator($liEditInput);

        if ($oldLikvidator instanceof User)
            $this->notifikaceCreator->createLikvidatorRevocationNotifikace($pojistnaUdalost, $oldLikvidator, true);

        if ($pojistnaUdalost->getLikvidator() instanceof User)
            $this->notifikaceCreator->createLikvidatorAssignmentNotifikace($pojistnaUdalost, true);

        return $pojistnaUdalost;
    }
}