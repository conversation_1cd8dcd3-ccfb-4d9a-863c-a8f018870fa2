<?php

namespace App\Service\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use Doctrine\ORM\EntityManagerInterface;

class PojistnaUdalostRemoveFromArchiveService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function removeFromArchivePojistnaUdalost(PojistnaUdalost $pojistnaUdalost):PojistnaUdalost
    {
        
        $pojistnaUdalost->removeFromArchive();

        $this->entityManager->flush();
        return $pojistnaUdalost;

    }
}