<?php

namespace App\Service\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use Doctrine\ORM\EntityManagerInterface;

class PojistnaUdalostOpenService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function openPojistnaUdalost(PojistnaUdalost $pojistnaUdalost):PojistnaUdalost
    {
        $pojistnaUdalost->open();
        $this->entityManager->flush();
        return $pojistnaUdalost;

    }
}
