<?php

namespace App\Service\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Entity\User;
use InvalidArgumentException;

class PojistnaUdalostCloseValidationService
{
    public function canClosePojistnaUdalost(PojistnaUdalost $pojistnaUdalost, User $user): bool
    {
        // Kontrola role uživatele
        if (!$user->isAdministrator()) {
            return false;
        }

        // Kontrola zda není už uzavřená
        if ($pojistnaUdalost->isUzavreno()) {
            return false;
        }

        // Kontrola zda není archivovaná
        if ($pojistnaUdalost->isArchive()) {
            return false;
        }

        // Kontrola zda jsou všechny úkoly vyřešené
        if (!$this->areAllTasksResolved($pojistnaUdalost)) {
            return false;
        }

        return true;
    }

    /**
     * 
     * @return string[]
     */
    public function getValidationErrors(PojistnaUdalost $pojistnaUdalost, User $user): array
    {
        $errors = [];

        if (!$user->isAdministrator()) {
            $errors[] = 'Nemáte oprávnění k uzavření pojistné události.';
        }

        if ($pojistnaUdalost->isUzavreno()) {
            $errors[] = 'Pojistná událost je již uzavřená.';
        }

        if ($pojistnaUdalost->isArchive()) {
            $errors[] = 'Pojistná událost je archivovaná.';
        }

        if (!$this->areAllTasksResolved($pojistnaUdalost)) {
            $unresolvedTasks = $this->getUnresolvedTasks($pojistnaUdalost);
            $errors[] = sprintf('Existuje %d nevyřešených úkolů.', count($unresolvedTasks));
        }

        return $errors;
    }

    private function areAllTasksResolved(PojistnaUdalost $pojistnaUdalost): bool
    {
        $poznamky = $pojistnaUdalost->getPoznamky();

        foreach ($poznamky as $poznamka) {
            if ($poznamka->isTask() && !$poznamka->isVyreseno()) {
                return false;
            }
        }

        return true;
    }


    /**
     * 
     * @return array<mixed>
     */
    private function getUnresolvedTasks(PojistnaUdalost $pojistnaUdalost): array
    {
        $unresolvedTasks = [];
        $poznamky = $pojistnaUdalost->getPoznamky();

        foreach ($poznamky as $poznamka) {
            if ($poznamka->isTask() && !$poznamka->isVyreseno()) {
                $unresolvedTasks[] = $poznamka;
            }
        }

        return $unresolvedTasks;
    }
}
