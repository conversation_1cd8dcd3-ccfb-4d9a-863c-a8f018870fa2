<?php

namespace App\Service\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Entity\Slozka;
use App\Repository\SouborRepository;
use App\Service\Slozka\SlozkaManagerService;
use App\Service\Soubor\SouborMoveService;
use Doctrine\ORM\EntityManagerInterface;

class PojistnaUdalostCloseService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SlozkaManagerService $slozkaManagerService,
        private SouborRepository $souborRepository,
        private SouborMoveService $souborMoveService
    ) {}

    public function closePojistnaUdalost(PojistnaUdalost $pojistnaUdalost): PojistnaUdalost
    {
        // 1. Odeslání souborů do pojišťovny
        $this->sendFilesToInsuranceCompany($pojistnaUdalost);
        
        // 2. Přesun souborů do složky "odesláno dd.mm.yyyy"
        $this->moveFilesToSentFolder($pojistnaUdalost);
        
        // 3. Uzavření pojistné události
        $pojistnaUdalost->close();
        $this->entityManager->flush();
        
        return $pojistnaUdalost;
    }

    /**
     * Placeholder pro odeslání souborů do pojišťovny přes API
     * TODO: Implementovat skutečné odeslání souborů přes API pojišťovny
     */
    private function sendFilesToInsuranceCompany(PojistnaUdalost $pojistnaUdalost): void
    {
        // Placeholder - zde bude implementováno odeslání souborů přes API pojišťovny
        // Například:
        // - Získání souborů ze složky pro pojišťovnu
        // - Příprava dat pro API call
        // - Odeslání přes HTTP client
        // - Zpracování odpovědi a error handling
    }

    /**
     * Přesune soubory do podsložky "odesláno dd.mm.yyyy"
     */
    private function moveFilesToSentFolder(PojistnaUdalost $pojistnaUdalost): void
    {
        // 1. Získání složky pro ukončení (parent složka)
        $parentSlozka = $pojistnaUdalost->getSlozkaPojistovnaUkonceni();
        
        // 2. Vytvoření názvu podsložky s aktuálním datem
        $currentDate = date('d.m.Y');
        $sentFolderName = "odesláno {$currentDate}";
        
        // 3. Vytvoření systémové podsložky s typem "odesláno"
        $sentSlozka = $this->slozkaManagerService->createSystemSubfolder(
            $sentFolderName,
            $parentSlozka,
            $pojistnaUdalost,
            Slozka::DRUH_SLOZKY_POJISTOVNA_UKONCENI_ODESLANO
        );
        
        // 4. Získání všech souborů ze složky ukončení
        $soubory = $this->souborRepository->findBySlozka($parentSlozka);
        
        // 5. Přesun souborů do nové "odesláno" složky
        if (!empty($soubory)) {
            $this->souborMoveService->moveFilesToFolder($soubory, $sentSlozka);
        }
    }
}
