<?php

namespace App\Service\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostLikvidatorEditInput;
use App\Entity\PojistnaUdalost;
use Doctrine\ORM\EntityManagerInterface;

class PojistnaUdalostLikvidatorService
{
    private EntityManagerInterface $entityManager;
    private PojistnaUdalostModifier $modifier;

    public function __construct(EntityManagerInterface $entityManager, PojistnaUdalostModifier $pojistnaUdalostModifier)
    {
        $this->entityManager = $entityManager;
        $this->modifier = $pojistnaUdalostModifier;
    }

    public function updatePojistnaUdalostLikvidator(PojistnaUdalost $pojistnaUdalost, PojistnaUdalostLikvidatorEditInput $pojistnaUdalostLikvidatorEditInput): PojistnaUdalost
    {
        $this->modifier->modifyLikvidator($pojistnaUdalost, $pojistnaUdalostLikvidatorEditInput);

        $this->entityManager->flush();

        return $pojistnaUdalost;
    }
}
