<?php

namespace App\Service\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikVozidloEditInput;
use App\Entity\Vozidlo;
use Doctrine\ORM\EntityManagerInterface;

class UcastnikVozidloUpdateService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function updateUcastnikVozidlo(Vozidlo $vozidlo, UcastnikVozidloEditInput $ucastnikVozidloEditInput): Vozidlo
    {
        $vozidlo->updateFromInput($ucastnikVozidloEditInput);

        $this->entityManager->flush();

        return $vozidlo;
    }
}
