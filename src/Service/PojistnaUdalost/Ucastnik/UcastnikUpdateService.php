<?php

namespace App\Service\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikEditInput;
use App\Entity\Ucastnik;
use Doctrine\ORM\EntityManagerInterface;

class UcastnikUpdateService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function updateUcastnik(Ucastnik $ucastnik, UcastnikEditInput $ucastnikEditInput): Ucastnik
    {
        $ucastnik->updateFromInput($ucastnikEditInput);

        $this->entityManager->flush();

        return $ucastnik;
    }
}
