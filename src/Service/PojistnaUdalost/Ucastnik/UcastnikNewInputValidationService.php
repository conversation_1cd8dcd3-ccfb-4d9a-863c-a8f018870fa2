<?php

namespace App\Service\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikNewInput;
use App\Enum\RoleUcastnika;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class UcastnikNewInputValidationService
{

    public const VALID_FIELD = 'valid';
    public const ERRORS_FIELD = 'errors';

    /**
     * 
     * @var array<mixed>
     */
    private array $result = [];

    public function __construct(
        private ValidatorInterface $validator
    ) {
        $this->resetResults();
    }


    /**
     * 
     * @param UcastnikNewInput $ucastnikNewInput 
     * @param RoleUcastnika $roleUcastnika 
     * @return array<mixed>
     */
    public function validateUcastnikByRoleForNewPojistnaUdalost(
        UcastnikNewInput $ucastnikNewInput,
        RoleUcastnika $roleUcastnika
    ): array {
        $this->resetResults();
        switch ($roleUcastnika) {
            case RoleUcastnika::ROLE_KONTAKTNI_OSOBA:
                $this->validatePrijmeniOrCompanyNameNotBlank($ucastnikNewInput);
                break;
            case RoleUcastnika::ROLE_POVERENA_OSOBA:
                $this->validatePrijmeniOrCompanyNameNotBlank($ucastnikNewInput);
                break;
            case RoleUcastnika::ROLE_POSKOZENY:
                $this->validatePrijmeniOrCompanyNameNotBlank($ucastnikNewInput);
                break;
            case RoleUcastnika::ROLE_POJISTENY:
                $this->validatePrijmeniOrCompanyNameNotBlank($ucastnikNewInput);
                break;
        }

        return $this->result;
    }


    /**
     * 
     * @param UcastnikNewInput $ucastnikNewInput 
     * @return array<mixed>
     */
    private function validatePrijmeniOrCompanyNameNotBlank(UcastnikNewInput $ucastnikNewInput): array
    {
        $notBlank = new NotBlank();


        if ($ucastnikNewInput->isCompany) {
            $notBlank->message = 'Název firmy musí být vyplněný';
            // use the validator to validate the value
            $errors = $this->validator->validate(
                $ucastnikNewInput->firma,
                $notBlank
            );
        } else {
            $notBlank->message = 'Příjmení musí být vyplněno';
            // use the validator to validate the value
            $errors = $this->validator->validate(
                $ucastnikNewInput->prijmeni,
                $notBlank
            );
        }


        $this->result[self::VALID_FIELD] = true;
        if ($errors->count() > 0) {
            // validationError
            $this->result[self::VALID_FIELD] = false;
            foreach ($errors as $oneError) {
                $this->result[self::ERRORS_FIELD][] = $oneError->getMessage();
            }
        }

        return $this->result;
    }

    private function resetResults():void
    {
        $this->result[self::VALID_FIELD] = true;
        $this->result[self::ERRORS_FIELD] = [];
    }
}
