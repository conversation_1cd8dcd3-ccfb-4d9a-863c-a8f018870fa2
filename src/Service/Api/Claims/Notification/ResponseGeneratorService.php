<?php
namespace App\Service\Api\Claims\Notification;

use App\Enum\Api\ErrorCode;
use App\Enum\Api\Status;
use Symfony\Component\HttpFoundation\JsonResponse;

class ResponseGeneratorService
{
    public function generateInvalidRequestResponse(string $field, string $error): JsonResponse
    {
        return new JsonResponse([
            "status" => Status::Error->value,
            "code" => ErrorCode::InvalidRequest->value,
            "message" => "Invalid request format",
            "details" => [
                "field" => $field,
                "error" => $error,
            ]
        ], 400);
    }

    public function generateUnauthorizedRequestResponse(): JsonResponse
    {
        return new JsonResponse([
            "status" => Status::Error->value,
            "code" => ErrorCode::Unauthorized->value,
            "message" => "Authentication credentials are missing or invalid",
        ], 401);
    }

    public function generateForbiddenRequestResponse(): JsonResponse
    {
        return new JsonResponse([
            "status" => Status::Error->value,
            "code" => ErrorCode::Forbidden->value,
            "message" => "Access denied for this IP address",
        ], 403);
    }

    public function generateNotFoundRequestResponse(): JsonResponse
    {
        return new JsonResponse([
            "status" => Status::Error->value,
            "code" => ErrorCode::NotFound->value,
            "message" => "Requested resource not found",
        ], 404);
    }

    public function generateServiceUnavailableResponse(int $retryAfter = 300): JsonResponse
    {
        return new JsonResponse([
            "status" => Status::Error->value,
            "code" => ErrorCode::ServiceUnavailable->value,
            "message" => "Service is temporarily unavailable",
            "retryAfter" => $retryAfter,
        ], 503);
    }

    public function generateSuccessResponse(int $notificationId): JsonResponse
    {
        return new JsonResponse([
            "status" => Status::Success->value,
            "message" => "Notification successfully processed",
            "notificationId" => $notificationId,
        ], 200);
    }

    public function generateInternalServerErrorResponse(?string $message = null): JsonResponse
    {
        $message = 'Internal server error: ' . ($message ?? 'Internal server error.');
        return new JsonResponse([
            "status" => Status::Error->value,
            "code" => ErrorCode::InternalServerError->value,
            "message" => 'Internal server error.',
        ], 500);
    }
}