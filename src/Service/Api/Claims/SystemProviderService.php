<?php
namespace App\Service\Api\Claims;

use App\Interface\Api\SystemInterface;

class SystemProviderService
{
    private const SYSTEMS = [
        'csobpoj25',
    ];

    public function isSystemValid(string $system): bool
    {
        return in_array($system, SELF::SYSTEMS, true);
    }

    public function provideSystem(string $system): ?SystemInterface
    {
        return match ($system) {
            // TODO: implement system provider
            'csobpoj25' => throw new \Exception('System csobpoj25 not implemented yet'), 
            default => null,
        };
    }
}