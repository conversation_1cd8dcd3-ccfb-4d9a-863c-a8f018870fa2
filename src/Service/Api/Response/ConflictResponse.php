<?php

namespace App\Service\Api\Response;

class ConflictResponse extends BaseResponse
{

    /**
     * @throws \Exception
     */
    public function __construct()
    {
        $this->setStatusCode(409);
        $this->setStatus(ResponseStatus::ERROR);
        $this->setMessage("Conflict - The request could not be completed due to a conflict with the current state of the target resource.");
        $this->setData([]);
    }
}