<?php

namespace App\Service\Api\Response;

use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;

abstract class BaseResponse implements IResponse
{
    private int $statusCode = 200;
    private string $status = "ok";
    private string $message;
    /**
     * @var array<mixed>
     */
    private array $data;

    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * @throws Exception
     */
    public function setStatusCode(int $statusCode): void
    {
        $this->statusCode = match ($statusCode) {
            200 => 200,
            201 => 201,
            400 => 400,
            401 => 401,
            403 => 403,
            404 => 404,
            500 => 500,
            default => throw new Exception("Invalid status code")
        };
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * @throws Exception
     */
    public function setStatus(string $status): void
    {
        $this->status = match ($status) {
            ResponseStatus::OK => ResponseStatus::OK,
            ResponseStatus::ERROR => ResponseStatus::ERROR,
            default => throw new Exception("Invalid status: $status")
        };
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): void
    {
        $this->message = $message;
    }

    /**
     * @param array<mixed> $data
     * @return void
     */
    public function setData(array $data): void
    {
        $this->data = $data;
    }

    /**
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function setDataAtKey(string $key, mixed $value): void
    {
        $this->data[$key] = $value;
    }

    /**
     * @return array<mixed>
     */
    public function getData(): array
    {
        return $this->data;
    }

    public function appendData(mixed $data): void
    {
        $this->data[] = $data;
    }

    public function getDataAsJson(): string
    {
        return json_encode($this->data);
    }

    /**
     * @return array<mixed>
     */
    public function getResponseAsArray(): array
    {
        return [
            "statusCode" => $this->statusCode,
            "status" => $this->status,
            "message" => $this->message,
            "data" => $this->data
        ];
    }

    public function getResponseAsJson(): JsonResponse
    {
        $response = new JsonResponse($this->getResponseAsArray());
        $response->setStatusCode($this->statusCode);
        $response->headers->set('Content-Type', 'application/json');
        return $response;
    }
}