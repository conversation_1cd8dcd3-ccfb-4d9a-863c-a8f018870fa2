<?php

namespace App\Service\Api\Response;

use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;

interface IResponse
{

    public function getStatusCode(): int;

    /**
     * @throws Exception
     */
    public function setStatusCode(int $statusCode): void;

    public function getStatus(): string;

    /**
     * @throws Exception
     */
    public function setStatus(string $status): void;

    public function getMessage(): string;

    public function setMessage(string $message): void;

    /**
     * @return array<mixed>
     */
    public function getData(): array;

    /**
     * @param array<mixed> $data
     * @return void
     */
    public function setData(array $data): void;

    public function setDataAtKey(string $key, mixed $value): void;

    public function appendData(mixed $data): void;

    public function getResponseAsJson(): JsonResponse;
}