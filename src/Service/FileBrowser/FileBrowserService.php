<?php

namespace App\Service\FileBrowser;

use App\Entity\Slozka;
use App\Entity\Soubor;
use App\Repository\SouborRepository;
use App\Repository\SlozkaRepository;
use BadMethodCallException;
use InvalidArgumentException;
use RuntimeException;
use LogicException;
use Doctrine\ORM\Exception\ORMException;

class FileBrowserService
{
    public function __construct(
        private SouborRepository $souborRepository,
        private SlozkaRepository $slozkaRepository,
    ) {}

    /**
     * Získá obsah složky - soubory a podsložky
     * 
     * @return array<mixed> 
     */
    public function getFolderContent(Slozka $slozka, string $sortField = 'createdAt', string $sortOrder = 'desc'): array
    {
        $soubory = $this->souborRepository->findBySlozkaWithSort($slozka, $sortField, $sortOrder);
        $podslozky = $this->getSubfolders($slozka);

        return [
            'soubory' => $soubory,
            'podslozky' => $podslozky,
            'slozka' => $slozka
        ];
    }

    /**
     * Získá breadcrumb cestu ke složce
     * @param Slozka $slozka 
     * @param Slozka|null $rootSlozka - root složka pro breadcrumb (nepovinné)
     * @return array<mixed>
     */
    public function getBreadcrumb(Slozka $slozka, ?Slozka $rootSlozka = null): array
    {
        $breadcrumb = [];
        $currentSlozka = $slozka;
        
        // Pokud není zadána root složka, použijeme aktuální jako jedinou
        if ($rootSlozka === null) {
            $rootSlozka = $slozka;
        }

        // Projdeme hierarchii složek směrem nahoru až k root složce
        while ($currentSlozka !== null) {
            $breadcrumb[] = [
                'nazev' => $this->getFolderDisplayName($currentSlozka),
                'slozka' => [
                    'id' => $currentSlozka->getId()
                ],
                'is_current' => false // Nastavíme později
            ];
            
            // Pokud jsme dosáhli root složky, zastavíme
            if ($currentSlozka->getId() === $rootSlozka->getId()) {
                break;
            }
            
            // Přejdeme na parent složku
            $currentSlozka = $currentSlozka->getParent();
        }

        // Obrátíme pořadí (od root k current)
        $breadcrumb = array_reverse($breadcrumb);
        
        // Označíme poslední jako current nebo přidáme aktuální složku
        $lastItem = $breadcrumb[count($breadcrumb) - 1];
        if ($lastItem['slozka']['id'] !== $slozka->getId()) {
            // Přidáme aktuální složku jako poslední položku
            $breadcrumb[] = [
                'nazev' => $this->getFolderDisplayName($slozka),
                'slozka' => [
                    'id' => $slozka->getId()
                ],
                'is_current' => true
            ];
        } else {
            // Aktuální složka už je v breadcrumb, označíme ji jako current
            $breadcrumb[count($breadcrumb) - 1]['is_current'] = true;
        }

        return $breadcrumb;
    }

    /**
     * Získá zobrazitelný název složky
     */
    public function getFolderDisplayName(Slozka $slozka): string
    {
        switch ($slozka->getDruhSlozky()) {
            case Slozka::DRUH_SLOZKY_ROOT:
                return 'Kořenová složka';
            case Slozka::DRUH_SLOZKY_POJISTOVNA_ZADANI:
                return 'Dokumenty od pojišťovny';
            case Slozka::DRUH_SLOZKY_POJISTOVNA_UKONCENI:
                return 'Dokumenty pro pojišťovnu';
            case Slozka::DRUH_SLOZKY_KLIENT:
                return 'Dokumenty od klienta';
            case Slozka::DRUH_SLOZKY_TECHNIK:
                return 'Dokumenty od technika';
            case Slozka::DRUH_SLOZKY_UKOLY:
                return 'Úkoly';
            default:
                return $slozka->getNazev() ?? 'Neznámá složka';
        }
    }

    /**
     * Získá ikonu pro typ souboru
     */
    public function getFileIcon(Soubor $soubor): string
    {
        $mimeType = $soubor->getMimeType();
        
        if (str_starts_with($mimeType, 'image/')) {
            return 'fa-file-image';
        }
        
        if ($mimeType === 'application/pdf') {
            return 'fa-file-pdf';
        }
        
        if (str_contains($mimeType, 'word') || str_contains($mimeType, 'document')) {
            return 'fa-file-word';
        }
        
        if (str_contains($mimeType, 'excel') || str_contains($mimeType, 'spreadsheet')) {
            return 'fa-file-excel';
        }
        
        if (str_contains($mimeType, 'powerpoint') || str_contains($mimeType, 'presentation')) {
            return 'fa-file-powerpoint';
        }
        
        if (str_contains($mimeType, 'zip') || str_contains($mimeType, 'archive')) {
            return 'fa-file-archive';
        }
        
        return 'fa-file';
    }

    /**
     * Získá CSS třídu pro barvu ikony podle typu souboru
     */
    public function getFileIconColor(Soubor $soubor): string
    {
        $mimeType = $soubor->getMimeType();
        
        if (str_starts_with($mimeType, 'image/')) {
            return 'text-green-600';
        }
        
        if ($mimeType === 'application/pdf') {
            return 'text-red-600';
        }
        
        if (str_contains($mimeType, 'word') || str_contains($mimeType, 'document')) {
            return 'text-blue-600';
        }
        
        if (str_contains($mimeType, 'excel') || str_contains($mimeType, 'spreadsheet')) {
            return 'text-green-700';
        }
        
        if (str_contains($mimeType, 'powerpoint') || str_contains($mimeType, 'presentation')) {
            return 'text-orange-600';
        }
        
        return 'text-gray-600';
    }

    /**
     * Formátuje velikost souboru
     */
    public function formatFileSize(int $bytes): string
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }

    /**
     * Získá podsložky pro danou složku
     * 
     * @param Slozka $slozka 
     * @return array<mixed>
     */
    private function getSubfolders(Slozka $slozka): array
    {
        return $this->slozkaRepository->findChildrenByParent($slozka);
    }
}
