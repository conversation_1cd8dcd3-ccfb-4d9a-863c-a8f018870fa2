<?php

namespace App\Service\User;

use App\Dto\User\ProfileEditInput;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;

class ProfileEditService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function updateUser(User $user, ProfileEditInput $profileEditInput): User
    {
        $user->modifyUserProfile($profileEditInput);        
        $this->entityManager->flush();
        return $user;
    }
}
