<?php

namespace App\Service\User;

use App\Dto\User\ProfileEditInput;
use App\Dto\User\UserEditInput;
use App\Entity\User;
use App\Service\UserRole\UserRoleProvidingService;
use Doctrine\ORM\EntityManagerInterface;

class UserEditService
{


    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserRoleProvidingService $userRoleProvidingService
        )
    {

    }

    public function updateUser(User $user, UserEditInput $userEditInput): User
    {
        $profileEditInput = ProfileEditInput::createFromUserEditInput($userEditInput);
        $user->modifyUserProfile($profileEditInput);
        $user->setRoles($this->userRoleProvidingService->getHardcodedRole($userEditInput->role));
        $user->assignDynamicRole($this->userRoleProvidingService->getDynamicRole($userEditInput->role));


        $this->entityManager->flush();
        return $user;
    }
}
