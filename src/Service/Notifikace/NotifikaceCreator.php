<?php
namespace App\Service\Notifikace;

use App\Entity\Notifikace;
use App\Entity\PojistnaUdalost;
use App\Entity\Poznamka;
use App\Entity\Prohlidka;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Twig\Environment;

class NotifikaceCreator
{
    private Environment $twig;
    private EntityManagerInterface $entityManager;
    private NotifikaceSender $notifikaceSender;

    public function __construct(Environment $twig, EntityManagerInterface $entityManager, NotifikaceSender $notifikaceSender)
    {
        $this->twig = $twig;
        $this->entityManager = $entityManager;
        $this->notifikaceSender = $notifikaceSender;
    }

    public function createLikvidatorAssignmentNotifikace(PojistnaUdalost $pojistnaUdalost, bool $sendMail): Notifikace
    {
        $titulek = "PU {$pojistnaUdalost->getCisloPojistnaUdalost()} - By<PERSON> jste přiřazeni k likvidaci";
        $obsah = $this->twig->render('notifikace/likvidator_assignment_email.html.twig', [
            'pojistnaUdalost' => $pojistnaUdalost,
        ]);

        $notifikace = new Notifikace($titulek, $obsah, Notifikace::TYP_LIKVIDATOR_ASSIGNMENT, $pojistnaUdalost->getLikvidator(), $sendMail);

        $this->entityManager->persist($notifikace);
        $this->entityManager->flush();

        $this->notifikaceSender->send($notifikace);

        return $notifikace;
    }

    public function createLikvidatorRevocationNotifikace(PojistnaUdalost $pojistnaUdalost, ?User $oldLikvidator, bool $sendMail): Notifikace
    {
        $titulek = "PU {$pojistnaUdalost->getCisloPojistnaUdalost()} - Byli jste odebráni z likvidace";
        $obsah = $this->twig->render('notifikace/likvidator_revocation_email.html.twig', [
            'pojistnaUdalost' => $pojistnaUdalost,
        ]);

        $notifikace = new Notifikace($titulek, $obsah, Notifikace::TYP_LIKVIDATOR_REVOCATION, $oldLikvidator, $sendMail);

        $this->entityManager->persist($notifikace);
        $this->entityManager->flush();

        $this->notifikaceSender->send($notifikace);

        return $notifikace;
    }

    public function createProhlidkaCreationNotifikace(Prohlidka $prohlidka, bool $sendMail): Notifikace
    {
        $pojistnaUdalost = $prohlidka->getPojistnaUdalost();
        $technik = $prohlidka->getTechnik();
        
        $titulek = "PU {$pojistnaUdalost->getCisloPojistnaUdalost()} - Požadavek na prohlídku";
        $obsah = $this->twig->render('notifikace/prohlidka_creation_email.html.twig', [
            'pojistnaUdalost' => $pojistnaUdalost,
        ]);

        $notifikace = new Notifikace($titulek, $obsah, Notifikace::TYP_PROHLIDKA_CREATION, $technik, $sendMail);

        $this->entityManager->persist($notifikace);
        $this->entityManager->flush();

        $this->notifikaceSender->send($notifikace);

        return $notifikace;
    }

    public function createProhlidkaRevocationNotifikace(Prohlidka $prohlidka, User $zrusilUzivatel, bool $sendMail): Notifikace
    {
        $pojistnaUdalost = $prohlidka->getPojistnaUdalost();
        $technik = $prohlidka->getTechnik();
        
        $titulek = "PU {$pojistnaUdalost->getCisloPojistnaUdalost()} - Zrušení požadavku na prohlídku";
        $obsah = $this->twig->render('notifikace/prohlidka_revocation_email.html.twig', [
            'pojistnaUdalost' => $pojistnaUdalost,
            'zrusilUzivatel' => $zrusilUzivatel,
        ]);

        $notifikace = new Notifikace($titulek, $obsah, Notifikace::TYP_PROHLIDKA_REVOCATION, $technik, $sendMail);

        $this->entityManager->persist($notifikace);
        $this->entityManager->flush();

        $this->notifikaceSender->send($notifikace);

        return $notifikace;
    }

    public function createPoznamkaTaskAssignmentNotifikace(Poznamka $poznamka, bool $sendMail): Notifikace
    {
        $pojistnaUdalost = $poznamka->getPojistnaUdalost();
        $resitel = $poznamka->getResitel();
        
        if (!$resitel) {
            throw new \InvalidArgumentException('Poznamka must have a resitel to create a task assignment notification');
        }
        
        $titulek = "PU {$pojistnaUdalost->getCisloPojistnaUdalost()} - Byl vám přiřazen nový úkol";
        $obsah = $this->twig->render('notifikace/poznamka_task_assignment_email.html.twig', [
            'poznamka' => $poznamka,
        ]);

        $notifikace = new Notifikace($titulek, $obsah, Notifikace::TYP_POZNAMKA_TASK_ASSIGNMENT, $resitel, $sendMail);

        $this->entityManager->persist($notifikace);
        $this->entityManager->flush();

        $this->notifikaceSender->send($notifikace);

        return $notifikace;
    }
    
    public function createPoznamkaTaskAssignmentRevocationNotifikace(Poznamka $poznamka, User $oldResitel, bool $sendMail): Notifikace
    {
        $pojistnaUdalost = $poznamka->getPojistnaUdalost();
        
        $titulek = "PU {$pojistnaUdalost->getCisloPojistnaUdalost()} - Byl vám odebrán úkol";
        $obsah = $this->twig->render('notifikace/poznamka_task_assignment_revocation_email.html.twig', [
            'poznamka' => $poznamka,
        ]);

        $notifikace = new Notifikace($titulek, $obsah, Notifikace::TYP_POZNAMKA_TASK_ASSIGNMENT_REVOCATION, $oldResitel, $sendMail);

        $this->entityManager->persist($notifikace);
        $this->entityManager->flush();

        $this->notifikaceSender->send($notifikace);

        return $notifikace;
    }
}
