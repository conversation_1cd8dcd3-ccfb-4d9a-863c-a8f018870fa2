<?php

namespace App\Service\Notifikace;

use App\Entity\Notifikace;
use App\Entity\User;
use App\Repository\NotifikaceRepository;
use DateTime;
use Doctrine\ORM\Query;
use Symfony\Component\HttpFoundation\Request;

class FilterManager
{
    private NotifikaceRepository $notifikaceRepository;

    public function __construct(NotifikaceRepository $notifikaceRepository)
    {
        $this->notifikaceRepository = $notifikaceRepository;
    }

    /**
     * @return array<string>
     */
    public function getTypes(Request $request): array
    {
        $requestTypes = explode(',', $request->query->get('typ', ''));
        $types = [];

        foreach ($requestTypes as $category) {
            if (!in_array($category, Notifikace::getTypes()))
                continue;

            $types[] = $category;
        }

        return $types;
    }

    /**
     * @return array<string|bool>
     */
    public function getDates(Request $request): array
    {
        $dateFrom = $request->query->get('od');
        $dateTo = $request->query->get('do');

        return [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'set' => $dateFrom || $dateTo,
        ];
    }

    public function getQuery(User $user, Request $request) : Query
    {
        $dates = $this->getDates($request);
        $types = $this->getTypes($request);

        if ($dates['set'])
            return $this->notifikaceRepository->getByDatesQuery($user, $dates);

        if ($types)
            return $this->notifikaceRepository->getByTypesQuery($user, $types);
        
        return $this->notifikaceRepository->getAllQuery($user);
    }

    public function getActiveFilter(Request $request): ?string
    {
        $dates = $this->getDates($request);
        $types = array_map([Notifikace::class, 'getTypStringFromString'], $this->getTypes($request));

        if ($dates['set'])
        {
            if ($dates['dateFrom'] && $dates['dateTo'])
                return 'Datum od ' . SELF::convertDateFormat($dates['dateFrom']) . ' do ' . SELF::convertDateFormat($dates['dateTo']);

            if ($dates['dateFrom'])
                return 'Datum od ' . SELF::convertDateFormat($dates['dateFrom']);

            if ($dates['dateTo'])
                return 'Datum do ' . SELF::convertDateFormat($dates['dateTo']);
        }

        if ($types)
            return 'Typ: ' . implode(', ', $types);

        return null;
    }

    private static function convertDateFormat(string $date): string
    {
        $dateTime = DateTime::createFromFormat('Y-m-d', $date);
        return $dateTime->format('d.m.Y');
    }
}