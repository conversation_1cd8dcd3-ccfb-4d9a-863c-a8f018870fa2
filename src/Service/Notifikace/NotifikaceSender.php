<?php
namespace App\Service\Notifikace;

use App\Entity\Notifikace;
use App\Entity\User;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;

class NotifikaceSender
{
    private MailerInterface $mailer;

    public function __construct(MailerInterface $mailer)
    {
        $this->mailer = $mailer;
    }

    public function send(Notifikace $notifikace): void
    {
        if ($notifikace->isSendMail())
            $this->sendEmail($notifikace);

        // more types to be added
    }

    private function sendEmail(Notifikace $notifikace): void
    {
        $userEmail = $notifikace->getUzivatel()->getEmail();
        
        // Skip sending if email is empty
        if (empty($userEmail)) {
            return;
        }
        
        $email = (new TemplatedEmail())
            ->from(new Address('<EMAIL>', 'InIn web aplikace'))
            ->to($userEmail)
            ->subject($notifikace->getTitulek())
            ->html($notifikace->getObsah());

        $this->mailer->send($email);
    }
}
