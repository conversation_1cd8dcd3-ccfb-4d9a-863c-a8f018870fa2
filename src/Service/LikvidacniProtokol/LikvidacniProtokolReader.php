<?php

namespace App\Service\LikvidacniProtokol;

use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToReadFile;

class LikvidacniProtokolReader
{

    public function __construct(
        private FilesystemOperator $likvidacniProtokolStorage,
    ) {}


    /**
     * 
     * @param string $filename 
     * @return resource 
     * @throws UnableToReadFile 
     * @throws FilesystemException 
     */
    public function readSouborAsStream(string $filename)
    {

        return $this->likvidacniProtokolStorage->readStream($filename);
    }
}
