<?php

namespace App\Service\LikvidacniProtokol;

use App\Entity\LikvidacniProtokol;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToDeleteFile;
use Psr\Log\LoggerInterface;

class LikvidacniProtokolRemover
{


    public function __construct(
        private FilesystemOperator $likvidacniProtokolStorage,
        private LoggerInterface $logger
    ) {}



    public function deleteFile(LikvidacniProtokol $likvidacniProtokol):void
    {
        try {
            $this->likvidacniProtokolStorage->delete($likvidacniProtokol->getFilename());
        } catch (FilesystemException | UnableToDeleteFile $exception) {
            //throw $th;
            $this->logger->alert(sprintf('Problem deleting file:  "%s" : '.$exception->getMessage(), $likvidacniProtokol->getFilename()));
        }
    }
}
