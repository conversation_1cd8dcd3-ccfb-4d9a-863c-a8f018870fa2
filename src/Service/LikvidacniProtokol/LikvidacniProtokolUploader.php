<?php

namespace App\Service\LikvidacniProtokol;

use App\Entity\LikvidacniProtokol;
use App\Entity\PojistnaUdalost;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use League\Flysystem\FilesystemOperator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\String\Slugger\SluggerInterface;

class LikvidacniProtokolUploader
{

    public function __construct(
        private SluggerInterface $slugger,
        private EntityManagerInterface $entityManager,
        private FilesystemOperator $likvidacniProtokolStorage,
        private RequestStack $requestStack,
        private LoggerInterface $logger
    ) {}



    public function uploadLikvidacniProtokolSoubor(
        UploadedFile $soubor,
        PojistnaUdalost $pojistnaUdalost,
        string $description
        ): ?LikvidacniProtokol
    {
        $originalFilename = pathinfo($soubor->getClientOriginalName(), PATHINFO_FILENAME);
        $fullOriginalname = pathinfo($soubor->getClientOriginalName(), PATHINFO_BASENAME);
        // this is needed to safely include the file name as part of the URL
        $safeFilename = $this->slugger->slug($originalFilename);        
        $newFilename = $safeFilename . '-' . uniqid() . '.' . $soubor->guessExtension();
        $mimeType = $soubor->getMimeType() ?? 'application/octet-stream';

        // Move the file to the directory where likvidacniprotokol files  are stored
        try {
            $stream = fopen($soubor->getPathname(), 'r');
            $this->likvidacniProtokolStorage->writeStream(
                $newFilename,
                $stream
            );
            if (is_resource($stream)) {
                fclose($stream);
            }

            //assign new file
            $newSoubor = new LikvidacniProtokol(
                $pojistnaUdalost,
                $newFilename,
                $fullOriginalname,
                $mimeType,
                $description
            );
            $this->entityManager->persist($newSoubor);
            $this->entityManager->flush();
            
            return ($newSoubor);

        } catch (FileException $exception) {
            // ... handle exception if something happens during file upload
            // add flash ze problem se zpracovanim souboru
            $request = $this->requestStack->getCurrentRequest();
            $session = $request->getSession();
            /**
             * @var Session $session
             */
            $session->getFlashBag()->add(FlashMessageHelper::TYPE_ERROR, 'Chyba při zpracování nahrávaného souboru. Kontaktujte prosím vývojáře');
            $this->logger->alert(sprintf('Problem se zpracovanim souboru:  "%s" : ' . $exception->getMessage(), $originalFilename));
        }
        
        return null;
    }
}
