<?php

namespace App\Service\Ciselniky;

use App\Entity\PojistnePodminky;
use App\Entity\PojistnePodminkySoubor;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use League\Flysystem\FilesystemOperator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\String\Slugger\SluggerInterface;

class PojistnePodminkySouborUploader
{

    public function __construct(
        private SluggerInterface $slugger,
        private EntityManagerInterface $entityManager,
        private FilesystemOperator $pojistnePodminkyStorage,
        private RequestStack $requestStack,
        private LoggerInterface $logger,
        private PojistnePodminkySouborRemover $pojistnePodminkySouborRemover
    )
    {
        
    }

    public function uploadPojistnePodminkySoubor(UploadedFile $soubor, PojistnePodminky $pojistnePodminky): PojistnePodminky
    {
        $originalFilename = pathinfo($soubor->getClientOriginalName(), PATHINFO_FILENAME);
        $fullOriginalname = pathinfo($soubor->getClientOriginalName(), PATHINFO_BASENAME);
        // this is needed to safely include the file name as part of the URL
        $safeFilename = $this->slugger->slug($originalFilename);
        $newFilename = $safeFilename . '-' . uniqid() . '.' . $soubor->guessExtension();
        // Move the file to the directory where brochures are stored
        try {
            $stream = fopen($soubor->getPathname(), 'r');
            $this->pojistnePodminkyStorage->writeStream(
                $newFilename,
                $stream
            );
            if (is_resource($stream)) {
                fclose($stream);
            }
                       
            $existingSoubor = $pojistnePodminky->getPojistnePodminkySoubor();
            if (!$existingSoubor) {
                //assign new file
                $newSoubor = new PojistnePodminkySoubor(
                    $newFilename,
                    $fullOriginalname,
                    $pojistnePodminky
                );
                $this->entityManager->persist($newSoubor);
                $pojistnePodminky->assignPojistnePodminkySoubor($newSoubor);
                return ($pojistnePodminky);
            } else {
                $this->pojistnePodminkySouborRemover->deleteFile($existingSoubor);
                $existingSoubor->changeNames($newFilename, $originalFilename);
            }
        } catch (FileException $exception) {
            // ... handle exception if something happens during file upload
            // add flash ze problem se zpracovanim souboru
            $request = $this->requestStack->getCurrentRequest();
            $session = $request->getSession();
            /**
             * @var Session $session
             */
            $session->getFlashBag()->add(FlashMessageHelper::TYPE_ERROR, 'Chyba při zpracování nahrávaného souboru. Kontaktujte prosím vývojáře');
            $this->logger->alert(sprintf('Problem se zpracovanim souboru:  "%s" : '.$exception->getMessage(), $originalFilename));

        }

        return $pojistnePodminky;
    }



}
