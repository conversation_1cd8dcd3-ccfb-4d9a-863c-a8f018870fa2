<?php

namespace App\Service\Ciselniky;

use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToReadFile;

class PojistnePodminkySouborReader
{

    public function __construct(
        private FilesystemOperator $pojistnePodminkyStorage,
    ) {}


    /**
     * 
     * @param string $filename 
     * @return resource 
     * @throws UnableToReadFile 
     * @throws FilesystemException 
     */
    public function readSouborAsStream(string $filename)
    {

        return $this->pojistnePodminkyStorage->readStream($filename);
    }
}
