<?php

namespace App\Service\Ciselniky;

use App\Dto\Ciselniky\PojistnePodminkyEditInput;
use App\Entity\PojistnePodminky;
use Doctrine\ORM\EntityManagerInterface;

class PojistnePodminkyUpdateService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function updatePojistnePodminky(PojistnePodminky $pojistnePodminky, PojistnePodminkyEditInput $pojistnePodminkyEditInput): PojistnePodminky
    {
        $pojistnePodminky->updateFromInput($pojistnePodminkyEditInput);

        $this->entityManager->flush();

        return $pojistnePodminky;
    }
}