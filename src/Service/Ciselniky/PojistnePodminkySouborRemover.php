<?php

namespace App\Service\Ciselniky;

use App\Entity\PojistnePodminkySoubor;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\UnableToDeleteFile;
use Psr\Log\LoggerInterface;

class PojistnePodminkySouborRemover
{

    public function __construct(
        private FilesystemOperator $pojistnePodminkyStorage,
        private LoggerInterface $logger
    )
    {
        
    }


    public function deleteFile(PojistnePodminkySoubor $pojistnePodminkySoubor):void
    {

        try {
            $this->pojistnePodminkyStorage->delete($pojistnePodminkySoubor->getFilename());
        } catch (FilesystemException | UnableToDeleteFile $exception) {
            //throw $th;
            $this->logger->alert(sprintf('Problem deleting file:  "%s" : '.$exception->getMessage(), $pojistnePodminkySoubor->getFilename()));
        }
        

    }
}
