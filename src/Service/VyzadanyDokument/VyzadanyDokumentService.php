<?php

namespace App\Service\VyzadanyDokument;

use App\Dto\VyzadanyDokument\VyzadanyDokumentEditInput;
use App\Dto\VyzadanyDokument\VyzadanyDokumentNewInput;
use App\Entity\PojistnaUdalost;
use App\Entity\User;
use App\Entity\VyzadanyDokument;
use App\Factory\VyzadanyDokument\VyzadanyDokumentNewFactory;
use App\Repository\PojistnaUdalostRepository;
use App\Repository\VyzadanyDokumentRepository;
use Doctrine\ORM\EntityManagerInterface;

class VyzadanyDokumentService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly VyzadanyDokumentNewFactory $vyzadanyDokumentNewFactory,
        private readonly PojistnaUdalostRepository $pojistnaUdalostRepository,
        private readonly VyzadanyDokumentRepository $vyzadanyDokumentRepository
    ) {
    }

    /**
     * Check if the insurance event is closed
     */
    public function isPojistnaUdalostUzavreno(PojistnaUdalost $pojistnaUdalost): bool
    {
        return $pojistnaUdalost->isUzavreno();
    }

    /**
     * Create a new VyzadanyDokument from custom input
     */
    public function createCustomVyzadanyDokument(VyzadanyDokumentNewInput $input, User $author): VyzadanyDokument
    {
        $newVyzadanyDokument = $this->vyzadanyDokumentNewFactory->createVyzadanyDokument($input);
        $newVyzadanyDokument->modifyPojistnaUdalost($this->pojistnaUdalostRepository->find($input->pojistnaUdalost->getId()));
        $newVyzadanyDokument->modifyAutor($author);
        $this->entityManager->persist($newVyzadanyDokument);

        return $newVyzadanyDokument;
    }

    /**
     * Create multiple VyzadanyDokument entities from predefined templates
     * 
     * @return VyzadanyDokument[]
     */
    public function createPredefinedVyzadanyDokumenty(VyzadanyDokumentNewInput $input, User $author): array
    {
        $createdDocuments = [];

        foreach ($input->pozadavky as $pozadavek) {
            $newInput = new VyzadanyDokumentNewInput();
            $newInput->pojistnaUdalost = $input->pojistnaUdalost;
            $newInput->autor = $author;
            $newInput->coVyzadano = $pozadavek->getTypPozadavku();
            $newInput->stav = $input->stav;

            $newVyzadanyDokument = $this->vyzadanyDokumentNewFactory->createVyzadanyDokument($newInput);
            $this->entityManager->persist($newVyzadanyDokument);
            $createdDocuments[] = $newVyzadanyDokument;
        }

        return $createdDocuments;
    }

    /**
     * Save changes to the database
     */
    public function saveChanges(): void
    {
        $this->entityManager->flush();
    }

    /**
     * Update an existing VyzadanyDokument
     */
    public function updateVyzadanyDokument(VyzadanyDokument $vyzadanyDokument, VyzadanyDokumentEditInput $input): VyzadanyDokument
    {
        $vyzadanyDokument->modify($input);
        return $vyzadanyDokument;
    }

    /**
     * Get all VyzadanyDokument entities for a PojistnaUdalost
     * 
     * @return VyzadanyDokument[]
     */
    public function getVyzadanyDokumentyForPojistnaUdalost(int $pojistnaUdalostId, string $sortOrder = 'DESC'): array
    {
        return $this->vyzadanyDokumentRepository->findAllByPojistnaUdalostId($pojistnaUdalostId, $sortOrder);
    }

    /**
     * Check if the input is valid for creating a new VyzadanyDokument
     */
    public function isValidInput(VyzadanyDokumentNewInput $input): bool
    {
        return ($input->isCustom && $input->coVyzadano) || 
               (!$input->isCustom && !empty($input->pozadavky));
    }

    /**
     * Delete a VyzadanyDokument entity
     */
    public function deleteVyzadanyDokument(VyzadanyDokument $vyzadanyDokument): void
    {
        $this->entityManager->remove($vyzadanyDokument);
        $this->entityManager->flush();
    }
}
