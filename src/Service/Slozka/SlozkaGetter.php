<?php

namespace App\Service\Slozka;

use App\Entity\PojistnaUdalost;
use App\Entity\Slozka;
use App\Exception\Slozka\SlozkaNotFoundException;
use App\Factory\PojistnaUdalost\PojistnaUdalostSlozkaFactory;
use Doctrine\ORM\EntityManagerInterface;

class SlozkaGetter
{

    public function __construct(
        private PojistnaUdalostSlozkaFactory $pojistnaUdalostSlozkaFactory,
        private EntityManagerInterface $entityManager
    ) {}


    public function getAllwaysUkolySlozka(PojistnaUdalost $pojistnaUdalost): Slozka
    {
        // check if slozka for SouborForPoznamka exists and create if not
        try {
            $slozkaUkoly =  $pojistnaUdalost->getSlozkaUkoly();
        } catch (SlozkaNotFoundException $exception) {
            // create slozka for Ukoly
            $slozkaUkoly = $this->pojistnaUdalostSlozkaFactory->createUkolySlozkaForPojistnaUdalost($pojistnaUdalost, $pojistnaUdalost->getSlozkaRoot());
            $pojistnaUdalost->addSlozka($slozkaUkoly);
            $this->entityManager->persist($slozkaUkoly);
            $this->entityManager->flush();
        }

        return $slozkaUkoly;
    }
}
