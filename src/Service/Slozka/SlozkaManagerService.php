<?php

namespace App\Service\Slozka;

use App\Entity\PojistnaUdalost;
use App\Entity\Slozka;
use App\Factory\Slozka\SlozkaFactory;
use App\Repository\SlozkaRepository;
use App\Repository\SouborRepository;
use Doctrine\ORM\EntityManagerInterface;
use League\Flysystem\FilesystemOperator;
use InvalidArgumentException;
use RuntimeException;

class SlozkaManagerService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SlozkaRepository $slozkaRepository,
        private SouborRepository $souborRepository,
        private FilesystemOperator $slozkyStorage,
        private SlozkaFactory $slozkaFactory
    ) {}

    /**
     * Vytvoří novou user pod-složku (deleguje na Factory)
     */
    public function createUserSubfolder(
        string $nazev,
        Slozka $parentSlozka,
        PojistnaUdalost $pojistnaUdalost,
        string $druhSlozky
    ): Slozka {
        // Vytvoření složky pomocí Factory
        $newSlozka = $this->slozkaFactory->createUserSubfolder($nazev, $parentSlozka, $pojistnaUdalost, $druhSlozky);

        // Uložení do databáze
        $this->entityManager->persist($newSlozka);
        $this->entityManager->flush();

        return $newSlozka;
    }

    /**
     * Vytvoří novou systémovou pod-složku (deleguje na Factory)
     */
    public function createSystemSubfolder(
        string $nazev,
        Slozka $parentSlozka,
        PojistnaUdalost $pojistnaUdalost,
        string $druhSlozky
    ): Slozka {
        // Vytvoření systémové složky pomocí Factory
        $newSlozka = $this->slozkaFactory->createSystemSubfolder($nazev, $parentSlozka, $pojistnaUdalost, $druhSlozky);

        // Uložení do databáze
        $this->entityManager->persist($newSlozka);
        $this->entityManager->flush();

        return $newSlozka;
    }

    /**
     * Smaže složku (pouze user složky a pouze prázdné)
     */
    public function deleteFolder(Slozka $slozka): bool
    {
        // Kontrola oprávnění
        if (!$slozka->canBeDeleted()) {
            throw new InvalidArgumentException('Složku nelze smazat - buď není uživatelská nebo není prázdná');
        }

        // Dodatečná kontrola prázdnosti
        if (!$this->isSlozkaEmpty($slozka)) {
            throw new InvalidArgumentException('Složka není prázdná a nelze ji smazat');
        }

        // Smazání fyzické složky
        $physicalPath = $this->getPhysicalPath($slozka);
        try {
            if ($this->slozkyStorage->directoryExists($physicalPath)) {
                $this->slozkyStorage->deleteDirectory($physicalPath);
            }
        } catch (\Exception $e) {
            throw new RuntimeException('Nepodařilo se smazat složku z disku: ' . $e->getMessage());
        }

        // Odebrání z parent složky
        if ($slozka->getParent()) {
            $slozka->getParent()->removeChild($slozka);
        }

        // Smazání z databáze
        $this->entityManager->remove($slozka);
        $this->entityManager->flush();

        return true;
    }

    /**
     * Získá obsah složky (pod-složky a soubory)
     * 
     * @param Slozka $slozka 
     * @return array<mixed>
     */
    public function getFolderContents(Slozka $slozka): array
    {
        $children = $this->slozkaRepository->findChildrenByParent($slozka);
        $soubory = $this->souborRepository->findBySlozka($slozka);

        return [
            'slozka' => $slozka,
            'podslozky' => $children,
            'soubory' => $soubory,
            'breadcrumbs' => $slozka->getBreadcrumbs()
        ];
    }

    /**
     * Zkontroluje, zda je složka prázdná
     */
    public function isSlozkaEmpty(Slozka $slozka): bool
    {
        return $this->slozkaRepository->isSlozkaEmpty($slozka);
    }

    /**
     * Získá fyzickou cestu ke složce
     */
    private function getPhysicalPath(Slozka $slozka): string
    {
        if ($slozka->getCesta()) {
            return $slozka->getCesta();
        }

        // Fallback - sestaví cestu z hierarchie
        return $slozka->getFullPath();
    }

    /**
     * Najde root složky pro pojistnou událost
     * 
     * @return array<mixed>
     */
    public function getRootSlozkyForPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): array
    {
        return $this->slozkaRepository->findRootSlozkyByPojistnaUdalost($pojistnaUdalost);
    }

    /**
     * Najde složku s kontrolou oprávnění
     */
    public function findSlozkaWithPermissionCheck(int $id, PojistnaUdalost $pojistnaUdalost): ?Slozka
    {
        return $this->slozkaRepository->findSlozkaWithPermissionCheck($id, $pojistnaUdalost);
    }

    /**
     * Validuje název složky (deleguje na Factory)
     * 
     * @return array<mixed>
     */
    public function validateSlozkaName(string $nazev): array
    {
        return $this->slozkaFactory->validateSlozkaName($nazev);
    }
}
