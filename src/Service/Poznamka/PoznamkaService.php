<?php

namespace App\Service\Poznamka;

use App\Dto\Poznamka\PoznamkaEditDueDateInput;
use App\Dto\Poznamka\PoznamkaEditInput;
use App\Dto\Poznamka\PoznamkaResolveInput;
use App\Entity\Poznamka;
use App\Entity\User;
use App\Service\Notifikace\NotifikaceCreator;
use Doctrine\ORM\EntityManagerInterface;

class PoznamkaService
{
    private EntityManagerInterface $entityManager;
    private NotifikaceCreator $notifikaceCreator;

    public function __construct(EntityManagerInterface $entityManager, NotifikaceCreator $notifikaceCreator)
    {
        $this->entityManager = $entityManager;
        $this->notifikaceCreator = $notifikaceCreator;
    }

    public function updatePoznamka(Poznamka $poznamka, PoznamkaEditInput $poznamkaEditInput): Poznamka
    {
        // Uložení původního řešitele před modifikací
        $oldResitel = $poznamka->getResitel();
        $wasTask = $poznamka->isTask();
        
        // Modifikace poznámky
        $poznamka->modify($poznamkaEditInput);
        $this->entityManager->flush();
        
        // Kontrola, zda došlo ke změně řešitele a zda jde o úkol
        if ($poznamka->isTask() && $poznamka->getResitel() !== null) {
            $newResitel = $poznamka->getResitel();
            
            // Pokud došlo ke změně řešitele
            if ($oldResitel !== null && $newResitel !== null && $oldResitel->getId() !== $newResitel->getId()) {
                // Vytvoření notifikace pro nového řešitele
                $this->notifikaceCreator->createPoznamkaTaskAssignmentNotifikace($poznamka, true);
                
                // Vytvoření notifikace pro původního řešitele
                $this->notifikaceCreator->createPoznamkaTaskAssignmentRevocationNotifikace($poznamka, $oldResitel, true);
            }
            // Pokud byl přidán nový řešitel (předtím nebyl žádný)
            elseif ($oldResitel === null && $newResitel !== null) {
                // Vytvoření notifikace pro nového řešitele
                $this->notifikaceCreator->createPoznamkaTaskAssignmentNotifikace($poznamka, true);
            }
            // Pokud se poznámka stala úkolem a má řešitele
            elseif (!$wasTask && $newResitel !== null) {
                // Vytvoření notifikace pro řešitele
                $this->notifikaceCreator->createPoznamkaTaskAssignmentNotifikace($poznamka, true);
            }
        }
        
        return $poznamka;
    }

    public function updatePoznamkaDueDate(Poznamka $poznamka, PoznamkaEditDueDateInput $editInput): Poznamka
    {
        $poznamka->modifyDueDate($editInput);
        $this->entityManager->flush();
        return $poznamka;
    }

    public function resolvePoznamka(Poznamka $poznamka, PoznamkaResolveInput $poznamkaResolveInput): Poznamka
    {
        $poznamka->modifyResolve($poznamkaResolveInput);
        $this->entityManager->flush();
        return $poznamka;
    }
}
