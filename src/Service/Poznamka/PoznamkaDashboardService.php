<?php
namespace App\Service\Poznamka;

use App\Entity\Poznamka;
use App\Entity\User;
use App\Repository\PoznamkaRepository;
use Symfony\Component\Security\Core\User\UserInterface;

class PoznamkaDashboardService
{
    public function __construct(
        private PoznamkaRepository $repository
    ) {}

    /**
     * @return array<string, array<string, Poznamka[]|int>|int>|null
     */
    public function getTaskBoxes(UserInterface $user): ?array
    {
        /**
         * @var User $user
         */
        if (!$user->isAdministrator() AND !$user->isLikvidator()) return null;

        $boxes['resitel'] = [
            'tasks' => $this->repository->findClosestUnlosedTasksByResitel($user),
            'count' => $this->repository->findUnclosedTaskCountByResitel($user)
        ];

        $boxes['autor'] = [
            'tasks' => $this->repository->findClosestUnclosedTasksByAutor($user),
            'count' => $this->repository->findUnclosedTaskCountByAutor($user)
        ];

        if ($user->isLikvidator()) return $boxes;

        $boxes['all'] = [
            'tasks' => $this->repository->findClosestUnclosedTasksByAutor(null),
            'count' => $this->repository->findUnclosedTaskCountByAutor(null)
        ];

        $boxes['overdue'] = $this->repository->findOverdueTaskCount();

        return $boxes;
    }
}