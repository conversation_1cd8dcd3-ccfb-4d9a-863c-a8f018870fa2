<?php

namespace App\Security\Voter;

use App\Entity\Poznamka;
use App\Entity\User;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * @extends Voter<string, Poznamka>
 */
final class PoznamkaVoter extends Voter
{
    public const FULL_EDIT = 'POZNAMKA_FULL_EDIT';
    public const DATE_EDIT = 'POZNAMKA_DATE_EDIT';
    public const RESOLVE = 'POZNAMKA_RESOLVE';

    protected function supports(string $attribute, mixed $subject): bool
    {
        return in_array(
            $attribute,
            [
                self::FULL_EDIT,
                self::DATE_EDIT,
                self::RESOLVE
            ]
        )
            && $subject instanceof Poznamka;
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();

        // if the user is anonymous, do not grant access
        if (!$user instanceof User) {
            return false;
        }

        /**
         * @var Poznamka $poznamka
         */
        $poznamka =  $subject;

        return match ($attribute) {
            self::FULL_EDIT => $this->canFullEdit($poznamka, $user),
            self::DATE_EDIT => $this->canDateEdit($poznamka, $user),
            self::RESOLVE => $this->canResolve($poznamka, $user),
            default => throw new \LogicException('This code should not be reached!')
        };
    }


    private function canFullEdit(Poznamka $poznamka, User $user): bool
    {
        return ($poznamka->getAutor()->getId() === $user->getId());
    }

    private function canDateEdit(Poznamka $poznamka, User $user): bool
    {
        $condition1 = ($poznamka->getResitel() == $user);
        $condition2 = ($poznamka->getAutor()->getId() === $user->getId());
        return $condition1 || $condition2;
    }

    /**
     * Just author can resolve poznamka/task
     * 
     * @param Poznamka $poznamka 
     * @param User $user 
     * @return bool 
     */
    private function canResolve(Poznamka $poznamka, User $user): bool
    {
        return ($poznamka->getAutor()->getId() === $user->getId());
    }
}
