<?php

declare(strict_types=1);

namespace App\Security\Voter;

use App\Entity\PojistnaUdalost;
use App\Entity\User;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * Voter pro autorizaci přístupu k pojistným událostem
 * 
 * Oprávnění:
 * - VIEW: zobrazení dat pojistné události
 * - EDIT: úprava dat, nahr<PERSON><PERSON><PERSON> souborů, vytváření složek
 * - DELETE_FILE: ma<PERSON><PERSON><PERSON> souborů a složek
 * 
 * Přístup mají:
 * - ROLE_COM_ADMIN: všechna oprávnění ke všem PU
 * - Přiřazený likvidátor: všechna oprávnění k přiřazené PU
 * - <PERSON><PERSON><PERSON> uživatelé: všechna oprávnění k přiřazené PU
 */
/**
 * @extends Voter<string, PojistnaUdalost>
 */
class PojistnaUdalostVoter extends Voter
{
    public const VIEW = 'VIEW';
    public const EDIT = 'EDIT';
    public const DELETE_FILE = 'DELETE_FILE';

    /**
     * Určuje, zda tento voter podporuje daný atribut a subjekt
     */
    protected function supports(string $attribute, mixed $subject): bool
    {
        // Podporujeme pouze naše definované atributy
        if (!in_array($attribute, [self::VIEW, self::EDIT, self::DELETE_FILE])) {
            return false;
        }

        // Subjekt musí být instance PojistnaUdalost
        return $subject instanceof PojistnaUdalost;
    }

    /**
     * Provádí autorizační logiku
     */
    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();

        // Uživatel musí být přihlášen
        if (!$user instanceof User) {
            return false;
        }

        /** @var PojistnaUdalost $pojistnaUdalost */
        $pojistnaUdalost = $subject;

        // ROLE_COM_ADMIN má všechna oprávnění ke všem pojistným událostem
        if (in_array('ROLE_COM_ADMIN', $user->getRoles())) {
            return true;
        }

        // Kontrola přístupu podle typu oprávnění
        return match ($attribute) {
            self::VIEW => $this->canView($pojistnaUdalost, $user),
            self::EDIT => $this->canEdit($pojistnaUdalost, $user),
            self::DELETE_FILE => $this->canDeleteFile($pojistnaUdalost, $user),
            default => false,
        };
    }

    /**
     * Kontrola oprávnění VIEW - zobrazení dat pojistné události
     */
    private function canView(PojistnaUdalost $pojistnaUdalost, User $user): bool
    {
        return $this->hasAccessToPojistnaUdalost($pojistnaUdalost, $user);
    }

    /**
     * Kontrola oprávnění EDIT - úprava dat, nahrávání souborů, vytváření složek
     */
    private function canEdit(PojistnaUdalost $pojistnaUdalost, User $user): bool
    {
        // Prozatím stejná logika jako VIEW
        // V budoucnu lze rozlišit podle rolí nebo specifických pravidel
        return $this->hasAccessToPojistnaUdalost($pojistnaUdalost, $user);
    }

    /**
     * Kontrola oprávnění DELETE_FILE - mazání souborů a složek
     */
    private function canDeleteFile(PojistnaUdalost $pojistnaUdalost, User $user): bool
    {
        // Prozatím stejná logika jako VIEW
        // V budoucnu lze přidat přísnější omezení pro mazání
        return $this->hasAccessToPojistnaUdalost($pojistnaUdalost, $user);
    }

    /**
     * Základní kontrola přístupu k pojistné události
     */
    private function hasAccessToPojistnaUdalost(PojistnaUdalost $pojistnaUdalost, User $user): bool
    {
        // 1. Přiřazený likvidátor má přístup
        if ($pojistnaUdalost->getLikvidator() === $user) {
            return true;
        }

        // 2. Další přiřazení uživatelé mají přístup
        if ($pojistnaUdalost->getDalsiUzivatele()->contains($user)) {
            return true;
        }

        // 3. ROLE_LIKVIDATOR má prozatím přístup ke všem PU
        // TODO: V budoucnu omezit jen na přiřazené PU
        if (in_array('ROLE_LIKVIDATOR', $user->getRoles())) {
            return true;
        }

        return false;
    }

    /**
     * Debug metoda pro zjištění důvodu přístupu (pro development)
     */
    public function getAccessReason(PojistnaUdalost $pojistnaUdalost, User $user): string
    {
        if (in_array('ROLE_COM_ADMIN', $user->getRoles())) {
            return 'ROLE_COM_ADMIN - přístup ke všem PU';
        }

        if ($pojistnaUdalost->getLikvidator() === $user) {
            return 'Přiřazený likvidátor';
        }

        if ($pojistnaUdalost->getDalsiUzivatele()->contains($user)) {
            return 'Další přiřazený uživatel';
        }

        if (in_array('ROLE_LIKVIDATOR', $user->getRoles())) {
            return 'ROLE_LIKVIDATOR - dočasný přístup ke všem PU';
        }

        return 'Žádný přístup';
    }
}
