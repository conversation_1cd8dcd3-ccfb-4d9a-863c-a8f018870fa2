<?php

namespace App\Repository;

use App\Entity\PojistnePodminky;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PojistnePodminky>
 */
class PojistnePodminkyRepository extends ServiceEntityRepository
{
    public const ALIAS = 'pp';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PojistnePodminky::class);
    }

    public function getAllQuery():Query
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        $queryBuilder = $queryBuilder
            ->orderBy(self::ALIAS.'.id', 'ASC');
            return $queryBuilder->getQuery();
    }

    //    /**
    //     * @return PojistnePodminky[] Returns an array of PojistnePodminky objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?PojistnePodminky
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
