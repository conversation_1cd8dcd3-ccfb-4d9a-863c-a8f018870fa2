<?php

namespace App\Repository;

use App\Entity\PojistnaUdalost;
use App\Entity\Slozka;
use App\Entity\Soubor;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Slozka>
 *
 * @method Slozka|null find($id, $lockMode = null, $lockVersion = null)
 * @method Slozka|null findOneBy(array $criteria, array $orderBy = null)
 * @method Slozka[]    findAll()
 * @method Slozka[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SlozkaRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Slozka::class);
    }

    //    /**
    //     * @return Slozka[] Returns an array of Slozka objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('s')
    //            ->andWhere('s.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('s.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Slozka
    //    {
    //        return $this->createQueryBuilder('s')
    //            ->andWhere('s.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }

    public function isUuidUsed(string $uuid): bool 
    {
        $result = (int)
            $this->createQueryBuilder('s')
            ->select('COUNT(s.id)')
            ->where('s.identifikator = :uuid')
            ->setParameter('uuid', $uuid)
            ->getQuery()
            ->getSingleScalarResult();

        return $result > 0;
    }

    /**
     * Najde všechny pod-složky pro danou složku
     * @return array<mixed>
     */
    public function findChildrenByParent(Slozka $parent): array
    {
        return $this->createQueryBuilder('s')
            ->where('s.parent = :parent')
            ->setParameter('parent', $parent)
            ->orderBy('s.nazev', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Najde root složky pro pojistnou událost
     * @return array<mixed>
     */
    public function findRootSlozkyByPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): array
    {
        return $this->createQueryBuilder('s')
            ->where('s.pojistnaUdalost = :pojistnaUdalost')
            ->andWhere('s.parent IS NULL')
            ->setParameter('pojistnaUdalost', $pojistnaUdalost)
            ->orderBy('s.nazev', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Zkontroluje, zda je složka prázdná (nemá žádné soubory ani pod-složky)
     */
    public function isSlozkaEmpty(Slozka $slozka): bool
    {
        // Zkontroluj pod-složky
        $childrenCount = $this->createQueryBuilder('s')
            ->select('COUNT(s.id)')
            ->where('s.parent = :slozka')
            ->setParameter('slozka', $slozka)
            ->getQuery()
            ->getSingleScalarResult();

        if ($childrenCount > 0) {
            return false;
        }

        // Zkontroluj soubory
        $souborCount = $this->getEntityManager()
            ->getRepository(Soubor::class)
            ->createQueryBuilder('so')
            ->select('COUNT(so.id)')
            ->where('so.slozka = :slozka')
            ->setParameter('slozka', $slozka)
            ->getQuery()
            ->getSingleScalarResult();

        return $souborCount == 0;
    }

    /**
     * Najde všechny složky v hierarchii pro pojistnou událost
     * @param PojistnaUdalost $pojistnaUdalost 
     * @return array<mixed>
     */
    public function findAllSlozkyForPojistnaUdalost($pojistnaUdalost): array
    {
        return $this->createQueryBuilder('s')
            ->where('s.pojistnaUdalost = :pojistnaUdalost')
            ->setParameter('pojistnaUdalost', $pojistnaUdalost)
            ->orderBy('s.nazev', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Najde složku podle ID s kontrolou oprávnění
     * @param PojistnaUdalost $pojistnaUdalost 
     */
    public function findSlozkaWithPermissionCheck(int $id, $pojistnaUdalost): ?Slozka
    {
        return $this->createQueryBuilder('s')
            ->where('s.id = :id')
            ->andWhere('s.pojistnaUdalost = :pojistnaUdalost')
            ->setParameter('id', $id)
            ->setParameter('pojistnaUdalost', $pojistnaUdalost)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
