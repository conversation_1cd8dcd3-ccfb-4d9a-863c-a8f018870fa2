<?php

namespace App\Repository;

use App\Entity\Prohlidka;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Prohlidka>
 */
class ProhlidkaRepository extends ServiceEntityRepository
{

    const ALIAS = 'prohlidka';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Prohlidka::class);
    }

    //    /**
    //     * @return Prohlidka[] Returns an array of Prohlidka objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Prohlidka
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
