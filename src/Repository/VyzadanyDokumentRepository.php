<?php

namespace App\Repository;

use App\Entity\VyzadanyDokument;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<VyzadanyDokument>
 *
 * @method VyzadanyDokument|null find($id, $lockMode = null, $lockVersion = null)
 * @method VyzadanyDokument|null findOneBy(array $criteria, array $orderBy = null)
 * @method VyzadanyDokument[]    findAll()
 * @method VyzadanyDokument[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class VyzadanyDokumentRepository extends ServiceEntityRepository
{
    const ALIAS = 'vyzadany_dokument';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, VyzadanyDokument::class);
    }

    /**
     * @param int $pojistnaUdalostId
     * @param string $sortOrder
     * @return VyzadanyDokument[]
     */
    public function findAllByPojistnaUdalostId(int $pojistnaUdalostId, string $sortOrder = 'DESC'): array
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->leftJoin(self::ALIAS.'.pojistnaUdalost', 'pou')
            ->andWhere('pou.id = :pojistnaUdalostId')
            ->setParameter('pojistnaUdalostId', $pojistnaUdalostId)
            ->addOrderBy('CASE 
                WHEN '.self::ALIAS.'.stav = :stav_vyzadano THEN 1 
                WHEN '.self::ALIAS.'.stav = :stav_dolozeno THEN 2 
                WHEN '.self::ALIAS.'.stav = :stav_uz_nezadano THEN 3 
                ELSE 4 
                END', 'ASC')
            ->setParameter('stav_vyzadano', VyzadanyDokument::STAV_VYZADANO)
            ->setParameter('stav_dolozeno', VyzadanyDokument::STAV_DOLOZENO)
            ->setParameter('stav_uz_nezadano', VyzadanyDokument::STAV_UZ_NEZADANO)
            ->addOrderBy(self::ALIAS.'.createdAt', $sortOrder)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int $pojistnaUdalostId
     * @param string $stav
     * @param string $sortOrder
     * @return VyzadanyDokument[]
     */
    public function findAllByPojistnaUdalostIdAndStav(int $pojistnaUdalostId, string $stav, string $sortOrder = 'DESC'): array
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->leftJoin(self::ALIAS.'.pojistnaUdalost', 'pou')
            ->andWhere('pou.id = :pojistnaUdalostId')
            ->setParameter('pojistnaUdalostId', $pojistnaUdalostId)
            ->andWhere(self::ALIAS.'.stav = :stav')
            ->setParameter('stav', $stav)
            ->orderBy(self::ALIAS.'.createdAt', $sortOrder)
            ->getQuery()
            ->getResult();
    }
}
