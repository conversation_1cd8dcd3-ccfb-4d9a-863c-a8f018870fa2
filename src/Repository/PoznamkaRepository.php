<?php

namespace App\Repository;

use App\Entity\Poznamka;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Poznamka>
 *
 * @method Poznamka|null find($id, $lockMode = null, $lockVersion = null)
 * @method Poznamka|null findOneBy(array $criteria, array $orderBy = null)
 * @method Poznamka[]    findAll()
 * @method Poznamka[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PoznamkaRepository extends ServiceEntityRepository
{

    const ALIAS = 'poznamka';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Poznamka::class);
    }

    //    /**
    //     * @return Poznamka[] Returns an array of Poznamka objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Poznamka
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
    
    /**
     * @param int $pojistnaUdalostId
     * @param string $sortOrder
     * @return Poznamka[]
     */
    public function findAllNotesByPojistnaUdalostId(int $pojistnaUdalostId, string $sortOrder = 'DESC'): array
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->leftJoin(self::ALIAS.'.pojistnaUdalost', 'pou')
            ->andWhere('pou.id = :pojistnaUdalostId')
            ->setParameter('pojistnaUdalostId', $pojistnaUdalostId)
            ->orderBy(self::ALIAS.'.casVytvoreno', $sortOrder)
            ->getQuery()
            ->getResult();
    }
    
    /**
     * @param int $pojistnaUdalostId
     * @param string $sortOrder
     * @return Poznamka[]
     */
    public function findAllUnfinishedTasksByPojistnaUdalostId(int $pojistnaUdalostId, string $sortOrder = 'DESC'): array
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->leftJoin(self::ALIAS.'.pojistnaUdalost', 'pou')
            ->andWhere('pou.id = :pojistnaUdalostId')
            ->setParameter('pojistnaUdalostId', $pojistnaUdalostId)
            ->andWhere(self::ALIAS.'.withTask = :istask')
            ->setParameter('istask', true)
            ->andWhere(self::ALIAS . '.vyreseno = FALSE')
            ->orderBy(self::ALIAS.'.casVytvoreno', $sortOrder)
            ->getQuery()
            ->getResult();
    }
    

    /**
     * Vrací připnuté poznámky k pojistné události
     * 
     * @param int $pojistnaUdalostId
     * @param string $sortOrder
     * @return Poznamka[]
     */
    public function findPinnedNotesByPojistnaUdalostId(int $pojistnaUdalostId, string $sortOrder = 'DESC'): array
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->leftJoin(self::ALIAS.'.pojistnaUdalost', 'pou')
            ->andWhere('pou.id = :pojistnaUdalostId')
            ->andWhere(self::ALIAS.'.pinned = :pinned')
            ->setParameter('pojistnaUdalostId', $pojistnaUdalostId)
            ->setParameter('pinned', true)
            ->orderBy(self::ALIAS.'.casVytvoreno', $sortOrder)
            ->getQuery()
            ->getResult();
    }
    
    /**
     * Vrací nepřipnuté poznámky k pojistné události
     * 
     * @param int $pojistnaUdalostId
     * @param string $sortOrder
     * @return Poznamka[]
     */
    public function findUnpinnedNotesByPojistnaUdalostId(int $pojistnaUdalostId, string $sortOrder = 'DESC'): array
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->leftJoin(self::ALIAS.'.pojistnaUdalost', 'pou')
            ->andWhere('pou.id = :pojistnaUdalostId')
            ->andWhere(self::ALIAS.'.pinned = :pinned')
            ->setParameter('pojistnaUdalostId', $pojistnaUdalostId)
            ->setParameter('pinned', false)
            ->orderBy(self::ALIAS.'.casVytvoreno', $sortOrder)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Poznamka[]
     */
    public function findClosestUnlosedTasksByResitel(User $resitel, int $limit = 5): array
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->where(self::ALIAS . '.resitel = :resitel')
            ->setParameter('resitel', $resitel)
            ->andWhere(self::ALIAS . '.casVyreseni is NULL')
            ->orderBy(self::ALIAS . '.termin', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findUnclosedTaskCountByResitel(User $resitel): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.resitel = :resitel')
            ->setParameter('resitel', $resitel)
            ->andWhere(self::ALIAS . '.casVyreseni is NULL')
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @return Poznamka[]
     */
    public function findClosestUnclosedTasksByAutor(?User $autor, int $limit = 5): array
    {
        $qb = $this->createQueryBuilder(self::ALIAS)
            ->where(self::ALIAS . '.casVyreseni is NULL');

        if ($autor)
            $qb->andWhere(self::ALIAS . '.autor = :autor')
            ->setParameter('autor', $autor);

        return $qb->orderBy(self::ALIAS . '.termin', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findUnclosedTaskCountByAutor(?User $autor): int
    {
        $qb = $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.casVyreseni is NULL');
        
        if ($autor) $qb
            ->andWhere(self::ALIAS . '.autor = :autor')
            ->setParameter('autor', $autor);
            
        return $qb
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findUnclosedTaskCount(): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->andWhere(self::ALIAS . '.casVyreseni is NULL')
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findOverdueTaskCount(): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->andWhere(self::ALIAS . '.casVyreseni is NULL')
            ->andWhere(self::ALIAS . '.termin < :now')
            ->setParameter('now', new \DateTime())
            ->getQuery()
            ->getSingleScalarResult();
    }
}
