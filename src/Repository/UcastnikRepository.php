<?php

namespace App\Repository;

use App\Entity\PojistnaUdalost;
use App\Entity\Ucastnik;
use App\Enum\RoleUcastnika;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Ucastnik>
 */
class UcastnikRepository extends ServiceEntityRepository
{

    const ALIAS = 'ucastnik';


    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Ucastnik::class);
    }


    public function getPojistenyForPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): mixed
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        $queryBuilder->andWhere(self::ALIAS.'.pojistnaUdalost = :udalost')
        ->andWhere(self::ALIAS.'.roleUcastnika = :pojisteny')
        ->setParameter('pojisteny', RoleUcastnika::ROLE_POJISTENY->value)
        ->setParameter('udalost', $pojistnaUdalost);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * @return Ucastnik[]
     */
    public function getPoskozeniForPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): array
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        $queryBuilder->andWhere(self::ALIAS.'.pojistnaUdalost = :udalost')
        ->andWhere(self::ALIAS.'.roleUcastnika = :poskozeny')
        ->setParameter('poskozeny', RoleUcastnika::ROLE_POSKOZENY->value)
        ->setParameter('udalost', $pojistnaUdalost);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return Ucastnik[]
     */
    public function getPovereneOsobyForPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): array
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        $queryBuilder->andWhere(self::ALIAS.'.pojistnaUdalost = :udalost')
        ->andWhere(self::ALIAS.'.roleUcastnika = :poverenaOsoba')
        ->setParameter('poverenaOsoba', RoleUcastnika::ROLE_POVERENA_OSOBA->value)
        ->setParameter('udalost', $pojistnaUdalost);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return Ucastnik[]
     */
    public function getKontaktniOsobyForPojistnaUdalost(PojistnaUdalost $pojistnaUdalost): array
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        $queryBuilder->andWhere(self::ALIAS.'.pojistnaUdalost = :udalost')
        ->andWhere(self::ALIAS.'.roleUcastnika = :kontaktniOsoba')
        ->setParameter('kontaktniOsoba', RoleUcastnika::ROLE_KONTAKTNI_OSOBA->value)
        ->setParameter('udalost', $pojistnaUdalost);

        return $queryBuilder->getQuery()->getResult();
    }

    //    /**
    //     * @return Ucastnik[] Returns an array of Ucastnik objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('u')
    //            ->andWhere('u.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('u.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Ucastnik
    //    {
    //        return $this->createQueryBuilder('u')
    //            ->andWhere('u.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
