<?php

namespace App\Repository;

use App\Entity\Soubor;
use App\Entity\Slozka;
use BadMethodCallException;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\Persistence\ManagerRegistry;
use InvalidArgumentException;
use RuntimeException;
use LogicException;

/**
 * @extends ServiceEntityRepository<Soubor>
 */
class SouborRepository extends ServiceEntityRepository
{
    private const ALIAS = 's';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Soubor::class);
    }

    public function countBySlozka(Slozka $slozka): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select(sprintf('COUNT(%s.id)', self::ALIAS))
            ->andWhere(sprintf('%s.slozka = :slozka', self::ALIAS))
            ->setParameter('slozka', $slozka)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Find all Soubor entities by Slozka
     * 
     * @param Slozka $slozka 
     * @return array<mixed> 
     * @throws BadMethodCallException 
     * @throws InvalidArgumentException 
     * @throws RuntimeException 
     * @throws LogicException 
     * @throws InvalidArgumentException 
     * @throws ORMException 
     */
    public function findBySlozka(Slozka $slozka): array
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->andWhere(sprintf('%s.slozka = :slozka', self::ALIAS))
            ->setParameter('slozka', $slozka)
            ->orderBy(sprintf('%s.createdAt', self::ALIAS), 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find all Soubor entities by Slozka with custom sorting
     * 
     * @param Slozka $slozka 
     * @param string $sortField 
     * @param string $sortOrder 
     * @return array<mixed> 
     * @throws BadMethodCallException 
     * @throws InvalidArgumentException 
     * @throws RuntimeException 
     * @throws LogicException 
     * @throws InvalidArgumentException 
     * @throws ORMException 
     */
    public function findBySlozkaWithSort(Slozka $slozka, string $sortField = 'createdAt', string $sortOrder = 'desc'): array
    {
        // Validace sort field
        $allowedFields = ['createdAt'];
        if (!in_array($sortField, $allowedFields)) {
            $sortField = 'createdAt';
        }

        // Validace sort order
        $sortOrder = strtoupper($sortOrder);
        if (!in_array($sortOrder, ['ASC', 'DESC'])) {
            $sortOrder = 'DESC';
        }

        return $this->createQueryBuilder(self::ALIAS)
            ->andWhere(sprintf('%s.slozka = :slozka', self::ALIAS))
            ->setParameter('slozka', $slozka)
            ->orderBy(sprintf('%s.%s', self::ALIAS, $sortField), $sortOrder)
            ->getQuery()
            ->getResult();
    }
}
