<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\PojistnaUdalost;
use Doctrine\ORM\Query;

/**
 * @extends ServiceEntityRepository<PojistnaUdalost>
 */
class PojistnaUdalostArchivRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PojistnaUdalost::class);
    }

    public function getAllQuery(): Query
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder->where('p.archive = :archive')->setParameter('archive', true);
        $queryBuilder->orderBy('p.id', 'ASC');

        return $queryBuilder->getQuery();
    }

    public function getAllCount(): int
    {
        return $this->createQueryBuilder('p')
            ->select('COUNT(p.id)')
            ->where('p.archive = :archive')->setParameter('archive', true)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
