<?php

namespace App\Repository;

use App\Entity\Notifikace;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Notifikace>
 */
class NotifikaceRepository extends ServiceEntityRepository
{
    const ALIAS = 'ntf';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Notifikace::class);
    }

    public function getUnreadCount(User $user): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.precteno = :precteno')
            ->setParameter('precteno', false)
            ->andWhere(self::ALIAS . '.uzivatel = :uzivatel')
            ->setParameter('uzivatel', $user)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getAllCount(User $user): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.uzivatel = :uzivatel')
            ->setParameter('uzivatel', $user)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @param array<string|bool> $dates
     */
    public function getByDatesQuery(User $user, array $dates): Query
    {
        $qb = $this->createQueryBuilder(self::ALIAS)
            ->where(self::ALIAS . '.uzivatel = :uzivatel')
            ->setParameter('uzivatel', $user);

        if ($dates['dateFrom']) {
            $qb->andWhere(self::ALIAS . '.datumVytvoreni >= :dateFrom')
                ->setParameter('dateFrom', $dates['dateFrom']);
        }

        if ($dates['dateTo']) {
            $qb->andWhere(self::ALIAS . '.datumVytvoreni <= :dateTo')
                ->setParameter('dateTo', $dates['dateTo']);
        }

        return $qb->getQuery();
    }
    
    /**
     * @param array<string> $types
     */
    public function getByTypesQuery(User $user, array $types): Query
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->where(self::ALIAS . '.uzivatel = :uzivatel')
            ->setParameter('uzivatel', $user)
            ->andWhere(self::ALIAS . '.typ IN (:types)')
            ->setParameter('types', $types)
            ->getQuery();
    }

    public function getAllQuery(User $user): Query
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->where(self::ALIAS . '.uzivatel = :uzivatel')
            ->setParameter('uzivatel', $user)
            ->orderBy(self::ALIAS . '.id', 'DESC')
            ->getQuery();
    }
}
