<?php

namespace App\Repository;

use App\Entity\Vozidlo;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\Query; // Import Query class from Doctrine ORM

/**
 * @extends ServiceEntityRepository<Vozidlo>
 *
 * @method Vozidlo|null find($id, $lockMode = null, $lockVersion = null)
 * @method Vozidlo|null findOneBy(array $criteria, array $orderBy = null)
 * @method Vozidlo[]    findAll()
 * @method Vozidlo[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class VozidloRepository extends ServiceEntityRepository
{
    
    const ALIAS = 'v';
    
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Vozidlo::class);
    }
    
    public function getAllQuery(): Query // Adjust return type to Query
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        $queryBuilder->orderBy(self::ALIAS.'.', 'ASC');
        
        return $queryBuilder->getQuery();
    }
}
