<?php

namespace App\Repository;

use App\Entity\PojistnaUdalost;
use App\Entity\User;
use App\Enum\StavLikvidace;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * @extends ServiceEntityRepository<PojistnaUdalost>
 *
 * @method PojistnaUdalost|null find($id, $lockMode = null, $lockVersion = null)
 * @method PojistnaUdalost|null findOneBy(array $criteria, array $orderBy = null)
 * @method PojistnaUdalost[]    findAll()
 * @method PojistnaUdalost[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PojistnaUdalostRepository extends ServiceEntityRepository
{
    public const ALIAS = 'pou';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PojistnaUdalost::class);
    }

    /**
     * 
     * @param UserInterface $user 
     * @return Query 
     */
    public function getAllQuery(UserInterface $user, string $sort, string $dir): Query
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS)
            ->leftJoin(self::ALIAS . '.ucastnici', 'ucastnik')
            ->addSelect('ucastnik')
            ->where(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->orderBy($sort, $dir);
            

        $queryBuilder =  $this->filterListForUserWithRole($user, $queryBuilder);

        return $queryBuilder->getQuery();
    }

    /**
     * @param array{
     *     dates?: array{dateFrom?: string, dateTo?: string, dateType?: string},
     *     categories?: string[],
     *     misc?: array{likvidator?: string, stav?: string, zadavatel?: string, technik?: string}
     * } $filters 
     * @param UserInterface $user 
     * @return Query 
     */
    public function getFilteredQuery(array $filters, ?UserInterface $user): Query
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS)
            ->where(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->orderBy(self::ALIAS . '.id', 'ASC');

        // Dates
        $dates = $filters['dates'] ?? [];
        $dateField = $dates['dateType'] ?? 'prijato'; // default to prijato

        if (!empty($dates['dateFrom'])) {
            $field = $dateField === 'prijato' ? 'datumPrijetiOdPojistovny' : 'datumUkonceniLikvidace';
            $queryBuilder->andWhere(self::ALIAS . ".$field >= :dateFrom")
                ->setParameter('dateFrom', new \DateTime($dates['dateFrom']));
        }
        
        if (!empty($dates['dateTo'])) {
            $field = $dateField === 'prijato' ? 'datumPrijetiOdPojistovny' : 'datumUkonceniLikvidace';
            $dateTo = new \DateTime($dates['dateTo']);
            $dateTo->setTime(23, 59, 59);
            
            $queryBuilder->andWhere(self::ALIAS . ".$field <= :dateTo")
                ->setParameter('dateTo', $dateTo);
        }

        // Categories
        $categories = $filters['categories'] ?? [];
        if (!empty($categories)) {
            $queryBuilder->andWhere(self::ALIAS . '.kategorie IN (:categories)')
            ->setParameter('categories', $categories);
        }

        // Misc
        $misc = $filters['misc'] ?? [];
        if (!empty($misc['likvidator'])) {
            $queryBuilder->andWhere(self::ALIAS . '.likvidator = :likvidator')
            ->setParameter('likvidator', $misc['likvidator']);
        }

        if (!empty($misc['stav'])) {
            $queryBuilder->andWhere(self::ALIAS . '.stavLikvidace = :stav')
            ->setParameter('stav', $misc['stav']);
        }

        if (!empty($misc['zadavatel'])) {
            $queryBuilder->andWhere(self::ALIAS . '.zadavatel = :zadavatel')
            ->setParameter('zadavatel', $misc['zadavatel']);
        }

        if (!empty($misc['technik'])) {
            $queryBuilder
                ->join(self::ALIAS . '.dalsiUzivatele', 'du')
                ->andWhere('du.id = :technik')
                ->setParameter('technik', $misc['technik']);
        }

        if ($user) {
            $queryBuilder =  $this->filterListForUserWithRole($user, $queryBuilder);
        }

        return $queryBuilder->getQuery();
    }

    /**
     * 
     * @param string $search 
     * @return Query 
     */
    public function getBySearchQuery(string $search): Query
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS)
            ->leftJoin(self::ALIAS . '.ucastnici', 'ucastnik')
            ->addSelect('ucastnik')
            ->where(
                '(' .
                    self::ALIAS . '.cisloPojistnaUdalost LIKE :search OR ' .
                    self::ALIAS . '.cisloSkodniUdalost LIKE :search OR ' .
                    self::ALIAS . '.mistoPu LIKE :search OR ' .
                    'ucastnik.email LIKE :search OR ' .
                    'ucastnik.telefon LIKE :search OR ' .
                    'ucastnik.prijmeni LIKE :search' .
                ')'
            )
            ->setParameter('search', '%' . $search . '%')
            ->orderBy(self::ALIAS . '.id', 'ASC');

        return $queryBuilder->getQuery();
    }

    /**
     * @param string $pouNumber
     * @return array<string>
     */
    public function getByPouNumberQueryAsArrayForApi(string $pouNumber): array
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS)
            ->select(
                self::ALIAS . '.cisloPojistnaUdalost',
                self::ALIAS . '.datumVznikuSkody',
                self::ALIAS . '.stavLikvidace',
                self::ALIAS . '.kategorie',
            )
            ->where(self::ALIAS . '.cisloPojistnaUdalost = :cisloPojistnaUdalost')
            ->setParameter('cisloPojistnaUdalost', $pouNumber);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function findOneByIdAndUser(int $id, UserInterface $user): ?PojistnaUdalost
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS)
            ->where(self::ALIAS . '.id = :id')
            ->setParameter('id', $id);

        $queryBuilder =  $this->filterListForUserWithRole($user, $queryBuilder);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function getAllCount(): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getAllPrijatoCount(): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.stavLikvidace = :stavLikvidace')
            ->setParameter('stavLikvidace', StavLikvidace::PRIJATO->value)
            ->andWhere(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getPrijatoCountByLikvidator(UserInterface $user): int
    {
        /**
         * @var User $user
         */
        if (!$user->isLikvidator())
            throw new \Exception('User is not likvidator.');

        return $this
            ->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.likvidator = :likvidator')
            ->setParameter('likvidator', $user)
            ->andWhere(self::ALIAS . '.stavLikvidace = :stavLikvidace')
            ->setParameter('stavLikvidace', StavLikvidace::PRIJATO->value)
            ->andWhere(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getAllProbihaCount(): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.stavLikvidace = :stavLikvidace')
            ->setParameter('stavLikvidace', StavLikvidace::PROBIHA->value)
            ->andWhere(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getProbihaCountByLikvidator(UserInterface $user): int
    {
        /**
         * @var User $user
         */
        if (!$user->isLikvidator())
            throw new \Exception('User is not likvidator.');

        return $this
            ->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.likvidator = :likvidator')
            ->setParameter('likvidator', $user)
            ->andWhere(self::ALIAS . '.stavLikvidace = :stavLikvidace')
            ->setParameter('stavLikvidace', StavLikvidace::PROBIHA->value)
            ->andWhere(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getAllUzavrenoCount(): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.stavLikvidace = :stavLikvidace')
            ->setParameter('stavLikvidace', StavLikvidace::UZAVRENO->value)
            ->andWhere(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getTodayPojistnaUdalostCount(): int
    {
        $today = new \DateTime();
        $today->setTime(0, 0, 0);
        $tomorrow = clone $today;
        $tomorrow->modify('+1 day');

        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->where(self::ALIAS . '.datumPrijetiOdPojistovny >= :today')
            ->andWhere(self::ALIAS . '.datumPrijetiOdPojistovny < :tomorrow')
            ->andWhere(self::ALIAS . '.archive = :archive')
            ->setParameter('today', $today)
            ->setParameter('tomorrow', $tomorrow)
            ->setParameter('archive', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    private function filterListForUserWithRole(UserInterface $user, QueryBuilder $queryBuilder): QueryBuilder
    {
        /**
         * @var User $user
         */
        if ($user->isLikvidator()) {
            $queryBuilder->andWhere(self::ALIAS . '.likvidator = :user')
                ->setParameter('user', $user);
            return $queryBuilder;
        }

        $dynamicRole = $user->getDynamicRole();
        if (is_null($dynamicRole)) {
            return $queryBuilder;
        }

        // Include PojistnaUdalost entities where the user is in dalsiUzivatele collection
        $queryBuilder->leftJoin(self::ALIAS . '.dalsiUzivatele', 'dalsiUzivatele')
            ->andWhere('dalsiUzivatele = :dalsiUzivatel')
            ->setParameter('dalsiUzivatel', $user);

        return $queryBuilder;
    }


    public function getWithoutNotesCount(): int
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->leftJoin(self::ALIAS . '.poznamky', 'poznamka')
            ->where('poznamka.id IS NULL')
            ->andWhere(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getWithoutNotesByLikvidatorCount(UserInterface $user): int
    {
        /**
         * @var User $user
         */
        if (!$user->isLikvidator())
            throw new \Exception('User is not likvidator.');

        return $this->createQueryBuilder(self::ALIAS)
            ->select('COUNT(' . self::ALIAS . '.id)')
            ->leftJoin(self::ALIAS . '.poznamky', 'poznamka')
            ->where('poznamka.id IS NULL')
            ->andWhere(self::ALIAS . '.likvidator = :likvidator')
            ->setParameter('likvidator', $user)
            ->andWhere(self::ALIAS . '.archive = :archive')
            ->setParameter('archive', false)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
