<?php

namespace App\Repository;

use App\Entity\Zadavatel;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Zadavatel>
 * 
 * @method Zadavatel|null find($id, $lockMode = null, $lockVersion = null)
 * @method Zadavatel|null findOneBy(array $criteria, array $orderBy = null)
 * @method Zadavatel[]    findAll()
 * @method Zadavatel[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 *
 */
class ZadavatelRepository extends ServiceEntityRepository
{

    public const ALIAS = 'zadavatel';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Zadavatel::class);
    }

    public function findNameById(string $id): ?string
    {
        return $this->find($id)?->getNazevPojistovny();
    }

    public function getAllQuery():Query
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        $queryBuilder = $queryBuilder
            ->orderBy(self::ALIAS.'.kontaktniEmail', 'ASC');
            return $queryBuilder->getQuery();
    }
}
