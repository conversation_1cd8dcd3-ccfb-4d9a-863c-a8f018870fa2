<?php

namespace App\Repository;

use App\Entity\LikvidacniProtokol;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LikvidacniProtokol>
 */
class LikvidacniProtokolRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LikvidacniProtokol::class);
    }

    /**
     * Najde všechny likvidační protokoly pro danou pojistnou událost s řazením podle data
     * 
     * @return LikvidacniProtokol[]
     */
    public function findByPojistnaUdalostOrderedByDate(int $pojistnaUdalostId, string $order = 'DESC'): array
    {
        // Validace order parametru
        $order = strtoupper($order);
        if (!in_array($order, ['ASC', 'DESC'])) {
            $order = 'DESC';
        }

        return $this->createQueryBuilder('l')
            ->andWhere('l.pojistnaUdalost = :pojistnaUdalostId')
            ->setParameter('pojistnaUdalostId', $pojistnaUdalostId)
            ->orderBy('l.createdAt', $order)
            ->getQuery()
            ->getResult();
    }

    //    /**
    //     * @return LikvidacniProtokol[] Returns an array of LikvidacniProtokol objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('l')
    //            ->andWhere('l.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('l.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?LikvidacniProtokol
    //    {
    //        return $this->createQueryBuilder('l')
    //            ->andWhere('l.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
