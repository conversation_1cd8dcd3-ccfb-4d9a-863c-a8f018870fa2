<?php

namespace App\Repository;

use App\Entity\PojistnaUdalost;
use App\Entity\User;
use App\Entity\UserRole;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;

/**
 * @extends ServiceEntityRepository<User>
 *
 * @method User|null find($id, $lockMode = null, $lockVersion = null)
 * @method User|null findOneBy(array $criteria, array $orderBy = null)
 * @method User[]    findAll()
 * @method User[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserRepository extends ServiceEntityRepository implements PasswordUpgraderInterface
{


    public const ALIAS = 'user';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, User::class);
    }

    /**
     * Used to upgrade (rehash) the user's password automatically over time.
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', $user::class));
        }

        $user->setPassword($newHashedPassword);
        $this->getEntityManager()->persist($user);
        $this->getEntityManager()->flush();
    }

    /**
     * @return User[] An array of user objects with a role ROLE_LIKVIDATOR
     */
    public function findLikvidators(): array
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.roles LIKE :role')
            ->setParameter('role', '%'.User::ROLE_LIKVIDATOR.'%')
            ->orderBy('u.surname', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return User[] An array of user objects with a dynamic role named Technik
     */
    public function findTechniks(): array
    {
        return $this->createQueryBuilder('u')
            ->join('u.dynamicRole', 'r')
            ->where('r.name = :roleName')
            ->setParameter('roleName', 'Technik')
            ->orderBy('u.surname', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return User[] An array of user objects with a role ROLE_LIKVIDATOR
     */
    public function findLikvidatorsIncludingAdmins()
    {
        $likvidators = $this->findLikvidators();

        $new = $this->createQueryBuilder('u')
            ->join(PojistnaUdalost::class, 'pou', Join::WITH, 'pou.likvidator = u.id')
            ->orderBy('u.surname', 'ASC')
            ->getQuery()
            ->getResult();

        foreach ($new as $likvidator) {
            if (in_array($likvidator, $likvidators)) continue;
            $likvidators[] = $likvidator;
        }

        usort($likvidators, function($a, $b) {
            return strcmp($a->getSurname(), $b->getSurname());
        });

        return $likvidators;
    }
    
    private function getAllButUWAdminQuery(): \Doctrine\ORM\QueryBuilder
    {
        $queryBuilder = $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        return $queryBuilder
            ->andWhere($queryBuilder->expr()->notLike(self::ALIAS.'.roles', ':role'))
            ->setParameter('role', '%'.User::ROLE_UW_ADMIN.'%');
    }


    public function findAllButUWAdminQuery():Query
    {
        $queryBuilder = $this->getAllButUWAdminQuery();
        $queryBuilder->orderBy(self::ALIAS.'.email', 'ASC');

        return $queryBuilder->getQuery();

    }

    public function searchByString(?string $query): Query
    {
        $queryBuilder = $this->getAllButUWAdminQuery();
        if ($query) {
            $queryBuilder->andWhere(self::ALIAS.'.surname LIKE :query OR '.self::ALIAS.'.email LIKE :query')
                ->setParameter('query', '%' . $query .'%');
        }
        $queryBuilder->orderBy(self::ALIAS.'.email', 'ASC');

        return $queryBuilder->getQuery();
    }

    public function findNameById(string $id): ?string
    {
        $user = $this->find($id);

        if ($user)
            return $user->getName() . ' ' . $user->getSurname();
        
        return null;
    }
    
    /**
     * Checks if there are any users with the specified UserRole
     * 
     * @param UserRole $userRole The role to check
     * @return bool True if users with this role exist, false otherwise
     */
    public function hasUsersWithRole(UserRole $userRole): bool
    {
        $count = $this->createQueryBuilder('u')
            ->select('COUNT(u.id)')
            ->andWhere('u.dynamicRole = :role')
            ->setParameter('role', $userRole)
            ->getQuery()
            ->getSingleScalarResult();
            
        return $count > 0;
    }
}
