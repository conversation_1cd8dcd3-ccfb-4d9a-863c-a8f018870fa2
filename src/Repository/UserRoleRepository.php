<?php

namespace App\Repository;

use App\Entity\UserRole;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<UserRole>
 */
class UserRoleRepository extends ServiceEntityRepository
{

    public const ALIAS = 'userrole';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserRole::class);
    }


    /**
     * 
     * @return array<mixed>
     */
    public function getAllActiveUserRoles(): array
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        $queryBuilder
            ->andWhere(self::ALIAS . '.active = :active')
            ->setParameter('active', true)
            ->orderBy(self::ALIAS . '.name', 'ASC')
        ;
        return $queryBuilder->getQuery()->getResult();
    }

    public function findRoleByCode(string $code): ?UserRole
    {
        return $this->createQueryBuilder(self::ALIAS)
            ->andWhere(self::ALIAS . '.code = :val')
            ->setParameter('val', $code)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
