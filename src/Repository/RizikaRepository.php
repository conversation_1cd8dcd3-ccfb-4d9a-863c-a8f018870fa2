<?php

namespace App\Repository;

use App\Entity\Rizika;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Rizika>
 */
class RizikaRepository extends ServiceEntityRepository
{

    public const ALIAS = 'rizikopu';

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Rizika::class);
    }

    public function getAllQuery():Query
    {
        $queryBuilder = $this->createQueryBuilder(self::ALIAS);
        $queryBuilder = $queryBuilder
            ->orderBy(self::ALIAS.'.id', 'ASC');
            return $queryBuilder->getQuery();
    }
}