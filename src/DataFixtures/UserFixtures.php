<?php

namespace App\DataFixtures;

use App\Entity\User;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class UserFixtures extends AbstractBaseFixture
{
    /**
     * Generuje telefonní číslo ve formátu s mezerami (např. 602 123 456)
     */
    private function generatePhoneNumber(): string
    {
        $prefix = $this->faker->randomElement(['601', '602', '603', '604', '605', '606', '607', '608', '609', '720', '721', '722', '723', '724', '725', '726', '727', '728', '729', '730', '731', '732', '733', '734', '735', '736', '737', '738', '739', '770', '771', '772', '773', '774', '775', '776', '777', '778', '779']);
        $middle = $this->faker->numberBetween(100, 999);
        $last = $this->faker->numberBetween(100, 999);
        
        return sprintf('%s %d %d', $prefix, $middle, $last);
    }
    private UserPasswordHasherInterface $hasher;

    private const ADMIN_EMAILS = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];

    private const LIKVIDATOR_EMAILS = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];

    public function __construct(UserPasswordHasherInterface $hasher)
    {
        $this->hasher = $hasher;
    }

    protected function loadData(ObjectManager $manager): void
    {
        $this->createAdmins($manager);
        $this->createLikvidators($manager);
        
        $manager->flush();
    }

    private function createAdmins(ObjectManager $manager): void
    {
        // Create 3 admin users
        for ($i = 0; $i < 3; $i++) {
            $user = new User();
            $user->setEmail(self::ADMIN_EMAILS[$i]);
            $user->setName($this->faker->firstName());
            $user->setSurname($this->faker->lastName());
            $user->setTelephone($this->generatePhoneNumber());
            $user->setRoles(['ROLE_COM_ADMIN']);
            $password = $this->hasher->hashPassword($user, 'tajneHeslo123456');
            $user->setPassword($password);
            
            $manager->persist($user);
            $this->addReference('admin_' . $i, $user);
        }
    }

    private function createLikvidators(ObjectManager $manager): void
    {
        // Create 3 likvidator users
        for ($i = 0; $i < 3; $i++) {
            $user = new User();
            $user->setEmail(self::LIKVIDATOR_EMAILS[$i]);
            $user->setName($this->faker->firstName());
            $user->setSurname($this->faker->lastName());
            $user->setTelephone($this->generatePhoneNumber());
            $user->setRoles(['ROLE_LIKVIDATOR']);
            $password = $this->hasher->hashPassword($user, 'tajneHeslo123456');
            $user->setPassword($password);
            
            $manager->persist($user);
            $this->addReference('likvidator_' . $i, $user);
        }
    }
}
