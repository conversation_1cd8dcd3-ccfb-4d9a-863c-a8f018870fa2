<?php

namespace App\DataFixtures\Factory;

use App\Entity\PojistnaUdalost;
use App\Entity\Ucastnik;
use App\Entity\Vozidlo;
use App\Enum\RoleUcastnika;
use Doctrine\Persistence\ObjectManager;
use Faker\Generator;

class UcastnikFactory
{
    private Generator $faker;

    public function __construct(Generator $faker)
    {
        $this->faker = $faker;
    }
    
    /**
     * Generuje telefonní číslo ve formátu s mezerami (např. 602 123 456)
     */
    private function generatePhoneNumber(): string
    {
        $prefix = $this->faker->randomElement(['601', '602', '603', '604', '605', '606', '607', '608', '609', '720', '721', '722', '723', '724', '725', '726', '727', '728', '729', '730', '731', '732', '733', '734', '735', '736', '737', '738', '739', '770', '771', '772', '773', '774', '775', '776', '777', '778', '779']);
        $middle = $this->faker->numberBetween(100, 999);
        $last = $this->faker->numberBetween(100, 999);
        
        return sprintf('%s %d %d', $prefix, $middle, $last);
    }

    public function create(
        PojistnaUdalost $pojistnaUdalost,
        RoleUcastnika $role,
        bool $platceDPH = false
    ): Ucastnik {
        // Generujeme hodnotu pro firmu
        $firma = $this->faker->boolean(30) ? $this->faker->company() : null;
        
        // Nastavíme company na true, pokud je vyplněna firma, jinak na false
        $company = $firma !== null;

        $rodneCislo = $this->generateRodneCislo();
        if ($role == RoleUcastnika::ROLE_KONTAKTNI_OSOBA || $role == RoleUcastnika::ROLE_POVERENA_OSOBA)
        {
            $rodneCislo = null;
        }

        
        $ucastnik = new Ucastnik(
            $pojistnaUdalost,
            $role->value,
            $this->faker->firstName(),
            $this->faker->lastName(),
            $this->faker->boolean(30) ? $this->faker->address() : null,
            $rodneCislo,
            $this->generatePhoneNumber(),
            $this->faker->email(),
            $this->generateCisloUctu(),
            $this->faker->boolean(70) ? $this->faker->sentence() : null,
            $this->faker->company(),
            $this->generateCisloSmlouvy(),
            $firma,
            $this->faker->boolean(30) ? $this->faker->numerify('########') : null,
            $this->faker->boolean(30) ? $this->faker->address() : null,
            $company,
            $platceDPH
        );

        return $ucastnik;
    }

    public function createVinik(PojistnaUdalost $pojistnaUdalost, bool $platceDPH = false): Ucastnik
    {
        return $this->create($pojistnaUdalost, RoleUcastnika::ROLE_VINIK, $platceDPH);
    }

    public function createPoskozeny(PojistnaUdalost $pojistnaUdalost, bool $platceDPH = false): Ucastnik
    {
        return $this->create($pojistnaUdalost, RoleUcastnika::ROLE_POSKOZENY, $platceDPH);
    }

    public function createPoverenaOsoba(PojistnaUdalost $pojistnaUdalost): Ucastnik
    {
        return $this->create($pojistnaUdalost, RoleUcastnika::ROLE_POVERENA_OSOBA, false);
    }
    
    public function createKontaktniOsoba(PojistnaUdalost $pojistnaUdalost): Ucastnik
    {
        return $this->create($pojistnaUdalost, RoleUcastnika::ROLE_KONTAKTNI_OSOBA, false);
    }
    
    public function createPojisteny(PojistnaUdalost $pojistnaUdalost, bool $platceDPH = false): Ucastnik
    {
        return $this->create($pojistnaUdalost, RoleUcastnika::ROLE_POJISTENY, $platceDPH);
    }

    public function createVozidla(ObjectManager $manager, Ucastnik $ucastnik, int $count = 1): void
    {
        $druhyVozidel = ['osobni', 'nakladni', 'prives'];
        $znackyVozidel = ['Audi', 'BMW', 'VW', 'ŠKODA', 'Honda', 'Toyota', 'Mercedes', 'Ford', 'Hyundai', 'Kia'];
        
        for ($i = 0; $i < $count; $i++) {
            $vozidlo = new Vozidlo(
                $ucastnik,
                $this->generateSPZ(),
                $this->generateVIN(),
                $this->faker->numberBetween(50, 300) . ' kW', // Výkon
                $this->faker->numberBetween(1000, 3000) . ' ccm', // Obsah
                $druhyVozidel[$this->faker->numberBetween(0, count($druhyVozidel) - 1)],
                $znackyVozidel[$this->faker->numberBetween(0, count($znackyVozidel) - 1)]
            );
            
            $manager->persist($vozidlo);
        }
    }

    private function generateRodneCislo(): string
    {
        // Format: YYMMDD/XXXX
        $year = $this->faker->numberBetween(50, 99);
        $month = $this->faker->numberBetween(1, 12);
        $day = $this->faker->numberBetween(1, 28);
        $suffix = $this->faker->numberBetween(1000, 9999);
        
        return sprintf('%02d%02d%02d/%04d', $year, $month, $day, $suffix);
    }

    private function generateCisloUctu(): string
    {
        // Format: XXXXXXXXXX/YYYY
        $cislo = $this->faker->numerify('##########');
        $kod = $this->faker->randomElement(['0100', '0300', '0600', '0800', '2010', '2700']);
        
        return $cislo . '/' . $kod;
    }

    private function generateCisloSmlouvy(): string
    {
        return 'SML' . $this->faker->numerify('######');
    }

    private function generateSPZ(): string
    {
        // Format: 1A2 3456
        $region = $this->faker->randomElement(['A', 'B', 'C', 'E', 'H', 'J', 'K', 'L', 'M', 'P', 'S', 'T', 'U', 'Z']);
        return $this->faker->numberBetween(1, 9) . $region . $this->faker->numberBetween(1, 9) . ' ' . $this->faker->numerify('####');
    }

    private function generateVIN(): string
    {
        // Format: 17 alphanumeric characters
        return $this->faker->regexify('[A-Z0-9]{17}');
    }
}
