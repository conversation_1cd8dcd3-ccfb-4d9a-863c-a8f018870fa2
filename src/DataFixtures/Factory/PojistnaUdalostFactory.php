<?php

namespace App\DataFixtures\Factory;

use App\Entity\PojistnaUdalost;
use App\Entity\Zadavatel;
use App\Enum\StavLikvidace;
use App\Factory\PojistnaUdalost\PojistnaUdalostSlozkaFactory;
use Faker\Generator;

class PojistnaUdalostFactory
{
    private Generator $faker;
    private PojistnaUdalostSlozkaFactory $pojistnaUdalostSlozkaFactory;

    public function __construct(Generator $faker, PojistnaUdalostSlozkaFactory $pojistnaUdalostSlozkaFactory)
    {
        $this->faker = $faker;
        $this->pojistnaUdalostSlozkaFactory = $pojistnaUdalostSlozkaFactory;
    }

    public function create(
        Zadavatel $zadavatel,
        string $category,
        string $stavLikvidace,
        string $prohlidkaTyp,
        ?\DateTimeInterface $datumUkonceniLikvidace = null
    ): PojistnaUdalost {
        $datumPrijeti = new \DateTime();
        $datumPrijeti->modify('-' . $this->faker->numberBetween(1, 30) . ' days');
        
        $datumVzniku = clone $datumPrijeti;
        $datumVzniku->modify('-' . $this->faker->numberBetween(1, 5) . ' days');
        
        return new PojistnaUdalost(
            $zadavatel,
            $this->generateCisloPojistneUdalosti($category),
            $this->generateCisloSkodniUdalosti(),
            $category,
            $stavLikvidace,
            $prohlidkaTyp,
            $this->faker->city(),
            $datumVzniku,
            $datumUkonceniLikvidace,
            $this->faker->numberBetween(10000, 1000000) . ' Kč',
            $this->faker->paragraph(),
            $this->faker->city(),
            new \DateTimeImmutable(),
            $this->faker->paragraph(),
            $this->faker->paragraph(),
            $this->faker->numerify('######')
        );
    }

    public function createWithTodayDate(
        Zadavatel $zadavatel,
        string $category = PojistnaUdalost::CATEGORY_HAV,
        string $stavLikvidace = 'prijato' // StavLikvidace::PRIJATO->value
    ): PojistnaUdalost {
        $dnesniDatum = new \DateTime();
        
        return new PojistnaUdalost(
            $zadavatel,
            'DNES-' . \date('Ymd') . '-' . $this->faker->randomNumber(2),
            'SKOD-' . \date('Ymd') . '-' . $this->faker->randomNumber(2),
            $category,
            $stavLikvidace,
            PojistnaUdalost::PROHLIDKA_TYP_NE,
            $this->faker->city(),
            $dnesniDatum,
            $dnesniDatum,
            $this->faker->numberBetween(10000, 1000000) . ' Kč',
            '',
            $this->faker->city(),
            new \DateTimeImmutable(),
            '',
            '',
            $this->faker->numerify('######')
        );
    }

    public function createUnprocessed(
        Zadavatel $zadavatel,
        int $daysAgo = 0
    ): PojistnaUdalost {
        $datum = new \DateTime();
        if ($daysAgo > 0) {
            $datum->modify('-' . $daysAgo . ' days');
        }
        
        return new PojistnaUdalost(
            $zadavatel,
            'NEZPRAC-' . \date('Ymd') . '-' . $this->faker->randomNumber(2),
            'SKOD-NEZPRAC-' . \date('Ymd') . '-' . $this->faker->randomNumber(2),
            PojistnaUdalost::CATEGORY_HAV,
            StavLikvidace::PRIJATO->value,
            PojistnaUdalost::PROHLIDKA_TYP_NE,
            $this->faker->city(),
            $datum,
            $datum,
            $this->faker->numberBetween(10000, 1000000) . ' Kč',
            '',
            $this->faker->city(),
            new \DateTimeImmutable(),
            '',
            '',
            $this->faker->numerify('######')
        );
    }

    public function createFolders(\Doctrine\Persistence\ObjectManager $manager, PojistnaUdalost $pojistnaUdalost): void
    {
        $rootFolder = $this->pojistnaUdalostSlozkaFactory->createRootSlozkaForPojistnaUdalost($pojistnaUdalost);
        $pojistovnaZadaniSlozka = $this->pojistnaUdalostSlozkaFactory->createPojistovnaZadaniSlozkaForPojistnaUdalost($pojistnaUdalost, $rootFolder);
        $pojistovnaUkonceniSlozka = $this->pojistnaUdalostSlozkaFactory->createPojistovnaUkonceniSlozkaForPojistnaUdalost($pojistnaUdalost, $rootFolder);
        $klientSlozka = $this->pojistnaUdalostSlozkaFactory->createKlientSlozkaForPojistnaUdalost($pojistnaUdalost, $rootFolder);
        $techSlozka = $this->pojistnaUdalostSlozkaFactory->createTechnikSlozkaForPojistnaUdalost($pojistnaUdalost, $rootFolder);
        
        $manager->persist($rootFolder);
        $manager->persist($pojistovnaZadaniSlozka);
        $manager->persist($pojistovnaUkonceniSlozka);
        $manager->persist($klientSlozka);
        $manager->persist($techSlozka);
    }

    private function generateCisloPojistneUdalosti(string $category): string
    {
        return $category . '-' . $this->faker->numerify('######');
    }

    private function generateCisloSkodniUdalosti(): string
    {
        return 'SKOD-' . $this->faker->numerify('######');
    }

}
