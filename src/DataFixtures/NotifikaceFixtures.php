<?php

namespace App\DataFixtures;

use App\Entity\Notifikace;
use App\Entity\PojistnaUdalost;
use App\Entity\User;
use App\Enum\StavLikvidace;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class NotifikaceFixtures extends AbstractBaseFixture implements DependentFixtureInterface
{
    protected function loadData(ObjectManager $manager): void
    {
        $this->createLikvidatorAssignmentNotifications($manager);
        $this->createLikvidatorRevocationNotifications($manager);
        
        $manager->flush();
    }

    /**
     * Vytvoří notifikace o přiřazení likvidátora
     */
    private function createLikvidatorAssignmentNotifications(ObjectManager $manager): void
    {
        // Vytvoříme notifikace pro každého likvidátora
        for ($i = 0; $i < 3; $i++) {
            /** @var User $likvidator */
            $likvidator = $this->getReference('likvidator_' . $i, User::class);
            
            // Vytvoříme několik notifikací pro každého likvidátora
            $numNotifications = $this->faker->numberBetween(2, 5);
            for ($j = 0; $j < $numNotifications; $j++) {
                // Náhodně vybereme pojistnou událost ve stavu PROBIHA nebo UZAVRENO
                $categories = [
                    PojistnaUdalost::CATEGORY_HAV,
                    PojistnaUdalost::CATEGORY_POV,
                    PojistnaUdalost::CATEGORY_MAJ,
                    PojistnaUdalost::CATEGORY_ODP
                ];
                $category = $this->faker->randomElement($categories);
                
                $stavy = [
                    StavLikvidace::PROBIHA->value,
                    StavLikvidace::UZAVRENO->value
                ];
                $stav = $this->faker->randomElement($stavy);
                
                $refName = 'pojistna_udalost_' . $category . '_' . $stav . '_' . $this->faker->numberBetween(0, 1);
                
                if ($this->hasReference($refName, PojistnaUdalost::class)) {
                    /** @var PojistnaUdalost $pojistnaUdalost */
                    $pojistnaUdalost = $this->getReference($refName, PojistnaUdalost::class);
                    
                    $titulek = 'Přiřazení k pojistné události ' . $pojistnaUdalost->getCisloPojistnaUdalost();
                    $obsah = 'Byli jste přiřazeni jako likvidátor k pojistné události ' . 
                             $pojistnaUdalost->getCisloPojistnaUdalost() . ' (' . 
                             $pojistnaUdalost->getkategorieFullString() . ').';
                    
                    $notifikace = new Notifikace(
                        $titulek,
                        $obsah,
                        Notifikace::TYP_LIKVIDATOR_ASSIGNMENT,
                        $likvidator,
                        $this->faker->boolean(30) // 30% šance na odeslání emailu
                    );
                    
                    // 70% šance, že notifikace bude přečtená
                    if ($this->faker->boolean(70)) {
                        $notifikace->togglePrecteno();
                    }
                    
                    $manager->persist($notifikace);
                }
            }
        }
    }

    /**
     * Vytvoří notifikace o odebrání likvidátora
     */
    private function createLikvidatorRevocationNotifications(ObjectManager $manager): void
    {
        // Vytvoříme notifikace pro každého likvidátora
        for ($i = 0; $i < 3; $i++) {
            /** @var User $likvidator */
            $likvidator = $this->getReference('likvidator_' . $i, User::class);
            
            // Vytvoříme několik notifikací pro každého likvidátora
            $numNotifications = $this->faker->numberBetween(1, 3);
            for ($j = 0; $j < $numNotifications; $j++) {
                // Náhodně vybereme pojistnou událost ve stavu PRIJATO nebo CEKAME
                $categories = [
                    PojistnaUdalost::CATEGORY_HAV,
                    PojistnaUdalost::CATEGORY_POV,
                    PojistnaUdalost::CATEGORY_MAJ,
                    PojistnaUdalost::CATEGORY_ODP
                ];
                $category = $this->faker->randomElement($categories);
                
                $stavy = [
                    StavLikvidace::PRIJATO->value
                ];
                $stav = $this->faker->randomElement($stavy);
                
                $refName = 'pojistna_udalost_' . $category . '_' . $stav . '_' . $this->faker->numberBetween(0, 1);
                
                if ($this->hasReference($refName, PojistnaUdalost::class)) {
                    /** @var PojistnaUdalost $pojistnaUdalost */
                    $pojistnaUdalost = $this->getReference($refName, PojistnaUdalost::class);
                    
                    $titulek = 'Odebrání z pojistné události ' . $pojistnaUdalost->getCisloPojistnaUdalost();
                    $obsah = 'Byli jste odebráni jako likvidátor z pojistné události ' . 
                             $pojistnaUdalost->getCisloPojistnaUdalost() . ' (' . 
                             $pojistnaUdalost->getkategorieFullString() . ').';
                    
                    $notifikace = new Notifikace(
                        $titulek,
                        $obsah,
                        Notifikace::TYP_LIKVIDATOR_REVOCATION,
                        $likvidator,
                        $this->faker->boolean(30) // 30% šance na odeslání emailu
                    );
                    
                    // 50% šance, že notifikace bude přečtená
                    if ($this->faker->boolean(50)) {
                        $notifikace->togglePrecteno();
                    }
                    
                    $manager->persist($notifikace);
                }
            }
        }
    }

    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
            PojistnaUdalostFixtures::class
        ];
    }
}
