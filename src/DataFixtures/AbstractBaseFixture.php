<?php

namespace App\DataFixtures;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Faker\Factory;
use Faker\Generator;

abstract class AbstractBaseFixture extends Fixture
{
    /**
     * @var ObjectManager
     */
    protected $manager;

    /**
     * @var Generator
     */
    protected $faker;

    /**
     * Load data fixtures with the passed EntityManager
     */
    public function load(ObjectManager $manager): void
    {
        $this->manager = $manager;
        $this->faker = Factory::create();
        $this->faker->seed(1234); // For consistent data

        $this->loadData($manager);
        
        // Ensure all entities are flushed to the database
        $manager->flush();
    }

    /**
     * Load data fixtures
     */
    abstract protected function loadData(ObjectManager $manager): void;
}
