<?php

namespace App\DataFixtures;

use App\Entity\PojistnePodminky;
use Doctrine\Persistence\ObjectManager;

class PojistnePodminkyFixtures extends AbstractBaseFixture
{
    protected function loadData(ObjectManager $manager): void
    {
        $pojistovny = [
            'Allianz',
            'Česká pojišťovna',
            'ČSOB',
            'Kooperativa',
            'Generali',
            'UNIQA',
            'Direct',
            'AXA',
            'Slavia',
            'VZP'
        ];

        $typyPodminek = [
            'VPP',
            'DPP',
            'ZPP'
        ];

        $roky = range(2015, 2025);

        // Create 15 pojistne podminky entities
        for ($i = 0; $i < 15; $i++) {
            $pojistovna = $pojistovny[$this->faker->numberBetween(0, count($pojistovny) - 1)];
            $typPodminek = $typyPodminek[$this->faker->numberBetween(0, count($typyPodminek) - 1)];
            $rokOd = $this->faker->randomElement($roky);
            $rokDo = $this->faker->numberBetween($rokOd, 2025);
            
            $nazev = sprintf(
                '%s - %s Platné od %d do %d',
                $pojistovna,
                $typPodminek,
                $rokOd,
                $rokDo
            );
            
            // 70% chance to have a URL
            $url = $this->faker->boolean(70) 
                ? $this->faker->url() 
                : null;

            $pojistnePodminky = new PojistnePodminky($nazev, $url);
            
            $manager->persist($pojistnePodminky);
            $this->addReference('pojistne_podminky_' . $i, $pojistnePodminky);
        }

        $manager->flush();
    }
}
