<?php

namespace App\DataFixtures;

use App\DataFixtures\Factory\PojistnaUdalostFactory;
use App\DataFixtures\Factory\UcastnikFactory;
use App\Dto\PojistnaUdalost\PojistnaUdalostLikvidatorEditInput;
use App\Entity\PojistnaUdalost;
use App\Entity\Ucastnik;
use App\Entity\User;
use App\Entity\Zadavatel;
use App\Enum\RoleUcastnika;
use App\Enum\StavLikvidace;
use App\Factory\PojistnaUdalost\PojistnaUdalostSlozkaFactory;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class PojistnaUdalostFixtures extends AbstractBaseFixture implements DependentFixtureInterface
{
    private PojistnaUdalostFactory $pojistnaUdalostFactory;
    private UcastnikFactory $ucastnikFactory;
    private PojistnaUdalostSlozkaFactory $pojistnaUdalostSlozkaFactory;

    public function __construct(
        PojistnaUdalostFactory $pojistnaUdalostFactory, 
        UcastnikFactory $ucastnikFactory,
        PojistnaUdalostSlozkaFactory $pojistnaUdalostSlozkaFactory
    ) {
        $this->pojistnaUdalostFactory = $pojistnaUdalostFactory;
        $this->ucastnikFactory = $ucastnikFactory;
        $this->pojistnaUdalostSlozkaFactory = $pojistnaUdalostSlozkaFactory;
    }

    protected function loadData(ObjectManager $manager): void
    {
        $this->createStandardPojistneUdalosti($manager);
        $this->createTodayPojistnaUdalostFixtures($manager);
        $this->createUnprocessedPojistnaUdalostFixtures($manager);
    }
    
    /**
     * Vytvoří standardní pojistné události pro všechny kategorie a stavy
     */
    private function createStandardPojistneUdalosti(ObjectManager $manager): void
    {
        $categories = [
            PojistnaUdalost::CATEGORY_HAV,
            PojistnaUdalost::CATEGORY_POV,
            PojistnaUdalost::CATEGORY_MAJ,
            PojistnaUdalost::CATEGORY_ODP
        ];
        
        $likvidaceStavy = [
            StavLikvidace::PRIJATO->value,
            StavLikvidace::PROBIHA->value,
            StavLikvidace::UZAVRENO->value
        ];
        
        $prohlidkaTypy = [
            PojistnaUdalost::PROHLIDKA_TYP_KLIENT,
            PojistnaUdalost::PROHLIDKA_TYP_NE,
            PojistnaUdalost::PROHLIDKA_TYP_TECHNIK
        ];
        
        // Pro každou kategorii vytvoříme události ve všech stavech
        foreach ($categories as $categoryIndex => $category) {
            foreach ($likvidaceStavy as $stavIndex => $stav) {
                // Vytvoříme několik událostí pro každou kombinaci kategorie a stavu
                for ($i = 0; $i < 2; $i++) {
                    // Use a deterministic index instead of random for more predictable entity loading
                    $zadavatelIndex = ($categoryIndex + $stavIndex + $i) % 10;
                    $zadavatel = $this->getReference('zadavatel_' . $zadavatelIndex, Zadavatel::class);
                    
                    // Ensure the Zadavatel is attached to the EntityManager
                    $manager->persist($zadavatel);
                    
                    $prohlidkaTyp = $prohlidkaTypy[$this->faker->numberBetween(0, count($prohlidkaTypy) - 1)];
                    
                    // Datum ukončení likvidace pouze pro uzavřené události
                    $datumUkonceniLikvidace = null;
                    if ($stav === StavLikvidace::UZAVRENO->value) {
                        $datumUkonceniLikvidace = new \DateTime();
                        $datumUkonceniLikvidace->modify('-' . $this->faker->numberBetween(1, 10) . ' days');
                    }
                    
                    // Vytvoření pojistné události
                    $pojistnaUdalost = $this->pojistnaUdalostFactory->create(
                        $zadavatel,
                        $category,
                        $stav,
                        $prohlidkaTyp,
                        $datumUkonceniLikvidace
                    );
                    
                    // Přiřazení likvidátora (pouze pro některé stavy)
                    if ($stav !== StavLikvidace::PRIJATO->value) {
                        $likvidatorInput = new PojistnaUdalostLikvidatorEditInput();
                        $likvidatorInput->likvidator = $this->getReference('likvidator_' . $this->faker->numberBetween(0, 2), User::class);
                        $pojistnaUdalost->modifyLikvidator($likvidatorInput);
                    }
                    
                    $manager->persist($pojistnaUdalost);
                    
                    // Vytvoření složek pro pojistnou událost
                    $this->createFolders($manager, $pojistnaUdalost);
                    
                    // Vytvoření účastníků pro pojistnou událost
                    $this->createParticipants($manager, $pojistnaUdalost, $category);
                    
                    // Reference pro další fixtures
                    $this->addReference(
                        'pojistna_udalost_' . $category . '_' . $stav . '_' . $i,
                        $pojistnaUdalost
                    );
                }
            }
        }
    }

    /**
     * Vytvoří složky pro pojistnou událost
     */
    private function createFolders(ObjectManager $manager, PojistnaUdalost $pojistnaUdalost): void
    {
        $rootFolder = $this->pojistnaUdalostSlozkaFactory->createRootSlozkaForPojistnaUdalost($pojistnaUdalost);
        $pojistovnaZadaniSlozka = $this->pojistnaUdalostSlozkaFactory->createPojistovnaZadaniSlozkaForPojistnaUdalost($pojistnaUdalost, $rootFolder);
        $pojistovnaUkonceniSlozka = $this->pojistnaUdalostSlozkaFactory->createPojistovnaUkonceniSlozkaForPojistnaUdalost($pojistnaUdalost, $rootFolder);
        $klientSlozka = $this->pojistnaUdalostSlozkaFactory->createKlientSlozkaForPojistnaUdalost($pojistnaUdalost, $rootFolder);
        $techSlozka = $this->pojistnaUdalostSlozkaFactory->createTechnikSlozkaForPojistnaUdalost($pojistnaUdalost, $rootFolder);
        $ukolySlozka = $this->pojistnaUdalostSlozkaFactory->createUkolySlozkaForPojistnaUdalost($pojistnaUdalost, $rootFolder);
        
        $manager->persist($rootFolder);
        $manager->persist($pojistovnaZadaniSlozka);
        $manager->persist($pojistovnaUkonceniSlozka);
        $manager->persist($klientSlozka);
        $manager->persist($techSlozka);
        $manager->persist($ukolySlozka);
    }

    /**
     * Vytvoří účastníky pro pojistnou událost
     */
    private function createParticipants(ObjectManager $manager, PojistnaUdalost $pojistnaUdalost, string $category): void
    {
        // 1. Vždy vytvoříme právě jednoho pojištěného účastníka s 70% šancí, že bude plátce DPH
        $pojisteny = $this->ucastnikFactory->createPojisteny($pojistnaUdalost, $this->faker->boolean(70));
        $manager->persist($pojisteny);
        
        // Pro kategorie HAV a POV přidáme vozidla
        if ($category === PojistnaUdalost::CATEGORY_HAV || $category === PojistnaUdalost::CATEGORY_POV) {
            $this->ucastnikFactory->createVozidla($manager, $pojisteny, $this->faker->numberBetween(1, 2));
        }
        
        // 2. Vždy vytvoříme alespoň jednoho poškozeného
        $poskozeny = $this->ucastnikFactory->createPoskozeny($pojistnaUdalost, $this->faker->boolean(50));
        $manager->persist($poskozeny);
        
        // Pro kategorie HAV a POV přidáme vozidla
        if ($category === PojistnaUdalost::CATEGORY_HAV || $category === PojistnaUdalost::CATEGORY_POV) {
            $this->ucastnikFactory->createVozidla($manager, $poskozeny, $this->faker->numberBetween(1, 2));
        }
        
        // 3. Přidáme další poškozené (0-2)
        $numPoskozeni = $this->faker->numberBetween(0, 2);
        for ($i = 0; $i < $numPoskozeni; $i++) {
            $dalsiPoskozeny = $this->ucastnikFactory->createPoskozeny($pojistnaUdalost, $this->faker->boolean(50));
            $manager->persist($dalsiPoskozeny);
            
            // Pro kategorie HAV a POV přidáme vozidla
            if ($category === PojistnaUdalost::CATEGORY_HAV || $category === PojistnaUdalost::CATEGORY_POV) {
                $this->ucastnikFactory->createVozidla($manager, $dalsiPoskozeny, $this->faker->numberBetween(1, 2));
            }
        }
        
        // 4. Vždy musí být alespoň jeden viník s 50% šancí, že bude plátce DPH
        $vinik = $this->ucastnikFactory->createVinik($pojistnaUdalost, $this->faker->boolean(50));
        $manager->persist($vinik);
        
        // Pro kategorie HAV a POV přidáme vozidla
        if ($category === PojistnaUdalost::CATEGORY_HAV || $category === PojistnaUdalost::CATEGORY_POV) {
            $this->ucastnikFactory->createVozidla($manager, $vinik, $this->faker->numberBetween(1, 2));
        }
        
        // Přidáme pověřenou osobu (50% šance)
        if ($this->faker->boolean(50)) {
            // 30% šance, že to bude firma
            if ($this->faker->boolean(30)) {
                // Vytvoříme firmu jako pověřenou osobu
                $firma = $this->faker->company();
                $ico = $this->faker->numerify('########');
                $sidlo = $this->faker->address();
                $platceDPH = $this->faker->boolean(70);
                
                $poverenaOsoba = new Ucastnik(
                    $pojistnaUdalost,
                    RoleUcastnika::ROLE_POVERENA_OSOBA->value,
                    null, // jmeno
                    null, // prijmeni
                    null, // adresa
                    null, // rodneCislo
                    $this->faker->phoneNumber(), // telefon
                    $this->faker->email(), // email
                    $this->faker->numerify('##########') . '/' . $this->faker->randomElement(['0100', '0300', '0600', '0800']), // cisloUctu
                    $this->faker->sentence(), // poznamka
                    null, // pojistovna
                    null, // cisloSmlouvy
                    $firma, // firma
                    $ico, // ico
                    $sidlo, // sidlo
                    true, // company - je to firma, takže nastavíme na true
                    $platceDPH // platceDPH
                );
            } else {
                // Vytvoříme fyzickou osobu jako pověřenou osobu
                $poverenaOsoba = $this->ucastnikFactory->createPoverenaOsoba($pojistnaUdalost);
            }
            
            $manager->persist($poverenaOsoba);
        }
        
        // Přidáme kontaktní osoby (0-3)
        $numKontaktniOsoby = $this->faker->numberBetween(0, 3);
        for ($i = 0; $i < $numKontaktniOsoby; $i++) {
            // 30% šance, že to bude firma
            if ($this->faker->boolean(30)) {
                // Vytvoříme firmu jako kontaktní osobu
                $firma = $this->faker->company();
                $ico = $this->faker->numerify('########');
                $sidlo = $this->faker->address();
                $platceDPH = $this->faker->boolean(70);
                
                $kontaktniOsoba = new Ucastnik(
                    $pojistnaUdalost,
                    RoleUcastnika::ROLE_KONTAKTNI_OSOBA->value,
                    null, // jmeno
                    null, // prijmeni
                    null, // adresa
                    null, // rodneCislo
                    $this->faker->phoneNumber(), // telefon
                    $this->faker->email(), // email
                    $this->faker->numerify('##########') . '/' . $this->faker->randomElement(['0100', '0300', '0600', '0800']), // cisloUctu
                    $this->faker->sentence(), // poznamka
                    null, // pojistovna
                    null, // cisloSmlouvy
                    $firma, // firma
                    $ico, // ico
                    $sidlo, // sidlo
                    true, // company - je to firma, takže nastavíme na true
                    $platceDPH // platceDPH
                );
            } else {
                // Vytvoříme fyzickou osobu jako kontaktní osobu
                $kontaktniOsoba = $this->ucastnikFactory->createKontaktniOsoba($pojistnaUdalost);
            }
            
            $manager->persist($kontaktniOsoba);
        }
    }
    
    /**
     * Vytvoří pojistné události s dnešním datem
     */
    private function createTodayPojistnaUdalostFixtures(ObjectManager $manager): void
    {
        // Vytvoříme 5 pojistných událostí s dnešním datem
        for ($i = 0; $i < 5; $i++) {
            // Use deterministic index based on the loop
            $zadavatelIndex = $i % 10;
            $zadavatel = $this->getReference('zadavatel_' . $zadavatelIndex, Zadavatel::class);
            // Ensure the Zadavatel is attached to the EntityManager
            $manager->persist($zadavatel);
            
            $pojistnaUdalost = $this->pojistnaUdalostFactory->createWithTodayDate($zadavatel);
            $manager->persist($pojistnaUdalost);
            
            // Vytvoření složek pro pojistnou událost
            $this->createFolders($manager, $pojistnaUdalost);
            
            // Vytvoření účastníků pro pojistnou událost
            $this->createParticipants($manager, $pojistnaUdalost, PojistnaUdalost::CATEGORY_HAV);
            
            $this->addReference('pojistna_udalost_today_' . $i, $pojistnaUdalost);
        }
    }
    
    /**
     * Vytvoří nezpracované pojistné události
     */
    private function createUnprocessedPojistnaUdalostFixtures(ObjectManager $manager): void
    {
        // Nejprve zajistíme, že každý likvidátor má alespoň jednu nezpracovanou událost
        for ($likvidatorIndex = 0; $likvidatorIndex < 3; $likvidatorIndex++) {
            $zadavatelIndex = $likvidatorIndex % 10;
            $zadavatel = $this->getReference('zadavatel_' . $zadavatelIndex, Zadavatel::class);
            $manager->persist($zadavatel);
            
            $pojistnaUdalost = $this->pojistnaUdalostFactory->createUnprocessed($zadavatel, $likvidatorIndex);
            
            // Explicitní přiřazení k aktuálnímu likvidátorovi
            $likvidatorInput = new PojistnaUdalostLikvidatorEditInput();
            $likvidatorInput->likvidator = $this->getReference('likvidator_' . $likvidatorIndex, User::class);
            $pojistnaUdalost->modifyLikvidator($likvidatorInput);
            
            $manager->persist($pojistnaUdalost);
            
            // Vytvoření složek pro pojistnou událost
            $this->createFolders($manager, $pojistnaUdalost);
            
            // Vytvoření účastníků pro pojistnou událost
            $this->createParticipants($manager, $pojistnaUdalost, PojistnaUdalost::CATEGORY_HAV);
            
            $this->addReference('pojistna_udalost_unprocessed_likvidator_' . $likvidatorIndex, $pojistnaUdalost);
        }
        
        // Poté vytvoříme dodatečné nezpracované události s náhodným přiřazením likvidátora
        for ($i = 0; $i < 5; $i++) {
            $zadavatelIndex = $i % 10;
            $zadavatel = $this->getReference('zadavatel_' . $zadavatelIndex, Zadavatel::class);
            $manager->persist($zadavatel);
            
            $pojistnaUdalost = $this->pojistnaUdalostFactory->createUnprocessed($zadavatel, $i + 3); // offset +3 to avoid date overlap
            
            // Náhodné přiřazení likvidátora (70% šance)
            if ($this->faker->boolean(70)) {
                $likvidatorInput = new PojistnaUdalostLikvidatorEditInput();
                $likvidatorInput->likvidator = $this->getReference('likvidator_' . $this->faker->numberBetween(0, 2), User::class);
                $pojistnaUdalost->modifyLikvidator($likvidatorInput);
            }
            
            $manager->persist($pojistnaUdalost);
            
            // Vytvoření složek pro pojistnou událost
            $this->createFolders($manager, $pojistnaUdalost);
            
            // Vytvoření účastníků pro pojistnou událost
            $this->createParticipants($manager, $pojistnaUdalost, PojistnaUdalost::CATEGORY_HAV);
            
            $this->addReference('pojistna_udalost_unprocessed_' . $i, $pojistnaUdalost);
        }
    }
    
    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
            ZadavatelFixtures::class
        ];
    }
}
