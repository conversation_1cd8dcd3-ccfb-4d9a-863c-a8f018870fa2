<?php

namespace App\DataFixtures;

use App\Entity\Rizika;
use Doctrine\Persistence\ObjectManager;

class RizikaFixtures extends AbstractBaseFixture
{
    protected function loadData(ObjectManager $manager): void
    {
        $rizikaNames = [
            'Po<PERSON>de<PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON><PERSON>',
            'Přírodní katastrofa',
            'Autonehoda',
            '<PERSON>ž<PERSON>r',
            'Extrémní povětr<PERSON>',
            'Technická závada',
            '<PERSON><PERSON><PERSON><PERSON> ma<PERSON>',
            '<PERSON><PERSON><PERSON><PERSON>',
            'Krupobití',
            '<PERSON><PERSON><PERSON> pů<PERSON>',
            '<PERSON><PERSON><PERSON> sn<PERSON>',
            '<PERSON><PERSON><PERSON> stromu'
        ];

        foreach ($rizikaNames as $index => $name) {
            $riziko = new Rizika($name);
            $manager->persist($riziko);
            $this->addReference('riziko_' . $index, $riziko);
        }

        $manager->flush();
    }
}
