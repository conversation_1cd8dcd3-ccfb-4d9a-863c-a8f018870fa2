<?php

namespace App\DataFixtures;

use App\Entity\Zadavatel;
use Doctrine\Persistence\ObjectManager;

class ZadavatelFixtures extends AbstractBaseFixture
{
    /**
     * Generuje telefonní číslo ve formátu s mezerami (např. 602 123 456)
     */
    private function generatePhoneNumber(): string
    {
        $prefix = $this->faker->randomElement(['601', '602', '603', '604', '605', '606', '607', '608', '609', '720', '721', '722', '723', '724', '725', '726', '727', '728', '729', '730', '731', '732', '733', '734', '735', '736', '737', '738', '739', '770', '771', '772', '773', '774', '775', '776', '777', '778', '779']);
        $middle = $this->faker->numberBetween(100, 999);
        $last = $this->faker->numberBetween(100, 999);
        
        return sprintf('%s %d %d', $prefix, $middle, $last);
    }
    protected function loadData(ObjectManager $manager): void
    {
        $pojistovny = [
            'Allianz pojišťovna',
            'Česká pojišťovna',
            'ČSOB Pojišťovna',
            'Kooperativa pojišťovna',
            'Generali Česká pojišťovna',
            'UNIQA pojišťovna',
            'Direct pojišťovna',
            'AXA pojišťovna',
            'Slavia pojišťovna',
            'Pojišťovna VZP'
        ];

        // Create 10 zadavatel entities
        for ($i = 0; $i < 10; $i++) {
            $nazev = $pojistovny[$i];
            $kontaktniOsoba = $this->faker->firstName() . ' ' . $this->faker->lastName();
            $telefon = $this->generatePhoneNumber();
            $email = $this->faker->companyEmail();

            $zadavatel = new Zadavatel(
                $nazev,
                $kontaktniOsoba,
                $telefon,
                $email
            );

            $manager->persist($zadavatel);
            $this->addReference('zadavatel_' . $i, $zadavatel);
        }

        $manager->flush();
    }
}
