<?php

namespace App\DataFixtures;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;

/**
 * Hlavní fixture třída, která načítá všechny ostatní fixture třídy
 * 
 * Tato třída slouží pouze jako orchestrátor pro načítání ostatních fixture tříd.
 * Samotná data jsou vytvářena v jednotlivých fixture třídách.
 * 
 * Poznámka: Tato třída je prázdná, protože všechny fixture třídy jsou načítány
 * automaticky Doctrine fixture loaderem. Tato třída je zde pouze pro úplnost.
 */
class AppFixtures extends Fixture implements FixtureGroupInterface
{
    public function load(ObjectManager $manager): void
    {
        // Tato třída je prázdn<PERSON>, protože všechny fixture tří<PERSON> jsou na<PERSON>
        // automaticky Doctrine fixture loaderem.
    }

    /**
     * <PERSON><PERSON><PERSON> skupin<PERSON>, do kterých tato fixture patří
     */
    public static function getGroups(): array
    {
        return ['app'];
    }
}
