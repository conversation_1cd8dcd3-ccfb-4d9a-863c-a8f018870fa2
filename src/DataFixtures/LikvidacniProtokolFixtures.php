<?php

namespace App\DataFixtures;

use App\Entity\LikvidacniProtokol;
use App\Entity\PojistnaUdalost;
use App\Enum\StavLikvidace;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class LikvidacniProtokolFixtures extends AbstractBaseFixture implements DependentFixtureInterface
{
    protected function loadData(ObjectManager $manager): void
    {
        $categories = [
            PojistnaUdalost::CATEGORY_HAV,
            PojistnaUdalost::CATEGORY_POV,
            PojistnaUdalost::CATEGORY_MAJ,
            PojistnaUdalost::CATEGORY_ODP
        ];
        
        // Vytvoříme protokoly pouze pro uzavřené pojistné události
        foreach ($categories as $category) {
            for ($i = 0; $i < 2; $i++) {
                $refName = 'pojistna_udalost_' . $category . '_' . StavLikvidace::UZAVRENO->value . '_' . $i;
                
                if ($this->hasReference($refName, PojistnaUdalost::class)) {
                    /** @var PojistnaUdalost $pojistnaUdalost */
                    $pojistnaUdalost = $this->getReference($refName, PojistnaUdalost::class);
                    
                    // Ensure that the PojistnaUdalost entity is persisted
                    $manager->persist($pojistnaUdalost);
                    
                    $protokol = new LikvidacniProtokol(
                        $pojistnaUdalost,
                        $this->faker->uuid() . '.pdf', // filename
                        'protokol_' . $this->faker->uuid() . '.pdf', // originalFilename
                        'application/pdf', // mimeType
                        $this->faker->sentence() // description
                    );
                    
                    $manager->persist($protokol);
                }
            }
        }
        
        $manager->flush();
    }
    
    public function getDependencies(): array
    {
        return [
            PojistnaUdalostFixtures::class,
        ];
    }
}
