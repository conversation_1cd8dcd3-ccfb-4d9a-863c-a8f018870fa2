<?php

namespace App\DataFixtures;

use App\Entity\PojistnaUdalost;
use App\Entity\Poznamka;
use App\Entity\SouborForPoznamka;
use App\Entity\User;
use App\Enum\StavLikvidace;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class PoznamkaFixtures extends AbstractBaseFixture implements DependentFixtureInterface
{
    /**
     * Vytvoří delta objekt pro Rich Editor Quill
     * 
     * @param string $text Text poznámky
     * @return string JSON reprezentace delta objektu
     */
    private function createQuillDelta(string $text): string
    {
        // Základní delta objekt s textem
        $delta = [
            'ops' => [
                [
                    'insert' => $text . "\n"
                ]
            ]
        ];
        
        // Náhodně přidáme formátování pro některé části textu
        if ($this->faker->boolean(30)) {
            // Rozdělíme text na slova
            $words = explode(' ', $text);
            
            if (count($words) > 3) {
                // Vybereme náhodné slovo pro formátování
                $randomIndex = $this->faker->numberBetween(0, count($words) - 2);
                $randomWord = $words[$randomIndex];
                $randomWordLength = strlen($randomWord);
                
                // Vytvoříme nový delta objekt s formátováním
                $formattedText = implode(' ', array_slice($words, 0, $randomIndex));
                $remainingText = ' ' . implode(' ', array_slice($words, $randomIndex + 1));
                
                $delta = [
                    'ops' => [
                        [
                            'insert' => $formattedText . ' '
                        ],
                        [
                            'insert' => $randomWord,
                            'attributes' => $this->faker->randomElement([
                                ['bold' => true],
                                ['italic' => true],
                                ['underline' => true],
                                ['bold' => true, 'italic' => true]
                            ])
                        ],
                        [
                            'insert' => $remainingText . "\n"
                        ]
                    ]
                ];
            }
        }
        
        return json_encode($delta);
    }
    protected function loadData(ObjectManager $manager): void
    {
        $this->createPoznamkyForStandardPojistneUdalosti($manager);
        $this->createPoznamkyForTodayPojistneUdalosti($manager);
        $this->createPoznamkyForUnprocessedPojistneUdalosti($manager);
        $this->createPoznamkyForLikvidators($manager); // přidáno: zajistí úkoly pro likvidátory
        // Flush handled by parent class
    }

    /**
     * Vytvoří poznámky pro standardní pojistné události
     */
    private function createPoznamkyForStandardPojistneUdalosti(ObjectManager $manager): void
    {
        $categories = [
            PojistnaUdalost::CATEGORY_HAV,
            PojistnaUdalost::CATEGORY_POV,
            PojistnaUdalost::CATEGORY_MAJ,
            PojistnaUdalost::CATEGORY_ODP
        ];
        
        $likvidaceStavy = [
            StavLikvidace::PRIJATO->value,
            StavLikvidace::PROBIHA->value,
            StavLikvidace::UZAVRENO->value
        ];
        
        // Pro každou kategorii a stav vytvoříme poznámky
        foreach ($categories as $category) {
            foreach ($likvidaceStavy as $stav) {
                for ($i = 0; $i < 2; $i++) {
                    $refName = 'pojistna_udalost_' . $category . '_' . $stav . '_' . $i;
                    
                    if ($this->hasReference($refName, PojistnaUdalost::class)) {
                        /** @var PojistnaUdalost $pojistnaUdalost */
                        $pojistnaUdalost = $this->getReference($refName, PojistnaUdalost::class);
                        
                        // Ensure that the PojistnaUdalost entity is persisted
                        $manager->persist($pojistnaUdalost);
                        
                        // Vytvoříme několik poznámek pro každou pojistnou událost
                        $numNotes = $this->faker->numberBetween(1, 5);
                        
                        for ($j = 0; $j < $numNotes; $j++) {
                            $this->createPoznamka($manager, $pojistnaUdalost);
                        }
                    }
                }
            }
        }
    }

    /**
     * Vytvoří poznámky pro pojistné události s dnešním datem
     */
    private function createPoznamkyForTodayPojistneUdalosti(ObjectManager $manager): void
    {
        for ($i = 0; $i < 5; $i++) {
            $refName = 'pojistna_udalost_today_' . $i;
            
            if ($this->hasReference($refName, PojistnaUdalost::class)) {
                /** @var PojistnaUdalost $pojistnaUdalost */
                $pojistnaUdalost = $this->getReference($refName, PojistnaUdalost::class);
                
                // Ensure that the PojistnaUdalost entity is persisted
                $manager->persist($pojistnaUdalost);
                
                // Vytvoříme několik poznámek pro každou pojistnou událost
                $numNotes = $this->faker->numberBetween(1, 3);
                
                for ($j = 0; $j < $numNotes; $j++) {
                    $this->createPoznamka($manager, $pojistnaUdalost);
                }
            }
        }
    }

    /**
     * Vytvoří poznámky pro nezpracované pojistné události
     */
    private function createPoznamkyForUnprocessedPojistneUdalosti(ObjectManager $manager): void
    {
        for ($i = 0; $i < 5; $i++) {
            $refName = 'pojistna_udalost_unprocessed_' . $i;
            
            if ($this->hasReference($refName, PojistnaUdalost::class)) {
                /** @var PojistnaUdalost $pojistnaUdalost */
                $pojistnaUdalost = $this->getReference($refName, PojistnaUdalost::class);
                
                // Ensure that the PojistnaUdalost entity is persisted
                $manager->persist($pojistnaUdalost);
                
                // Vytvoříme několik poznámek pro každou pojistnou událost
                $numNotes = $this->faker->numberBetween(1, 3);
                
                for ($j = 0; $j < $numNotes; $j++) {
                    $this->createPoznamka($manager, $pojistnaUdalost);
                }
            }
        }
    }

    /**
     * Vytvoří jednu poznámku pro pojistnou událost
     */
    private function createPoznamka(ObjectManager $manager, PojistnaUdalost $pojistnaUdalost): void
    {
        // Náhodně vybereme autora
        $autorIndex = $this->faker->numberBetween(0, 2);
        /** @var User $autor */
        $autor = $this->getReference('admin_' . $autorIndex, User::class);
        
        // Vždy přidáme řešitele, aby nedocházelo k chybě v šabloně
        $resitelIndex = $this->faker->numberBetween(0, 2);
        /** @var User $resitel */
        $resitel = $this->getReference('likvidator_' . $resitelIndex, User::class);
        
        // Pro některé poznámky přidáme termín
        $termin = null;
        if ($this->faker->boolean(50)) {
            $termin = new \DateTime();
            $termin->modify('+' . $this->faker->numberBetween(1, 30) . ' days');
        }
        
        // Pro některé poznámky nastavíme, že jsou vyřešené
        $vyreseno = false;
        $casVyreseni = null;
        if ($this->faker->boolean(30)) {
            $vyreseno = true;
            $casVyreseni = new \DateTime();
            $casVyreseni->modify('-' . $this->faker->numberBetween(1, 10) . ' days');
        }
        
        // Generování nových properties
        $headline = $this->faker->sentence(3, true);
        $isTask = true;
        $pinned = $this->faker->boolean(20); // 20% šance, že bude připnutá
        $notifyZadavatel = $this->faker->boolean(30); // 30% šance, že bude notifikován zadavatel
        
        $poznamka = new Poznamka(
            $pojistnaUdalost,
            $autor,
            $this->createQuillDelta($this->faker->paragraph()),
            $casVyreseni,
            $this->faker->boolean(30), // hlidatSplneni
            $termin,
            $resitel,
            $vyreseno,
            $this->faker->boolean(10), // vytvorenoSystemem
            $headline,
            $isTask,
            $pinned,
            $notifyZadavatel
        );
        
        $manager->persist($poznamka);
        
        // Pro některé poznámky přidáme soubory
        if ($this->faker->boolean(30)) {
            $this->createFilesForPoznamka($manager, $poznamka, $pojistnaUdalost);
        }
    }

    /**
     * Vytvoří soubory pro poznámku
     */
    private function createFilesForPoznamka(ObjectManager $manager, Poznamka $poznamka, PojistnaUdalost $pojistnaUdalost): void
    {
        $numFiles = $this->faker->numberBetween(1, 2);
        
        for ($i = 0; $i < $numFiles; $i++) {
            $fileTypes = ['pdf', 'jpg', 'png', 'docx', 'xlsx'];
            $fileType = $fileTypes[$this->faker->numberBetween(0, count($fileTypes) - 1)];
            
            $fileName = $this->faker->word() . '_' . $this->faker->word() . '.' . $fileType;
            
            try {
                $slozka = $pojistnaUdalost->getSlozkaUkoly();
                
                $soubor = new SouborForPoznamka(
                    $this->faker->uuid() . '.' . $fileType, // filename
                    $fileName, // originalFilename
                    'application/' . $fileType, // mimeType
                    $slozka, // slozka
                    $poznamka // poznamka
                );
                
                $manager->persist($soubor);
            } catch (\Exception $e) {
                // Skip if we can't get the ukoly folder
                continue;
            }
        }
    }

    /**
     * Zajistí, že každý likvidátor má přiřazené úkoly
     */
    private function createPoznamkyForLikvidators(ObjectManager $manager): void
    {
        // Vytvoří úkoly pro každého likvidátora
        for ($likvidatorIndex = 0; $likvidatorIndex < 3; $likvidatorIndex++) {
            $likvidator = $this->getReference('likvidator_' . $likvidatorIndex, User::class);
            
            // Nejprve získáme jejich nezpracovanou událost, pokud existuje
            $refName = 'pojistna_udalost_unprocessed_likvidator_' . $likvidatorIndex;
            if ($this->hasReference($refName, PojistnaUdalost::class)) {
                $pojistnaUdalost = $this->getReference($refName, PojistnaUdalost::class);
                $manager->persist($pojistnaUdalost);
                
                // Vytvoříme 2-3 úkoly pro tuto událost, všechny přiřazené likvidátorovi
                $numTasks = $this->faker->numberBetween(2, 3);
                for ($i = 0; $i < $numTasks; $i++) {
                    $this->createAssignedPoznamka($manager, $pojistnaUdalost, $likvidator);
                }
            }
            
            // Také vytvoříme úkoly pro jiné náhodné události
            $randomEvents = 2;
            for ($i = 0; $i < $randomEvents; $i++) {
                $category = $this->faker->randomElement([
                    PojistnaUdalost::CATEGORY_HAV,
                    PojistnaUdalost::CATEGORY_POV,
                    PojistnaUdalost::CATEGORY_MAJ,
                    PojistnaUdalost::CATEGORY_ODP
                ]);
                
                $stav = $this->faker->randomElement([
                    StavLikvidace::PRIJATO->value,
                    StavLikvidace::PROBIHA->value
                ]);
                
                $eventIndex = $this->faker->numberBetween(0, 1);
                $refName = 'pojistna_udalost_' . $category . '_' . $stav . '_' . $eventIndex;
                
                if ($this->hasReference($refName, PojistnaUdalost::class)) {
                    $pojistnaUdalost = $this->getReference($refName, PojistnaUdalost::class);
                    $manager->persist($pojistnaUdalost);
                    
                    // Vytvoříme úkol přiřazený tomuto likvidátorovi
                    $this->createAssignedPoznamka($manager, $pojistnaUdalost, $likvidator);
                }
            }
        }
    }

    /**
     * Vytvoří úkol specificky přiřazený likvidátorovi
     */
    private function createAssignedPoznamka(ObjectManager $manager, PojistnaUdalost $pojistnaUdalost, User $likvidator): void
    {
        // Náhodný admin jako autor
        $autorIndex = $this->faker->numberBetween(0, 2);
        $autor = $this->getReference('admin_' . $autorIndex, User::class);
        
        // Termín do 1-14 dnů
        $termin = new \DateTime();
        $termin->modify('+' . $this->faker->numberBetween(1, 14) . ' days');
        
        // Většina by neměla být ještě vyřešena
        $vyreseno = $this->faker->boolean(20);
        $casVyreseni = null;
        if ($vyreseno) {
            $casVyreseni = new \DateTime();
            $casVyreseni->modify('-' . $this->faker->numberBetween(1, 5) . ' days');
        }
        
        // Generování nových properties
        $headline = $this->faker->sentence(3, true);
        $isTask = true; // Vždy je to úkol, protože je přiřazen likvidátorovi
        $pinned = $this->faker->boolean(30); // 30% šance, že bude připnutá
        $notifyZadavatel = $this->faker->boolean(40); // 40% šance, že bude notifikován zadavatel
        
        $poznamka = new Poznamka(
            $pojistnaUdalost,
            $autor,
            $this->createQuillDelta($this->faker->paragraph()),
            $casVyreseni,
            true, // hlidatSplneni - většina by měla být úkoly, které je třeba dokončit
            $termin,
            $likvidator, // explicitně přiřazeno likvidátorovi
            $vyreseno,
            false, // nevytvořeno systémem
            $headline,
            $isTask,
            $pinned,
            $notifyZadavatel
        );
        
        $manager->persist($poznamka);
        
        // Možná přidáme soubory k některým poznámkám
        if ($this->faker->boolean(30)) {
            $this->createFilesForPoznamka($manager, $poznamka, $pojistnaUdalost);
        }
    }

    public function getDependencies(): array
    {
        return [
            UserFixtures::class,
            PojistnaUdalostFixtures::class
        ];
    }
}
