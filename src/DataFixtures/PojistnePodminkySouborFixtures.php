<?php

namespace App\DataFixtures;

use App\Entity\PojistnePodminky;
use App\Entity\PojistnePodminkySoubor;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class PojistnePodminkySouborFixtures extends AbstractBaseFixture implements DependentFixtureInterface
{
    protected function loadData(ObjectManager $manager): void
    {
        // Vytvoříme soubory pro některé pojistné podmínky
        for ($i = 0; $i < 10; $i++) {
            if ($this->hasReference('pojistne_podminky_' . $i, PojistnePodminky::class)) {
                /** @var PojistnePodminky $pojistnePodminky */
                $pojistnePodminky = $this->getReference('pojistne_podminky_' . $i, PojistnePodminky::class);
                
                // 70% šance na vytvoření souboru
                if ($this->faker->boolean(70)) {
                    $filename = $this->faker->uuid() . '.pdf';
                    $originalFilename = 'pojistne_podminky_' . $i . '.pdf';
                    
                    $pojistnePodminkySoubor = new PojistnePodminkySoubor(
                        $filename,
                        $originalFilename,
                        $pojistnePodminky
                    );
                    
                    $manager->persist($pojistnePodminkySoubor);
                    $pojistnePodminky->assignPojistnePodminkySoubor($pojistnePodminkySoubor);
                }
            }
        }
        
        $manager->flush();
    }
    
    public function getDependencies(): array
    {
        return [
            PojistnePodminkyFixtures::class,
        ];
    }
}
