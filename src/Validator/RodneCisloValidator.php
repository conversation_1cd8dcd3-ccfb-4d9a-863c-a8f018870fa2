<?php
namespace App\Validator;

use Symfony\Component\Form\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class RodneCisloValidator extends ConstraintValidator
{
    public function validate(mixed $value, Constraint $constraint)
    {

        // custom constraints should ignore null and empty values to allow
        // other constraints (NotBlank, NotNull, etc.) to take care of that
        if (null === $value || '' === $value) {
            return;
        }        

        if (!$constraint instanceof RodneCislo) {
            throw new UnexpectedTypeException($constraint, RodneCislo::class);
        }

        if (!is_string($value)) {
            throw new UnexpectedTypeException($value, 'string');
        }

        if ($this->validateRodneCislo($value)) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('{{ value }}', $value)
            ->addViolation();
    }

    private function validateRodne<PERSON>islo(string $rodneCislo) : bool
    {
        if (!preg_match('#^\s*(\d\d)(\d\d)(\d\d)[ /]*(\d\d\d)(\d?)\s*$#', $rodneCislo, $matches)) {
            return FALSE;
        }

        list(, $year, $month, $day, $ext, $c) = $matches;
        $yearInt = intval($year);
        $monthInt = intval($month);
        $dayInt = intval($day);

        if ($c === '') {
            $yearInt += $yearInt < 54 ? 1900 : 1800;
        } else {
            // kontrolní číslice
            $mod = intval($year . $month . $day . $ext) % 11;
            if ($mod === 10) $mod = 0;
            if ($mod !== (int) $c) {
                return FALSE;
            }

            $yearInt += $yearInt < 54 ? 2000 : 1900;
        }

        // k měsíci může být připočteno 20, 50 nebo 70
        if ($monthInt > 70 && $yearInt > 2003) {
            $monthInt -= 70;
        } elseif ($monthInt > 50) {
            $monthInt -= 50;
        } elseif ($monthInt > 20 && $yearInt > 2003) {
            $monthInt -= 20;
        }


        // kontrola data
        return checkdate($monthInt, $dayInt, $yearInt);
    }
}
