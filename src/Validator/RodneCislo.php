<?php
namespace App\Validator;

use Symfony\Component\Validator\Constraint;

#[\Attribute]
class RodneCislo extends Constraint
{
    public string $message = '<PERSON><PERSON><PERSON> č<PERSON>lo "{{ value }}" není platné.';
    
    public function __construct(?string $message = null, ?array $groups = null, $payload = null)
    {
        parent::__construct([], $groups, $payload);

        $this->message = $message ?? $this->message;
    }
}