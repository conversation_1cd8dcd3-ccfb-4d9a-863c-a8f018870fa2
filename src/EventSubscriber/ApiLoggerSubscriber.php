<?php

declare(strict_types=1);

namespace App\EventSubscriber;

use App\Enum\Log\LogContext;
use App\Enum\Log\LogType;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Event\ResponseEvent;

class ApiLoggerSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private readonly LoggerInterface $apiExternalIncomingLogger,
        private readonly LoggerInterface $apiExternalOutgoingLogger,
        private readonly LoggerInterface $apiInternalLogger,
    ) {}

    public static function getSubscribedEvents(): array
    {
        return [
            RequestEvent::class => ['onKernelRequest', 100], // Vysoká priorita
            ResponseEvent::class => ['onKernelResponse', -100], // Nízká priorita
        ];
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $request = $event->getRequest();
        $apiContext = $this->determineApiContext($request);

        if ($apiContext === null) {
            return; // Není API request
        }

        $logger = $this->getLoggerForContext($apiContext);

        $logger->info(
            sprintf('%s API Request', $apiContext->value),
            [
                'type' => LogType::API_REQUEST->value,
                'context' => $apiContext->value,
                'method' => $request->getMethod(),
                'path' => $request->getPathInfo(),
                'headers' => $this->sanitizeHeaders($request->headers->all()),
                'query' => $request->query->all(),
                'body' => $this->getRequestBody($request),
                'client_ip' => $request->getClientIp(),
            ]
        );
    }

    public function onKernelResponse(ResponseEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $request = $event->getRequest();
        $apiContext = $this->determineApiContext($request);

        if ($apiContext === null) {
            return;
        }

        $response = $event->getResponse();
        $logger = $this->getLoggerForContext($apiContext);

        $logger->info(
            sprintf('%s API Response', $apiContext->value),
            [
                'type' => LogType::API_RESPONSE->value,
                'context' => $apiContext->value,
                'status_code' => $response->getStatusCode(),
                'headers' => $this->sanitizeHeaders($response->headers->all()),
                'body' => $this->getResponseBody($response),
            ]
        );
    }

    private function determineApiContext(Request $request): ?LogContext
    {
        $path = $request->getPathInfo();

        // Příklad rozlišení typu API (můžete upravit podle vaší aplikace)
        if (str_starts_with($path, '/api/v1/external')) {
            return LogContext::INBOUND;
        }

        if (str_starts_with($path, '/api/internal/v1')) {
            return LogContext::INTERNAL;
        }

        return null;
    }

    private function getLoggerForContext(LogContext $context): LoggerInterface
    {
        return match ($context) {
            LogContext::INBOUND => $this->apiExternalIncomingLogger,
            LogContext::OUTBOUND => $this->apiExternalOutgoingLogger,
            LogContext::INTERNAL => $this->apiInternalLogger,
            default => throw new \InvalidArgumentException('Unsupported API context')
        };
    }

    /**
     * 
     * @param array<mixed> $headers 
     * @return array<mixed> 
     */
    private function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = [
            'authorization',
            'cookie',
            'x-api-key',
            'proxy-authorization',
            'php-auth-user',
            'php-auth-pw'
        ];

        foreach ($headers as $name => $value) {
            if (in_array(strtolower($name), $sensitiveHeaders, true)) {
                $headers[$name] = ['[REDACTED]'];
            }
        }

        return $headers;
    }

    private function getRequestBody(Request $request): mixed
    {
        $content = $request->getContent();

        if (empty($content)) {
            return null;
        }

        try {
            $data = json_decode($content, true, 512, JSON_THROW_ON_ERROR);
            return $this->sanitizeData($data);
        } catch (\JsonException) {
            return '[non-JSON content]';
        }
    }


    private function getResponseBody(Response $response): mixed
    {
        $content = $response->getContent();

        if (empty($content)) {
            return null;
        }

        try {
            $data = json_decode($content, true, 512, JSON_THROW_ON_ERROR);
            return $this->sanitizeData($data);
        } catch (\JsonException) {
            return '[non-JSON content]';
        }
    }

    private function sanitizeData(mixed $data): mixed
    {
        if (!is_array($data)) {
            return $data;
        }

        $sensitiveFields = [
            'password',
            'token',
            'secret',
            'api_key',
            'credit_card',
            'php-auth-user',
            'php-auth-pw'
        ];

        array_walk_recursive($data, function (&$value, $key) use ($sensitiveFields) {
            if (is_string($key) && in_array(strtolower($key), $sensitiveFields, true)) {
                $value = '[REDACTED]';
            }
        });

        return $data;
    }
}
