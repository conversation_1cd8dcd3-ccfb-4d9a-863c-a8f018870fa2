<?php

namespace App\Entity;

use App\Repository\PojistnePodminkyRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PojistnePodminkyRepository::class)]
class PojistnePodminky
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $typVpp = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $urlVpp = null;

    #[ORM\OneToOne(mappedBy: 'pojistnePodminky', cascade: ['persist'])]
    private ?PojistnePodminkySoubor $pojistnePodminkySoubor = null;

    public function __construct(string $typVpp, ?string $urlVpp)
    {
        $this->typVpp = $typVpp;
        $this->setUrlVpp($urlVpp);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTypVpp(): ?string
    {
        return $this->typVpp;
    }

    public function getUrlVpp(): ?string
    {
        return $this->urlVpp;
    }

    public function setUrlVpp(?string $urlVpp): static
    {
        if ($urlVpp !== null AND !filter_var($urlVpp, FILTER_VALIDATE_URL))
            throw new \InvalidArgumentException('URL not valid');
        
        $this->urlVpp = $urlVpp;

        return $this;
    }

    public function modify(string $typVpp, ?string $urlVpp): static
    {
        $this->typVpp = $typVpp;
        $this->setUrlVpp($urlVpp);

        return $this;
    }

    public function updateFromInput(\App\Dto\Ciselniky\PojistnePodminkyEditInput $pojistnePodminkyEditInput): static
    {
        $this->modify(
            $pojistnePodminkyEditInput->typVpp,
            $pojistnePodminkyEditInput->urlVpp
        );

        return $this;
    }


    /**
     * Get the value of pojistnePodminkySoubor
     */
    public function getPojistnePodminkySoubor(): ?PojistnePodminkySoubor
    {
        return $this->pojistnePodminkySoubor;
    }

    /**
     * Set the value of pojistnePodminkySoubor
     */
    public function assignPojistnePodminkySoubor(PojistnePodminkySoubor $pojistnePodminkySoubor): self
    {
        $this->pojistnePodminkySoubor = $pojistnePodminkySoubor;

        return $this;
    }

    public function removePojistnePodminkySoubor():void
    {
        $this->pojistnePodminkySoubor = null;
    }

    public function hasAttachedPojistnePodminkySoubor():bool
    {
        return ($this->pojistnePodminkySoubor !== null);
    }
}