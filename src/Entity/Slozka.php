<?php

namespace App\Entity;

use App\Repository\SlozkaRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: SlozkaRepository::class)]
class Slozka
{
    // allowed values for field $uloziste
    public const ULOZISTE_LOCAL = 'local';
    public const ULOZISTE_S3 = 'S3';

    // allowed values for field $typSlozky
    public const TYP_SLOZKY_SYSTEM = 'System';
    public const TYP_SLOZKY_USER = 'User';

    // allowed values for field $druh
    public const DRUH_SLOZKY_ROOT = 'root';
    public const DRUH_SLOZKY_POJISTOVNA_ZADANI = 'pojistovna_zadani';
    public const DRUH_SLOZKY_POJISTOVNA_UKONCENI = 'pojistovna_ukonceni';
    public const DRUH_SLOZKY_KLIENT = 'klient';
    public const DRUH_SLOZKY_TECHNIK = 'technik';
    public const DRUH_SLOZKY_UKOLY = 'ukoly';
    public const DRUH_SLOZKY_POJISTOVNA_UKONCENI_ODESLANO = 'pojistovna_ukonceni_odeslano';
    public const DRUH_SLOZKY_PU_NEURCENO = 'pu_neurceno';


    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;


    #[ORM\Column(length: 255)]
    private ?string $nazev = null;

    #[ORM\Column(length: 255)]
    private ?string $identifikator = null;

    #[ORM\Column(length: 16)]
    private ?string $uloziste = null;

    #[ORM\Column(length: 16)]
    private ?string $typSlozky = null;

    #[ORM\ManyToOne(inversedBy: 'slozky')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PojistnaUdalost $pojistnaUdalost = null;

    #[ORM\Column(length: 100)]
    private ?string $druhSlozky = null;

    // Hierarchické vztahy
    #[ORM\ManyToOne(targetEntity: self::class, inversedBy: 'children')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Slozka $parent = null;

    /** 
    * @var Collection<int, Slozka> $children
    */
    #[ORM\OneToMany(mappedBy: 'parent', targetEntity: self::class)]
    private Collection $children;

    #[ORM\Column(length: 500, nullable: true)]    
    private ?string $cesta = null;

    public function __construct(
        ?PojistnaUdalost $pojistnaUdalost,
        ?string $nazev,
        ?string $uloziste,
        ?string $typSlozky,
        ?Slozka $parent = null
    ) {
        $this->pojistnaUdalost = $pojistnaUdalost;
        $this->nazev = $nazev;
        $this->parent = $parent;
        $this->children = new ArrayCollection();
        $this->createIdentifikator();
        $this->setUloziste($uloziste);
        $this->defineTypAsSystemSlozka();
        if ($typSlozky === self::TYP_SLOZKY_USER) {
            $this->defineTypAsUserSlozka();
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPojistnaUdalost(): ?PojistnaUdalost
    {
        return $this->pojistnaUdalost;
    }

    public function getNazev(): ?string
    {
        return $this->nazev;
    }

    public function getNazevSlozkyIncludingIdentificator(): ?string
    {
        return $this->nazev . '_' . $this->identifikator;
    }

    public function getIdentifikator(): ?string
    {
        return $this->identifikator;
    }

    private function createIdentifikator(): void
    {
        $this->identifikator = uniqid();
    }

    public function getUloziste(): ?string
    {
        return $this->uloziste;
    }

    public function setUloziste(string $uloziste): static
    {
        $allowed = [
            self::ULOZISTE_LOCAL,
            self::ULOZISTE_S3
        ];
        if (!in_array($uloziste, $allowed)) {
            throw new \InvalidArgumentException("Invalid value for \$uloziste");
        }

        $this->uloziste = $uloziste;

        return $this;
    }

    public function getTypSlozky(): ?string
    {
        return $this->typSlozky;
    }

    public function defineTypAsSystemSlozka(): self
    {
        $this->typSlozky = self::TYP_SLOZKY_SYSTEM;
        return $this;
    }

    public function defineTypAsUserSlozka(): self
    {
        $this->typSlozky = self::TYP_SLOZKY_USER;
        return $this;
    }


    public function getDruhSlozky(): ?string
    {
        return $this->druhSlozky;
    }

    public function defineAsRootSlozka(): self
    {
        $this->druhSlozky = self::DRUH_SLOZKY_ROOT;
        return $this;
    }

    public function defineAsKlientSlozka(): self
    {
        $this->druhSlozky = self::DRUH_SLOZKY_KLIENT;
        return $this;
    }

    public function defineAsTechnikSlozka(): self
    {
        $this->druhSlozky = self::DRUH_SLOZKY_TECHNIK;
        return $this;
    }

    public function defineAsUkolySlozka(): self
    {
        $this->druhSlozky = self::DRUH_SLOZKY_UKOLY;
        return $this;
    }

    public function defineAsPojistovnaZadaniSlozka(): self
    {
        $this->druhSlozky = self::DRUH_SLOZKY_POJISTOVNA_ZADANI;
        return $this;
    }

    public function defineAsPojistovnaUkonceniSlozka(): self
    {
        $this->druhSlozky = self::DRUH_SLOZKY_POJISTOVNA_UKONCENI;
        return $this;
    }

    public function defineAsPojistovnaUkonceniOdeslanoSlozka(): self
    {
        $this->druhSlozky = self::DRUH_SLOZKY_POJISTOVNA_UKONCENI_ODESLANO;
        return $this;
    }

    public function defineAsPUNeurcenoSlozka(): self
    {
        $this->druhSlozky = self::DRUH_SLOZKY_PU_NEURCENO;
        return $this;
    }

    public function isSlozkaForUkoly(): bool
    {
        return $this->druhSlozky == self::DRUH_SLOZKY_UKOLY;
    }

    // Hierarchické metody
    public function getParent(): ?Slozka
    {
        return $this->parent;
    }

    public function assignParent(?Slozka $parent): self
    {
        $this->parent = $parent;
        return $this;
    }

    /**
     * 
     * @return Collection<int, Slozka> 
     */
    public function getChildren(): Collection
    {
        return $this->children;
    }

    public function addChild(Slozka $child): self
    {
        if (!$this->children->contains($child)) {
            $this->children[] = $child;
            $child->assignParent($this);
        }
        return $this;
    }

    public function removeChild(Slozka $child): self
    {
        if ($this->children->removeElement($child)) {
            if ($child->getParent() === $this) {
                $child->assignParent(null);
            }
        }
        return $this;
    }

    public function getCesta(): ?string
    {
        return $this->cesta;
    }

    public function setCesta(?string $cesta): self
    {
        $this->cesta = $cesta;
        return $this;
    }

    public function getFullPath(): string
    {
        if ($this->parent === null) {
            return $this->getNazevSlozkyIncludingIdentificator();
        }

        return $this->parent->getFullPath() . '/' . $this->getNazevSlozkyIncludingIdentificator();
    }

    public function isRoot(): bool
    {
        return $this->parent === null;
    }

    public function isSystemSlozka(): bool
    {
        return $this->typSlozky === self::TYP_SLOZKY_SYSTEM;
    }

    public function isUserSlozka(): bool
    {
        return $this->typSlozky === self::TYP_SLOZKY_USER;
    }

    public function canBeDeleted(): bool
    {
        // Pouze user složky mohou být smazány a musí být prázdné
        return $this->isUserSlozka() && $this->children->isEmpty();
    }

    /**
     * @return array<int, array{id: int|null, nazev: string|null, isRoot: bool}>
     */
    public function getBreadcrumbs(): array
    {
        $breadcrumbs = [];
        $current = $this;

        while ($current !== null) {
            array_unshift($breadcrumbs, [
                'id' => $current->getId(),
                'nazev' => $current->getNazev(),
                'isRoot' => $current->isRoot()
            ]);
            $current = $current->getParent();
        }

        return $breadcrumbs;
    }
}
