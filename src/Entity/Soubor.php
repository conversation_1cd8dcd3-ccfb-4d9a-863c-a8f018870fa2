<?php

namespace App\Entity;

use App\Repository\SouborRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: SouborRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Soubor extends AbstractSlozkaBasedSoubor
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    public function getId(): ?int
    {
        return $this->id;
    }




    #[ORM\PrePersist]
    public function setCreatedAt():void
    {
        $this->createdAt = new DateTimeImmutable();

    }


    public function __construct(
        string $filename,
        string $originalFilename,
        string $mimeType,
        Slozka $slozka,


    )
    {
        $this->filename =  $filename;
        $this->originalFilename =  $originalFilename;
        $this->mimeType =  $mimeType;
        $this->slozka =  $slozka;
    }

}
