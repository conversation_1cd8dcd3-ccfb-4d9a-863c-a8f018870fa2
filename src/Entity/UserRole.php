<?php

namespace App\Entity;

use App\Repository\UserRoleRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: UserRoleRepository::class)]
#[ORM\HasLifecycleCallbacks]
class UserRole
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 180)]
    private string $name;

    #[ORM\Column(length: 250, unique: true)]
    private string $code;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $description = null;

    #[ORM\Column]
    private bool $active = false;

    #[ORM\Column]
    private \DateTimeImmutable $createdAt;

    #[ORM\Column]
    private \DateTimeImmutable $updatedAt;

    public function __construct(string $name, string $description, bool $isActive)
    {
        $this->name =  $name;
        $this->description =  $description;
        $this->active = $isActive;
        $this->assignCodeWithRolePrefix($this->name);
    }

    public function modify(string $name, string $description, bool $isActive): void
    {
        $this->name =  $name;
        $this->description =  $description;
        $this->active = $isActive;
        $this->assignCodeWithRolePrefix($this->name);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }


    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    #[ORM\PrePersist]
    public function setCreatedAt(): static
    {
        $this->createdAt = new DateTimeImmutable();

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function setUpdatedAt(): static
    {
        $this->updatedAt = new DateTimeImmutable();

        return $this;
    }

    public function toggleActive(): void
    {
        $this->active =  !$this->active;
    }

    /**
     * Assigns a code to the role, ensuring it always starts with 'ROLE_' prefix
     * and is converted to uppercase
     * 
     * @param string $name The name to use for the code (with or without 'ROLE_' prefix)
     * @return $this For method chaining
     */
    public function assignCodeWithRolePrefix(string $name): static
    {
        // If the name already starts with 'ROLE_', use it as is
        if (strpos($name, User::ROLE_PREFIX) === 0) {
            $code = $name;
        } else {
            // Otherwise, add the 'ROLE_' prefix
            $code = User::ROLE_PREFIX . $name;
        }
        
        // Convert the final code to uppercase
        $this->code = strtoupper($code);
        
        return $this;
    }

    /**
     * Get the value of code
     */
    public function getCode(): string
    {
        return $this->code;
    }
}
