<?php

namespace App\Entity;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikVozidloEditInput;
use App\Dto\PojistnaUdalost\Ucastnik\UcastnikVozidloNewInput;
use App\Repository\VozidloRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: VozidloRepository::class)]
#[ORM\Table(name: 'vozidlo')]
class Vozidlo
{
    public const CATEGORY_DRUH_OSOBNI = 'osobni';
    public const CATEGORY_DRUH_NAKLADNI = 'nakladni';
    public const CATEGORY_DRUH_PRIVES = 'prives';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'vozidla')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Ucastnik $ucastnik = null;

    #[ORM\Column(length: 255)]
    private ?string $kodRZ = null;

    #[ORM\Column(length: 255)]
    private ?string $kodVIN = null;

    #[ORM\Column(length: 255)]
    private ?string $vykon = null;

    #[ORM\Column(length: 255)]
    private ?string $obsah = null;

    #[ORM\Column(length: 128)]
    private ?string $druh = null;

    #[ORM\Column(length: 255)]
    private ?string $typ = null;

    public function __construct(
        ?Ucastnik $ucastnik,   
        ?string $kodRZ,
        ?string $kodVIN,
        ?string $vykon,
        ?string $obsah,
        ?string $druh,
        ?string $typ
    ) {
        $this->ucastnik = $ucastnik;
        $this->kodRZ = $kodRZ;
        $this->kodVIN = $kodVIN;
        $this->vykon = $vykon;
        $this->obsah = $obsah;
        $this->druh = $druh;
        $this->typ = $typ;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUcastnik(): ?Ucastnik
    {
        return $this->ucastnik;
    }

    public function setUcastnik(?Ucastnik $ucastnik): static
    {
        $this->ucastnik = $ucastnik;
        return $this;
    }

    public function getKodRZ(): ?string
    {
        return $this->kodRZ;
    }

    public function getKodVIN(): ?string
    {
        return $this->kodVIN;
    }

    public function getVykon(): ?string
    {
        return $this->vykon;
    }

    public function getObsah(): ?string
    {
        return $this->obsah;
    }

    public function getTyp(): ?string
    {
        return $this->typ;
    }

    public function getDruh(): ?string
    {
        return $this->druh;
    }

    public function getDruhString(): string
    {
        $result = '';
        switch ($this->druh) {
            case self::CATEGORY_DRUH_OSOBNI:
                $result = 'Osobní';
                break;
            case self::CATEGORY_DRUH_NAKLADNI:
                $result = 'Nákladní';
                break;
            case self::CATEGORY_DRUH_PRIVES:
                $result = 'Přívěs';
                break;
        }
        return $result;
    }

    public static function createUcastnikVozidloFromInput(UcastnikVozidloNewInput $input, ?Ucastnik $ucastnik): Vozidlo
    {
        return new self(
            $ucastnik,
            $input->kodRZ,
            $input->kodVIN,
            $input->vykon,
            $input->obsah,
            $input->druh,
            $input->typ
        );
    }

    public function updateFromInput(UcastnikVozidloEditInput $ucastnikVozidloEditInput): static
    {
        $this->kodRZ = $ucastnikVozidloEditInput->kodRZ;
        $this->kodVIN = $ucastnikVozidloEditInput->kodVIN;
        $this->vykon = $ucastnikVozidloEditInput->vykon;
        $this->obsah = $ucastnikVozidloEditInput->obsah;
        $this->druh = $ucastnikVozidloEditInput->druh;
        $this->typ = $ucastnikVozidloEditInput->typ;

        return $this;
    }
}
