<?php

namespace App\Entity;

use App\Repository\NotifikaceRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: NotifikaceRepository::class)]
class Notifikace
{
    public const TYP_LIKVIDATOR_ASSIGNMENT = 'LIK_ASSIGNMENT';
    public const TYP_LIKVIDATOR_REVOCATION = 'LIK_REVOCATION';
    public const TYP_PROHLIDKA_CREATION = 'PROHLIDKA_CREATION';
    public const TYP_PROHLIDKA_REVOCATION = 'PROHLIDKA_REVOCATION';
    public const TYP_POZNAMKA_TASK_ASSIGNMENT = 'POZNAMKA_TASK_ASSIGNMENT';
    public const TYP_POZNAMKA_TASK_ASSIGNMENT_REVOCATION = 'POZNAMKA_TASK_ASSIGNMENT_REVOCATION';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $titulek = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $obsah = null;

    #[ORM\Column(length: 63)]
    private ?string $typ = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $datumVytvoreni = null;

    #[ORM\Column]
    private ?bool $precteno = null;

    #[ORM\ManyToOne(inversedBy: 'notifikace')]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $uzivatel = null;

    #[ORM\Column]
    private ?bool $sendMail = null;

    public function __construct(
        string $titulek,
        string $obsah,
        string $typ,
        User $uzivatel,
        bool $sendMail
    ) {
        $this->titulek = $titulek;
        $this->obsah = $obsah;
        $this->setTyp($typ);
        $this->uzivatel = $uzivatel;
        $this->datumVytvoreni = new \DateTime();
        $this->precteno = false;
        $this->sendMail = $sendMail;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitulek(): ?string
    {
        return $this->titulek;
    }

    public function getObsah(): ?string
    {
        return $this->obsah;
    }

    public function getTyp(): ?string
    {
        return $this->typ;
    }

    public function getTypString(): string
    {
        return match ($this->typ) {
            self::TYP_LIKVIDATOR_ASSIGNMENT => 'Přiřazení likvidátora',
            self::TYP_LIKVIDATOR_REVOCATION => 'Odebrání likvidátora',
            self::TYP_PROHLIDKA_CREATION => 'Vytvoření prohlídky',
            self::TYP_PROHLIDKA_REVOCATION => 'Zrušení prohlídky',
            self::TYP_POZNAMKA_TASK_ASSIGNMENT => 'Přiřazení úkolu',
            self::TYP_POZNAMKA_TASK_ASSIGNMENT_REVOCATION => 'Odebrání úkolu',
            default => '',
        };
    }

    public static function getTypStringFromString(string $typ): string
    {
        return match ($typ) {
            self::TYP_LIKVIDATOR_ASSIGNMENT => 'Přiřazení likvidátora',
            'Přiřazení likvidátora' => 'Přiřazení likvidátora',
            self::TYP_LIKVIDATOR_REVOCATION => 'Odebrání likvidátora',
            'Odebrání likvidátora' => 'Odebrání likvidátora',
            self::TYP_PROHLIDKA_CREATION => 'Vytvoření prohlídky',
            'Vytvoření prohlídky' => 'Vytvoření prohlídky',
            self::TYP_PROHLIDKA_REVOCATION => 'Zrušení prohlídky',
            'Zrušení prohlídky' => 'Zrušení prohlídky',
            self::TYP_POZNAMKA_TASK_ASSIGNMENT => 'Přiřazení úkolu',
            'Přiřazení úkolu' => 'Přiřazení úkolu',
            self::TYP_POZNAMKA_TASK_ASSIGNMENT_REVOCATION => 'Odebrání úkolu',
            'Odebrání úkolu' => 'Odebrání úkolu',
            default => '',
        };
    }

    /**
     * @return string[]
     */
    public static function getTypes(): array
    {
        return [
            self::TYP_LIKVIDATOR_ASSIGNMENT,
            self::TYP_LIKVIDATOR_REVOCATION,
            self::TYP_PROHLIDKA_CREATION,
            self::TYP_PROHLIDKA_REVOCATION,
            self::TYP_POZNAMKA_TASK_ASSIGNMENT,
            self::TYP_POZNAMKA_TASK_ASSIGNMENT_REVOCATION,
        ];
    }

    private function setTyp(string $typ): static
    {
        if (
            !in_array($typ, [
                self::TYP_LIKVIDATOR_ASSIGNMENT,
                self::TYP_LIKVIDATOR_REVOCATION,
                self::TYP_PROHLIDKA_CREATION,
                self::TYP_PROHLIDKA_REVOCATION,
                self::TYP_POZNAMKA_TASK_ASSIGNMENT,
                self::TYP_POZNAMKA_TASK_ASSIGNMENT_REVOCATION,
            ])
        ) throw new \InvalidArgumentException('Invalid notification type');
        $this->typ = $typ;

        return $this;
    }

    public function getDatumVytvoreni(): ?\DateTimeInterface
    {
        return $this->datumVytvoreni;
    }

    public function togglePrecteno(): static
    {
        $this->precteno = !$this->precteno;

        return $this;
    }

    public function isPrecteno(): ?bool
    {
        return $this->precteno;
    }

    public function getUzivatel(): ?User
    {
        return $this->uzivatel;
    }

    public function isSendMail(): ?bool
    {
        return $this->sendMail;
    }
}
