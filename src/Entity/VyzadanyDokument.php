<?php

namespace App\Entity;

use App\Dto\VyzadanyDokument\VyzadanyDokumentEditInput;
use App\Repository\VyzadanyDokumentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: VyzadanyDokumentRepository::class)]
#[ORM\Table(name: 'vyzadany_dokument')]
#[ORM\HasLifecycleCallbacks]
class VyzadanyDokument
{
    public const STAV_VYZADANO = 'VYZADANO';
    public const STAV_DOLOZENO = 'DOLOZENO';
    public const STAV_UZ_NEZADANO = 'UZ_NEZADANO';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'vyzadaneDokumenty')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PojistnaUdalost $pojistnaUdalost = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $autor = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $coVyzadano = null;

    #[ORM\Column(length: 255)]
    private ?string $stav = self::STAV_VYZADANO;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct(
        ?PojistnaUdalost $pojistnaUdalost,
        ?User $autor,
        ?string $coVyzadano,
        ?string $stav = self::STAV_VYZADANO
    ) {
        $this->pojistnaUdalost = $pojistnaUdalost;
        $this->autor = $autor;
        $this->coVyzadano = $coVyzadano;
        $this->stav = $stav ?? self::STAV_VYZADANO;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPojistnaUdalost(): ?PojistnaUdalost
    {
        return $this->pojistnaUdalost;
    }

    public function modifyPojistnaUdalost(?PojistnaUdalost $pojistnaUdalost): static
    {
        $this->pojistnaUdalost = $pojistnaUdalost;

        return $this;
    }

    public function getAutor(): ?User
    {
        return $this->autor;
    }

    public function modifyAutor(?User $autor): static
    {
        $this->autor = $autor;

        return $this;
    }

    public function getCoVyzadano(): ?string
    {
        return $this->coVyzadano;
    }

    public function getStav(): ?string
    {
        return $this->stav;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    #[ORM\PrePersist]
    public function onPrePersistCreated(): void
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function modify(VyzadanyDokumentEditInput $vyzadanyDokumentEditInput): self
    {
        $this->coVyzadano = $vyzadanyDokumentEditInput->coVyzadano;

        if ($vyzadanyDokumentEditInput->stav !== $this->stav) {
            $this->updatedAt = new \DateTime();
        }

        $this->stav = $vyzadanyDokumentEditInput->stav;

        return $this;
    }
}