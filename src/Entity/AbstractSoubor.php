<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

abstract class AbstractSoubor
{
    const IMAGE_MIME_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];

    #[ORM\Column(length: 255)]
    protected ?string $filename = null;

    #[ORM\Column(length: 255)]
    protected ?string $originalFilename = null;

    #[ORM\Column(length: 255)]
    protected ?string $mimeType = null;

    #[ORM\Column]
    protected ?\DateTimeImmutable $createdAt = null;

    abstract public function setCreatedAt():void;



    public function getFilename(): ?string
    {
        return $this->filename;
    }


    public function getOriginalFilename(): ?string
    {
        return $this->originalFilename;
    }

    public function modifyOriginalFilename(string $newOriginalFilename): void
    {
        $this->originalFilename = $newOriginalFilename;
    }


    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function isImage(): bool
    {
        return in_array($this->mimeType, SELF::IMAGE_MIME_TYPES);
    }
}
