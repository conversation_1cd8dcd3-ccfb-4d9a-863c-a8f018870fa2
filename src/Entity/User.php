<?php

namespace App\Entity;

use App\Dto\User\UserEditInput;
use App\Dto\User\ProfileEditInput;
use App\Enum\PuListColumn;
use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: '`user`')]
#[ORM\UniqueConstraint(name: 'UNIQ_IDENTIFIER_EMAIL', fields: ['email'])]
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    public const ROLE_UW_ADMIN = 'ROLE_UW_ADMIN';
    public const ROLE_COM_ADMIN = 'ROLE_COM_ADMIN';
    public const ROLE_LIKVIDATOR = 'ROLE_LIKVIDATOR';
    public const ROLE_USER = 'ROLE_USER';
    public const ROLE_PREFIX = 'ROLE_';



    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 180)]
    private ?string $email = null;

    /**
     * @var list<string> The user roles
     */
    #[ORM\Column]
    private array $roles = [];

    /**
     * @var string The hashed password
     */
    #[ORM\Column]
    private ?string $password = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255)]
    private ?string $surname = null;

    #[ORM\Column(length: 255)]
    private ?string $telephone = null;

    #[ORM\Column]
    private bool $active = true;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $lastLogin = null;

    /**
     * @var Collection<int, Poznamka>
     */
    #[ORM\OneToMany(targetEntity: Poznamka::class, mappedBy: 'autor')]
    private Collection $autorPoznamky;

    /**
     * @var Collection<int, Poznamka>
     */
    #[ORM\OneToMany(targetEntity: Poznamka::class, mappedBy: 'resitel')]
    private Collection $resitelPoznamky;

    /**
     * @var Collection<int, Notifikace>
     */
    #[ORM\OneToMany(targetEntity: Notifikace::class, mappedBy: 'uzivatel')]
    private Collection $notifikace;

    #[ORM\ManyToOne(targetEntity: UserRole::class)]
    private ?UserRole $dynamicRole = null;

    /** @var string[] */
    #[ORM\Column(type: Types::JSON)]
    private ?array $puShownCols = [];

    public function __construct()
    {
        $this->autorPoznamky = new ArrayCollection();
        $this->resitelPoznamky = new ArrayCollection();
        $this->notifikace = new ArrayCollection();
        $this->puShownCols = PuListColumn::values();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;

        return $this;
    }

    /**
     * A visual identifier that represents this user.
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    /**
     * @see UserInterface
     *
     * @return list<string>
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        $roles = array_filter($roles);       

        // Přidáme dynamickou roli, pokud existuje
        if ($this->dynamicRole !== null) {
            $roles[] = $this->dynamicRole->getCode();
        }

        // guarantee every user at least has ROLE_USER
        $roles[] = self::ROLE_USER;

        return array_unique($roles);
    }

    /**
     * @param list<string> $roles
     */
    public function setRoles(array $roles): static
    {
        $this->roles = $roles;

        return $this;
    }

    /**
     * @see PasswordAuthenticatedUserInterface
     */
    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(string $password): static
    {
        $this->password = $password;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getSurname(): ?string
    {
        return $this->surname;
    }

    public function setSurname(string $surname): static
    {
        $this->surname = $surname;

        return $this;
    }

    public function getFullName(): string
    {
        return $this->name . ' ' . $this->surname;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(string $telephone): static
    {
        $this->telephone = $telephone;

        return $this;
    }

    public function getActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): static
    {
        $this->active = $active;

        return $this;
    }

    /**
     * provides string representing user's role
     * @return string 
     */
    public function getRoleString(): string
    {
        if (!empty($this->roles)) {
            $firstRole = $this->roles[0];
            switch ($firstRole) {
                case 'ROLE_LIKVIDATOR':
                    return 'Likvidátor';

                case 'ROLE_COM_ADMIN':
                    return 'Administrátor';
            }
        }

        if ($this->dynamicRole !== null) {
            return $this->dynamicRole->getName();
        }

        return '';
    }


    public function isLikvidator(): bool
    {
        return ($this->getRoles()[0] === self::ROLE_LIKVIDATOR);
    }

    public function isAdministrator(): bool
    {
        return ($this->getRoles()[0] === self::ROLE_COM_ADMIN);
    }

    public function getLastLogin(): ?\DateTimeInterface
    {
        return $this->lastLogin;
    }

    public function setLastLogin(?\DateTimeInterface $lastLogin): static
    {
        $this->lastLogin = $lastLogin;
        return $this;
    }

    public function getFirstRole(): string
    {
        $allRoles = $this->getRoles();

        if (isset($allRoles[0]) && !empty($allRoles[0])) {
            return $allRoles[0];
        }
        return '';
    }

    public function modifyUserProfile(ProfileEditInput $profileEditInput): User
    {
        $this->email =  $profileEditInput->email;
        $this->name = $profileEditInput->name;
        $this->surname = $profileEditInput->surname;
        $this->telephone = $profileEditInput->telephone;
        return $this;
    }

    public function getLastLoginString(): string
    {
        if (is_null($this->getLastLogin())) {
            return 'nikdy';
        }

        return $this->getLastLogin()->format('d.m.Y H:i');
    }

    /**
     * @return Collection<int, Poznamka>
     */
    public function getAutorPoznamky(): Collection
    {
        return $this->autorPoznamky;
    }

    public function addAutorPoznamky(Poznamka $autorPoznamky): static
    {
        if (!$this->autorPoznamky->contains($autorPoznamky)) {
            $this->autorPoznamky->add($autorPoznamky);
            $autorPoznamky->modifyAutor($this);
        }

        return $this;
    }

    public function removeAutorPoznamky(Poznamka $autorPoznamky): static
    {
        if ($this->autorPoznamky->removeElement($autorPoznamky)) {
            // set the owning side to null (unless already changed)
            if ($autorPoznamky->getAutor() === $this) {
                $autorPoznamky->modifyAutor(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Poznamka>
     */
    public function getResitelPoznamky(): Collection
    {
        return $this->resitelPoznamky;
    }

    public function addResitelPoznamky(Poznamka $resitelPoznamky): static
    {
        if (!$this->resitelPoznamky->contains($resitelPoznamky)) {
            $this->resitelPoznamky->add($resitelPoznamky);
            $resitelPoznamky->modifyResitel($this);
        }

        return $this;
    }

    public function removeResitelPoznamky(Poznamka $resitelPoznamky): static
    {
        if ($this->resitelPoznamky->removeElement($resitelPoznamky)) {
            // set the owning side to null (unless already changed)
            if ($resitelPoznamky->getResitel() === $this) {
                $resitelPoznamky->modifyResitel(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Notifikace>
     */
    public function getNotifikace(): Collection
    {
        return $this->notifikace;
    }


    /**
     * Get the value of dynamicRole
     */
    public function getDynamicRole(): ?UserRole
    {
        return $this->dynamicRole;
    }

    /**
     * Set the value of dynamicRole
     */
    public function assignDynamicRole(?UserRole $dynamicRole): self
    {
        $this->dynamicRole = $dynamicRole;

        return $this;
    }

    /** @param string[] $columns */
    public function setPuCols(array $columns): static
    {
        foreach ($columns as $column)
            if (!in_array($column, PuListColumn::values()))
                throw new \InvalidArgumentException('Invalid column name: ' . $column);

        $this->puShownCols = $columns;
        return $this;
    }

    /** @return string[] */
    public function getPuShownCols(): array
    {
        return $this->puShownCols ?? $this->puShownCols = PuListColumn::values();
    }

    /** @return PuListColumn[] */
    public function getPuShownColsEnum(): array
    {
        return PuListColumn::fromArray($this->getPuShownCols());
    }
}
