<?php

namespace App\Entity;

use App\Repository\RizikaRepository;
use Doctrine\ORM\Mapping as ORM;
use App\Dto\Rizika\RizikaEditInput; // Import správné třídy

#[ORM\Entity(repositoryClass: RizikaRepository::class)]
class Rizika
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255)]
    private string $typPojistneni;

    public function __construct(string $typPojistneni)
    {
        $this->typPojistneni = $typPojistneni;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTypPojistneni(): ?string
    {
        return $this->typPojistneni;
    }
    
    public function modify(RizikaEditInput $rizikaEditInput): Rizika
    {
        $this->typPojistneni = $rizikaEditInput->typPojistneni;
        
        return $this;
    }
}
