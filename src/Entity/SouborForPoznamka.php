<?php

namespace App\Entity;

use App\Repository\SouborForPoznamkaRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use Exception;

#[ORM\Entity(repositoryClass: SouborForPoznamkaRepository::class)]
#[ORM\HasLifecycleCallbacks]
class SouborForPoznamka extends AbstractSlozkaBasedSoubor
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'soubory')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Poznamka $poznamka = null;

    public function __construct(
        string $filename,
        string $originalFilename,
        string $mimeType,
        Slozka $slozka,
        Poznamka $poznamka
    ) {


        if (!$slozka->isSlozkaForUkoly()) {
            throw new Exception("Cannot create SouborForPoznamka in Slozka that is not DRUH_SLOZKY_UKOLY", 1);
        }

        $this->filename =  $filename;
        $this->originalFilename =  $originalFilename;
        $this->mimeType =  $mimeType;
        $this->slozka =  $slozka;
        $this->poznamka = $poznamka;
    }

    #[ORM\PrePersist]
    public function setCreatedAt(): void
    {
        $this->createdAt = new DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPoznamka(): ?Poznamka
    {
        return $this->poznamka;
    }
}
