<?php

namespace App\Entity;

use App\Repository\LimitRizikaRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: LimitRizikaRepository::class)]
class LimitRizika
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private string $pojisteneRiziko;

    #[ORM\Column]
    private int $castkaLimitRizika = 0;

    #[ORM\Column(length: 255)]
    private string $limitRizikaNa;

    #[ORM\ManyToOne(inversedBy: 'limityRizika')]
    #[ORM\JoinColumn(nullable: false)]
    private PojistnaUdalost $pojistnaUdalost;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private Rizika $zRizika;


    public function __construct(
        PojistnaUdalost $pojistnaUdalost,
        Rizika $riziko,
        string $pojisteneRiziko,
        int $castkaLimitRizika,
        string $limitRizikaNa,        
    )
    {
        $this->pojistnaUdalost = $pojistnaUdalost;
        $this->pojisteneRiziko = $pojisteneRiziko;
        $this->castkaLimitRizika = $castkaLimitRizika;
        $this->limitRizikaNa =  $limitRizikaNa;     
        $this->zRizika = $riziko;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPojisteneRiziko(): ?string
    {
        return $this->pojisteneRiziko;
    }

    public function getCastkaLimitRizika(): ?int
    {
        return $this->castkaLimitRizika;
    }


    public function getLimitRizikaNa(): ?string
    {
        return $this->limitRizikaNa;
    }


    public function getPojistnaUdalost(): ?PojistnaUdalost
    {
        return $this->pojistnaUdalost;
    }

    public function getZRizika(): ?Rizika
    {
        return $this->zRizika;
    }

}
