<?php

namespace App\Entity;

use App\Dto\Poznamka\PoznamkaEditDueDateInput;
use App\Repository\PoznamkaRepository;
use App\Dto\Poznamka\PoznamkaEditInput;
use App\Dto\Poznamka\PoznamkaResolveInput;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PoznamkaRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Poznamka
{
    public const TASK_INFO = 'TASK_INFO';
    public const TASK_WARNING = 'TASK_WARNING';
    public const TASK_DANGER = 'TASK_DANGER';
    public const TASK_DONE = 'TASK_DONE';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'poznamky')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PojistnaUdalost $pojistnaUdalost = null;

    #[ORM\ManyToOne(inversedBy: 'autorPoznamky')]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $autor = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $obsah = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $casVytvoreno = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $casUpraveno = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $casVyreseni = null;

    #[ORM\Column]
    private ?bool $hlidatSplneni = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $termin = null;

    #[ORM\ManyToOne(inversedBy: 'resitelPoznamky')]
    private ?User $resitel = null;

    #[ORM\Column]
    private bool $vyreseno = false;

    #[ORM\Column]
    private ?bool $vytvorenoSystemem = null;

    /**
     * @var Collection<int, SouborForPoznamka>
     */
    #[ORM\OneToMany(targetEntity: SouborForPoznamka::class, mappedBy: 'poznamka')]
    private Collection $soubory;

    #[ORM\Column]
    private bool $withTask = false;

    #[ORM\Column(length: 255)]
    private ?string $headline = null;

    #[ORM\Column]
    private bool $pinned = false;

    #[ORM\Column]
    private bool $notifyZadavatel = false;

    public function __construct(
        ?PojistnaUdalost $pojistnaUdalost,
        ?User $autor,
        ?string $obsah,
        ?\DateTimeInterface $casVyreseni,
        ?bool $hlidatSplneni,
        ?\DateTimeInterface $termin,
        ?User $resitel,
        bool $vyreseno,
        ?bool $vytvorenoSystemem,
        string $headline,
        bool $isTask,
        bool $pinned,
        bool $notifyZadavatel
    ) {
        $this->pojistnaUdalost = $pojistnaUdalost;
        $this->autor = $autor;
        $this->obsah = $obsah;
        $this->casVyreseni = $casVyreseni;
        $this->hlidatSplneni = $hlidatSplneni;
        $this->termin = $termin;
        $this->resitel = $resitel;
        $this->vyreseno = $vyreseno;
        $this->vytvorenoSystemem = $vytvorenoSystemem;
        if (is_null($this->vytvorenoSystemem)) {
            $this->vytvorenoSystemem = false;
        }


        $this->headline =  $headline;
        $this->withTask = $isTask;
        $this->pinned =  $pinned;
        $this->notifyZadavatel = $notifyZadavatel;

        $this->soubory = new ArrayCollection();


    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPojistnaUdalost(): ?PojistnaUdalost
    {
        return $this->pojistnaUdalost;
    }

    public function modifyPojistnaUdalost(?PojistnaUdalost $pojistnaUdalost): static
    {
        $this->pojistnaUdalost = $pojistnaUdalost;

        return $this;
    }

    public function getAutor(): ?User
    {
        return $this->autor;
    }

    public function modifyAutor(?User $autor): static
    {
        $this->autor = $autor;

        return $this;
    }

    public function getObsah(): ?string
    {
        return $this->obsah;
    }

    public function getPrehledObsah(): ?string
    {
        return strlen($this->obsah) <= 250
            ? $this->obsah
            : mb_substr($this->obsah, 0, 250) . '...';
    }

    public function getCasVytvoreno(): ?\DateTimeInterface
    {
        return $this->casVytvoreno;
    }

    public function getCasUpraveno(): ?\DateTimeInterface
    {
        return $this->casUpraveno;
    }


    public function getCasVyreseni(): ?\DateTimeInterface
    {
        return $this->casVyreseni;
    }

    public function isHlidatSplneni(): ?bool
    {
        return $this->hlidatSplneni;
    }

    public function activateHlidatSplneni(): void
    {
        $this->hlidatSplneni = true;
    }


    public function getTermin(): ?\DateTimeInterface
    {
        return $this->termin;
    }

    public function getResitel(): ?User
    {
        return $this->resitel;
    }

    public function modifyResitel(?User $resitel): static
    {
        $this->resitel = $resitel;

        return $this;
    }

    public function isVyreseno(): bool
    {
        return $this->vyreseno;
    }

    public function isVytvorenoSystemem(): ?bool
    {
        return $this->vytvorenoSystemem;
    }

    public function isTask(): bool
    {
        return $this->withTask;
        //return $this->resitel !== null || $this->termin !== null;
    }

    public function getTaskStatus(): string
    {
        if ($this->vyreseno) return SELF::TASK_DONE;
        if (!$this->termin) return SELF::TASK_INFO;

        $now = new \DateTime();
        $interval = $this->termin->diff($now);
        if ($this->termin < $now) return SELF::TASK_DANGER;
        if ($interval->days <= 3) return SELF::TASK_WARNING;

        return SELF::TASK_INFO;
    }

    #[ORM\PrePersist]
    public function onPrePersistCreated(): void
    {
        $this->casVytvoreno = new \DateTime();
        $this->casUpraveno = new \DateTime();
    }

    #[ORM\PreUpdate]
    public function onPreUpdateUpdated(): void
    {
        $this->casUpraveno = new \DateTime();
    }

    public function modify(PoznamkaEditInput $poznamkaEditInput): Poznamka
    {
        $this->obsah = $poznamkaEditInput->obsah;
        $this->hlidatSplneni = $poznamkaEditInput->hlidatSplneni;
        $this->termin = $poznamkaEditInput->termin;
        $this->resitel = $poznamkaEditInput->resitel;
        $this->vyreseno = $poznamkaEditInput->vyreseno;
        $this->withTask = $poznamkaEditInput->withTask;
        $this->headline = $poznamkaEditInput->headline;
        $this->pinned = $poznamkaEditInput->pinned;
        $this->notifyZadavatel = $poznamkaEditInput->notifyZadavatel;

        return $this;
    }

    public function modifyResolve(PoznamkaResolveInput $poznamkaResolveInput): Poznamka
    {
        $this->casVyreseni = new \DateTime();
        $this->vyreseno = true;

        return $this;
    }

    public function modifyDueDate(PoznamkaEditDueDateInput $editInput): Poznamka
    {
        $this->termin = $editInput->termin;

        return $this;
    }

    public function canView(User $user): bool
    {
        if ($this->autor === $user || $this->resitel === $user) {
            return true;
        }

        if ($user->isAdministrator()) {
            return true;
        }

        if ($user->isLikvidator() && $this->pojistnaUdalost && $this->pojistnaUdalost->getLikvidator() === $user) {
            return true;
        }

        return false;
    }

    public function terminDiffDays(?\DateTimeInterface $dateTermin): int
    {

        if ($dateTermin === null) {
            throw new \InvalidArgumentException('Argument cannot be null');
        }

        if (!$dateTermin instanceof \DateTimeInterface) {
            throw new \InvalidArgumentException('Argument must be of type DateTimeInterface');
        }

        $now = new \DateTime();
        $interval = $now->diff($dateTermin);
        $totalDays = $interval->days;

        if ($dateTermin < $now) {
            return -1 * $totalDays; // Return negative days difference
        } else {
            return $totalDays; // Return positive days difference
        }
    }

    /**
     * @return Collection<int, SouborForPoznamka>
     */
    public function getSoubory(): Collection
    {
        return $this->soubory;
    }



    public function getHeadline(): ?string
    {
        return $this->headline;
    }

    public function isPinned(): ?bool
    {
        return $this->pinned;
    }

    public function pinIt(): static
    {
        $this->pinned = true;

        return $this;
    }

    public function isNotifyZadavatel(): ?bool
    {
        return $this->notifyZadavatel;
    }
}
