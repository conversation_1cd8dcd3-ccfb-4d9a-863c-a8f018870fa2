<?php

namespace App\Entity;

use App\Repository\ProhlidkaRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProhlidkaRepository::class)]
class Prohlidka
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $technik = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $datum = null;

    #[ORM\ManyToOne(inversedBy: 'prohlidky')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PojistnaUdalost $pojistnaUdalost = null;


    public function __construct(
        PojistnaUdalost $pojistnaUdalost,
        User $realizator,
        ?DateTimeImmutable $datumProhlidky = null
    )
    {
        $this->pojistnaUdalost =  $pojistnaUdalost;
        $this->technik =  $realizator;
        $this->datum =  $datumProhlidky;

    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDatum(): ?\DateTimeImmutable
    {
        return $this->datum;
    }

    public function setDatum(?\DateTimeImmutable $datum): self
    {
        $this->datum = $datum;
        return $this;
    }

    /**
     * Get the value of technik
     */
    public function getTechnik(): ?User
    {
        return $this->technik;
    }

    public function getPojistnaUdalost(): ?PojistnaUdalost
    {
        return $this->pojistnaUdalost;
    }
}
