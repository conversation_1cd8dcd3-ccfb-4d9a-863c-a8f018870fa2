<?php

namespace App\Entity;

use App\Repository\LikvidacniProtokolRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: LikvidacniProtokolRepository::class)]
#[ORM\HasLifecycleCallbacks]
class LikvidacniProtokol extends AbstractSoubor
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'likvidacniProtokoly')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PojistnaUdalost $pojistnaUdalost = null;

    #[ORM\Column(length: 255)]
    private ?string $description = null;



    public function __construct(
        PojistnaUdalost $pojistnaUdalost,
        string $filename,
        string $originalFilename,
        string $mimeType,
        string $description
        )
    {
        
        $this->pojistnaUdalost =  $pojistnaUdalost;
        $this->filename =  $filename;
        $this->originalFilename = $originalFilename;
        $this->mimeType =  $mimeType;
        $this->description =  $description;
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPojistnaUdalost(): ?PojistnaUdalost
    {
        return $this->pojistnaUdalost;
    }

    #[ORM\PrePersist]
    public function setCreatedAt():void
    {
        $this->createdAt = new DateTimeImmutable();

    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

}
