<?php

namespace App\Entity;

use App\Dto\PojistnaUdalost\PojistnaSmlouva\PojistnaSmlouvaEditInput;
use App\Repository\PojistnaUdalostRepository;
use App\Dto\PojistnaUdalost\PojistnaUdalostEditInput;
use App\Dto\PojistnaUdalost\PojistnaUdalostAdditionalInfoEditInput;
use App\Enum\PojistenaRizika;
use App\Enum\StavLikvidace;
use App\Exception\Slozka\SlozkaNotFoundException;
use DateTime;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;



#[ORM\Entity(repositoryClass: PojistnaUdalostRepository::class)]
#[ORM\HasLifecycleCallbacks]
class PojistnaUdalost
{
    // allowed values for field $kategorie
    public const CATEGORY_HAV = 'HAV';
    public const CATEGORY_POV = 'POV';
    public const CATEGORY_MAJ = 'MAJ';
    public const CATEGORY_ODP = 'ODP';

    // allowed values for field $stavLikvidace
    // @deprecated Use StavLikvidace enum instead
    public const LIKVIDACE_STAV_PRIJATO = 'prijato';
    // @deprecated Use StavLikvidace enum instead
    public const LIKVIDACE_STAV_PROBIHA = 'probiha';
    // @deprecated Use StavLikvidace enum instead
    public const LIKVIDACE_STAV_UZAVRENO = 'uzavreno';
    // @deprecated Use StavLikvidace enum instead
    public const LIKVIDACE_STAV_PROHLIDKA = 'prohlidka';
    // @deprecated Use StavLikvidace enum instead
    public const LIKVIDACE_STAV_ROZPOCET = 'rozpocet';
    // @deprecated Use StavLikvidace enum instead
    public const LIKVIDACE_STAV_POSTUP = 'postup';
    // @deprecated Use StavLikvidace enum instead
    public const LIKVIDACE_STAV_POZADAVEK = 'pozadavek';
    // @deprecated Use StavLikvidace enum instead
    public const LIKVIDACE_STAV_CEKAME = 'cekame'; // they do not need this but keeping for compatibility

    // allowed values for field $prohlidkaTyp
    public const PROHLIDKA_TYP_KLIENT = 'ano_klient';
    public const PROHLIDKA_TYP_NE = 'ne';
    public const PROHLIDKA_TYP_TECHNIK = 'ano_technik';

    // allowed values for field $zpusobOpravy
    public const ZPUSOB_OPRAVY_ROZPOCTEM = 'rozpoctem';
    public const ZPUSOB_OPRAVY_FAKTURA = 'faktura';

    public const FOTODOKUMENTACE_NE = 'NE';
    public const FOTODOKUMENTACE_ANO_KLIENT = 'ANO-KLIENT';
    public const FOTODOKUMENTACE_ANO_TECHNIK = 'ANO-TECHNIK';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 128)]
    private ?string $cisloPojistnaUdalost = null;

    #[ORM\Column(length: 128,  nullable: true)]
    private ?string $cisloSkodniUdalost = null;

    #[ORM\Column(length: 128)]
    private ?string $kategorie = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Zadavatel $zadavatel = null;

    #[ORM\Column(length: 128)]
    private ?string $stavLikvidace = null;

    #[ORM\Column(length: 128)]
    private ?string $prohlidkaTyp = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $prohlidkaDatum = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $prohlidkaMisto = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $datumPrijetiOdPojistovny = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $datumVznikuSkody = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $datumUkonceniLikvidace = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $rozsahPojisteni = null;

    #[ORM\Column(length: 64, nullable: true)]
    private ?string $zpusobOpravy = null;

    #[ORM\Column(nullable: true)]
    private ?int $castkaPojistna = 0;

    #[ORM\Column(nullable: true)]
    private ?int $castkaZpusobOpravy = 0;

    #[ORM\Column(nullable: true)]
    private ?int $castkaSpoluucast = 0;

    #[ORM\Column(nullable: true)]
    private ?int $castkaLimitRiziko = 0;

    #[ORM\Column(nullable: true)]
    private ?bool $vinkulace = null;

    #[ORM\Column(nullable: true)]
    private ?bool $regres = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $poznamka = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $servisNazev = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $servisCisloUctu = null;

    #[ORM\Column(nullable: true)]
    private ?bool $stanoveniPravnihoZakladu = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $typVPP = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $typPojisteni = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $likvidator = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $datumNahlaseniSkody = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $datumVytvoreni = null;

    #[ORM\Column]
    private ?bool $archive = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $typFotodokumentace = null;

    #[ORM\Column(length: 200, nullable: true)]
    private ?string $cisloPojistneSmlouvy = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $mistoPu;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $popisVzniku = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $rozsahPoskozeni = null;

    /**
     * @var Collection<int, Ucastnik>
     */
    #[ORM\OneToMany(targetEntity: Ucastnik::class, mappedBy: 'pojistnaUdalost')]
    private Collection $ucastnici;

    /**
     * @var Collection<int, Poznamka>
     */
    #[ORM\OneToMany(targetEntity: Poznamka::class, mappedBy: 'pojistnaUdalost')]
    private Collection $poznamky;

    /**
     * @var Collection<int, Slozka>
     */
    #[ORM\OneToMany(targetEntity: Slozka::class, mappedBy: 'pojistnaUdalost', orphanRemoval: true)]
    private Collection $slozky;

    /**
     * @var Collection<int, LikvidacniProtokol>
     */
    #[ORM\OneToMany(targetEntity: LikvidacniProtokol::class, mappedBy: 'pojistnaUdalost')]
    private Collection $likvidacniProtokoly;

    /**
     * @var Collection<int, User>
     */
    #[ORM\ManyToMany(targetEntity: User::class)]
    private Collection $dalsiUzivatele;

    /**
     * @var Collection<int, Prohlidka>
     */
    #[ORM\OneToMany(targetEntity: Prohlidka::class, mappedBy: 'pojistnaUdalost', orphanRemoval: true)]
    private Collection $prohlidky;

    /**
     * @var Collection<int, VyzadanyDokument>
     */
    #[ORM\OneToMany(targetEntity: VyzadanyDokument::class, mappedBy: 'pojistnaUdalost')]
    private Collection $vyzadaneDokumenty;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $regresName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $regresTel = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $regresAdresa = null;

    #[ORM\Column(nullable: true)]
    private ?int $castkaRezerva = null;
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $PSNazev = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $PSPlatnostOd = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $PSMistoPojisteni = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $PSZPojisteni = null;

    #[ORM\ManyToOne]
    private ?Rizika $PSZRizika = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $PSVariantaPojisteni = null;

    #[ORM\ManyToOne]
    private ?PojistnePodminky $PSVPP = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $PSPojisteno = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $PSLimitRizikoNa = null;

    /**
     * @var Collection<int, LimitRizika>
     */
    #[ORM\OneToMany(targetEntity: LimitRizika::class, mappedBy: 'pojistnaUdalost')]
    private Collection $limityRizika;

    public function __construct(
        ?Zadavatel $zadavatel,
        ?string $cisloPojistnaUdalost,
        ?string $cisloSkodniUdalost,
        ?string $kategorie,
        ?string $stavLikvidace,
        ?string $prohlidkaTyp,
        ?string $prohlidkaMisto,
        ?\DateTimeInterface $datumVznikuSkody,
        ?\DateTimeInterface $datumUkonceniLikvidace,
        ?string $rozsahPojisteni,
        ?string $poznamka,
        ?string $mistoPojistneudalosti,
        ?\DateTimeImmutable $datumNahlaseniSkody,
        ?string $popisVzniku,
        ?string $rozsahPoskozeni,
        ?string $cisloPojistneSmlouvy
    ) {
        $this->zadavatel = $zadavatel;
        $this->cisloPojistnaUdalost = $cisloPojistnaUdalost;
        $this->cisloSkodniUdalost = $cisloSkodniUdalost;
        $this->kategorie = $kategorie;
        $this->stavLikvidace = $stavLikvidace;
        $this->prohlidkaTyp = $prohlidkaTyp;
        $this->prohlidkaMisto = $prohlidkaMisto;
        $this->datumVznikuSkody = $datumVznikuSkody;
        $this->datumUkonceniLikvidace = $datumUkonceniLikvidace;
        $this->rozsahPojisteni = $rozsahPojisteni;
        $this->poznamka = $poznamka;
        $this->datumVytvoreni = new \DateTimeImmutable();
        $this->mistoPu =  $mistoPojistneudalosti;

        $this->datumNahlaseniSkody =  $datumNahlaseniSkody;
        $this->popisVzniku =  $popisVzniku;
        $this->rozsahPoskozeni =  $rozsahPoskozeni;

        $this->cisloPojistneSmlouvy = $cisloPojistneSmlouvy;

        $this->ucastnici = new ArrayCollection();
        $this->poznamky = new ArrayCollection();
        $this->slozky = new ArrayCollection();

        $this->archive = false;
        $this->likvidacniProtokoly = new ArrayCollection();
        $this->dalsiUzivatele = new ArrayCollection();
        $this->prohlidky = new ArrayCollection();
        $this->vyzadaneDokumenty = new ArrayCollection();
        $this->limityRizika = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCisloPojistnaUdalost(): ?string
    {
        return $this->cisloPojistnaUdalost;
    }

    public function getCisloSkodniUdalost(): ?string
    {
        return $this->cisloSkodniUdalost;
    }

    public function getKategorie(): ?string
    {
        return $this->kategorie;
    }

    public function getZadavatel(): ?Zadavatel
    {
        return $this->zadavatel;
    }

    public function getStavLikvidace(): ?string
    {
        return $this->stavLikvidace;
    }

    // added when created new Enum
    public function getStavLikvidaceAsEnum(): StavLikvidace
    {
        return StavLikvidace::fromString($this->stavLikvidace);
    }

    public function defineStavLikvidaceFromEnum(StavLikvidace $stav): self
    {
        $this->stavLikvidace = $stav->value;
        return $this;
    }


    public function getProhlidkaTyp(): ?string
    {
        return $this->prohlidkaTyp;
    }

    public function getProhlidkaDatum(): ?\DateTimeInterface
    {
        return $this->prohlidkaDatum;
    }

    public function getProhlidkaMisto(): ?string
    {
        return $this->prohlidkaMisto;
    }

    public function getDatumPrijeti(): ?\DateTimeInterface
    {
        return $this->datumPrijetiOdPojistovny;
    }

    public function getDatumVzniku(): ?\DateTimeInterface
    {
        return $this->datumVznikuSkody;
    }

    public function getDatumUkonceniLikvidace(): ?\DateTimeInterface
    {
        return $this->datumUkonceniLikvidace;
    }

    public function getRozsahPojisteni(): ?string
    {
        return $this->rozsahPojisteni;
    }

    public function getZpusobOpravy(): ?string
    {
        return $this->zpusobOpravy;
    }

    public function getCastkaPojistna(): ?int
    {
        return $this->castkaPojistna;
    }

    public function getCastkaZpusobOpravy(): ?int
    {
        return $this->castkaZpusobOpravy;
    }

    public function getCastkaSpoluucast(): ?int
    {
        return $this->castkaSpoluucast;
    }

    public function getCastkaLimitRiziko(): ?int
    {
        return $this->castkaLimitRiziko;
    }

    public function isVinkulace(): ?bool
    {
        return $this->vinkulace;
    }

    public function isRegres(): ?bool
    {
        return $this->regres;
    }

    public function getPoznamka(): ?string
    {
        return $this->poznamka;
    }

    public function getServisNazev(): ?string
    {
        return $this->servisNazev;
    }

    public function getServisCisloUctu(): ?string
    {
        return $this->servisCisloUctu;
    }

    public function isStanoveniPravnihoZakladu(): ?bool
    {
        return $this->stanoveniPravnihoZakladu;
    }

    public function getTypVPP(): ?string
    {
        return $this->typVPP;
    }

    public function getTypPojisteni(): ?string
    {
        return $this->typPojisteni;
    }

    public function getTypFotodokumentace(): ?string
    {
        return $this->typFotodokumentace;
    }
    public function getTypFotodokumentaceFullString(): string
    {
        $result = '';
        switch ($this->getTypFotodokumentace()) {
            case self::FOTODOKUMENTACE_NE:
                $result = 'Ne';
                break;
            case self::FOTODOKUMENTACE_ANO_KLIENT:
                $result = 'Ano - Klient';
                break;
            case self::FOTODOKUMENTACE_ANO_TECHNIK:
                $result = 'Ano - Technik';
                break;
        }
        return $result;
    }

    public function getLikvidator(): ?User
    {
        return $this->likvidator;
    }

    public function getDatumNahlaseniSkody(): ?\DateTimeImmutable
    {
        return $this->datumNahlaseniSkody;
    }

    public function getDatumVytvoreni(): ?\DateTimeImmutable
    {
        return $this->datumVytvoreni;
    }

    public function getkategorieFullString(): string
    {
        $result = '';
        switch ($this->getKategorie()) {
            case self::CATEGORY_HAV:
                $result = 'Auto - HAV';
                break;
            case self::CATEGORY_MAJ:
                $result = 'Majetek';
                break;
            case self::CATEGORY_POV:
                $result = 'Auto - POV';
                break;
            case self::CATEGORY_ODP:
                $result = 'Odpovědnost';
                break;
        }
        return $result;
    }

    /**
     * @deprecated Use getStavLikvidaceLabel() instead
     */
    public function getStavLikvidaceFullString(): string
    {
        $result = '';
        switch ($this->getStavLikvidace()) {
            case self::LIKVIDACE_STAV_PRIJATO:
                $result = 'Přijato';
                break;
            case self::LIKVIDACE_STAV_PROBIHA:
                $result = 'Probíhá';
                break;
            case self::LIKVIDACE_STAV_UZAVRENO:
                $result = 'Uzavřeno';
                break;
            case self::LIKVIDACE_STAV_CEKAME:
                $result = 'Čekáme na podklady';
                break;
            case self::LIKVIDACE_STAV_PROHLIDKA:
                $result = 'Prohlídka';
                break;
            case self::LIKVIDACE_STAV_ROZPOCET:
                $result = 'Rozpočet';
                break;
            case self::LIKVIDACE_STAV_POSTUP:
                $result = 'Postup';
                break;
            case self::LIKVIDACE_STAV_POZADAVEK:
                $result = 'Požadavek';
                break;
        }
        return $result;
    }

    public function getStavLikvidaceLabel(): string
    {
        return $this->getStavLikvidaceAsEnum()->getLabel();
    }

    public function close(): self
    {
        $this->defineStavLikvidaceFromEnum(StavLikvidace::UZAVRENO);
        $this->datumUkonceniLikvidace = new DateTime();
        return $this;
    }

    public function open(): self
    {
        $this->defineStavLikvidaceFromEnum(StavLikvidace::PROBIHA);
        $this->datumUkonceniLikvidace = null;
        return $this;
    }

    public function isUzavreno(): bool
    {
        return $this->getStavLikvidaceAsEnum() === StavLikvidace::UZAVRENO;
    }

    public function isProbiha(): bool
    {
        return $this->getStavLikvidaceAsEnum() === StavLikvidace::PROBIHA;
    }

    public function isPrijato(): bool
    {
        return $this->getStavLikvidaceAsEnum() === StavLikvidace::PRIJATO;
    }


    public function isArchive(): ?bool
    {
        return $this->archive;
    }

    public function addToArchive(): static
    {
        $this->archive = true;

        return $this;
    }

    public function removeFromArchive(): self
    {
        $this->archive = false;
        return $this;
    }

    public function getArchive(): bool
    {
        return $this->archive;
    }

    /**
     * @return Collection<int, Ucastnik>
     */
    public function getUcastnici(): Collection
    {
        return $this->ucastnici;
    }

    public function addUcastnik(Ucastnik $ucastnik): static
    {
        if (!$this->ucastnici->contains($ucastnik)) {
            $this->ucastnici->add($ucastnik);
            $ucastnik->setPojistnaUdalost($this);
        }

        return $this;
    }

    public function removeUcastnik(Ucastnik $ucastnik): static
    {
        if ($this->ucastnici->removeElement($ucastnik)) {
            // set the owning side to null (unless already changed)
            if ($ucastnik->getPojistnaUdalost() === $this) {
                $ucastnik->setPojistnaUdalost(null);
            }
        }

        return $this;
    }

    public function modifyLikvidator(\App\Dto\PojistnaUdalost\PojistnaUdalostLikvidatorEditInput $liEditInput): PojistnaUdalost
    {
        $this->likvidator = $liEditInput->likvidator;

        return $this;
    }

    public function modify(PojistnaUdalostEditInput $puEditInput): PojistnaUdalost
    {
        $this->zadavatel = $puEditInput->zadavatel;
        $this->kategorie = $puEditInput->kategorie;

        $this->cisloPojistnaUdalost = $puEditInput->cisloPojistnaUdalost;
        $this->cisloSkodniUdalost = $puEditInput->cisloSkodniUdalost;
        $this->cisloPojistneSmlouvy =  $puEditInput->cisloPojistneSmlouvy;

        $this->datumVznikuSkody = $puEditInput->datumVznikuSkody;
        $this->datumNahlaseniSkody =  $puEditInput->datumNahlaseniSkody;
        $this->mistoPu =  $puEditInput->mistoPu;

        $this->rozsahPoskozeni =   $puEditInput->rozsahPoskozeni;
        $this->popisVzniku =  $puEditInput->popisVzniku;

        $this->stavLikvidace = $puEditInput->stavLikvidace;


        return $this;
    }


    /**
     * @return Collection<int, Poznamka>
     */
    public function getPoznamky(): Collection
    {
        return $this->poznamky;
    }

    public function addPoznamky(Poznamka $poznamky): static
    {
        if (!$this->poznamky->contains($poznamky)) {
            $this->poznamky->add($poznamky);
            $poznamky->modifyPojistnaUdalost($this);
        }

        return $this;
    }

    public function removePoznamky(Poznamka $poznamky): static
    {
        if ($this->poznamky->removeElement($poznamky)) {
            // set the owning side to null (unless already changed)
            if ($poznamky->getPojistnaUdalost() === $this) {
                $poznamky->modifyPojistnaUdalost(null);
            }
        }

        return $this;
    }
    public function getSlozkaRoot(): ?Slozka
    {

        $slozky = $this->getSlozky();
        foreach ($slozky as $one_slozka) {
            if ($one_slozka->getDruhSlozky() == Slozka::DRUH_SLOZKY_ROOT) {
                return $one_slozka;
            }
        }

        throw new SlozkaNotFoundException("Root slozka pro pojistnou udalost c." . $this->getId() . ' neexistuje');
    }

    public function getSlozkaPojistovnaZadani(): ?Slozka
    {
        $slozky = $this->getSlozky();
        foreach ($slozky as $one_slozka) {
            if ($one_slozka->getDruhSlozky() == Slozka::DRUH_SLOZKY_POJISTOVNA_ZADANI) {
                return $one_slozka;
            }
        }
        throw new SlozkaNotFoundException("Slozka POJISTOVNA_ZADANI pro pojistnou udalost c." . $this->getId() . ' neexistuje');
    }


    public function getSlozkaKlient(): ?Slozka
    {
        $slozky = $this->getSlozky();
        foreach ($slozky as $one_slozka) {
            if ($one_slozka->getDruhSlozky() == Slozka::DRUH_SLOZKY_KLIENT) {
                return $one_slozka;
            }
        }
        throw new SlozkaNotFoundException("Slozka KLIENT pro pojistnou udalost c." . $this->getId() . ' neexistuje');
    }


    public function getSlozkaTechnik(): ?Slozka
    {
        $slozky = $this->getSlozky();
        foreach ($slozky as $one_slozka) {
            if ($one_slozka->getDruhSlozky() == Slozka::DRUH_SLOZKY_TECHNIK) {
                return $one_slozka;
            }
        }
        throw new SlozkaNotFoundException("Slozka TECHNIK pro pojistnou udalost c." . $this->getId() . ' neexistuje');
    }

    public function getSlozkaUkoly(): ?Slozka
    {
        $slozky = $this->getSlozky();
        foreach ($slozky as $one_slozka) {
            if ($one_slozka->getDruhSlozky() == Slozka::DRUH_SLOZKY_UKOLY) {
                return $one_slozka;
            }
        }
        throw new SlozkaNotFoundException("Slozka pro UKOLY pro pojistnou udalost c." . $this->getId() . ' neexistuje');
    }


    public function getSlozkaPojistovnaUkonceni(): ?Slozka
    {

        $slozky = $this->getSlozky();
        foreach ($slozky as $one_slozka) {
            if ($one_slozka->getDruhSlozky() == Slozka::DRUH_SLOZKY_POJISTOVNA_UKONCENI) {
                return $one_slozka;
            }
        }
        throw new SlozkaNotFoundException("Slozka POJISTOVNA_UKONCENI pro pojistnou udalost c." . $this->getId() . ' neexistuje');
    }

    public function isCarAllowed(): bool
    {
        return in_array($this->kategorie, [self::CATEGORY_HAV, self::CATEGORY_POV]);
    }

    public function hasPojistenyParticipant(): bool
    {
        foreach ($this->ucastnici as $ucastnik) {
            if ($ucastnik->isPojisteny()) {
                return true;
            }
        }
        return false;
    }

    public function getPojistenyParticipant(): ?Ucastnik
    {
        foreach ($this->ucastnici as $ucastnik) {
            if ($ucastnik->isPojisteny()) {
                return $ucastnik;
            }
        }
        return null;
    }

    /**
     * Return first Poskozeny participant
     * 
     * @return null|Ucastnik 
     */
    public function getPoskozenyUcastnik(): ?Ucastnik
    {
        foreach ($this->ucastnici as $ucastnik)
            if ($ucastnik->isPoskozeny())
                return $ucastnik;
        return null;
    }

    /**
     * @return Collection<int, Slozka>
     */
    public function getSlozky(): Collection
    {
        return $this->slozky;
    }

    public function addSlozka(Slozka $slozka): static
    {
        if (!$this->slozky->contains($slozka)) {
            $this->slozky->add($slozka);
        }

        return $this;
    }

    public function removeSlozka(Slozka $slozka): static
    {
        $this->slozky->removeElement($slozka);

        return $this;
    }

    public function isMajetekCategory(): bool
    {
        return $this->kategorie === self::CATEGORY_MAJ;
    }

    public function isHavarijniCategory(): bool
    {
        return $this->kategorie === self::CATEGORY_HAV;
    }

    public function isPovinneRuceniCategory(): bool
    {
        return $this->kategorie === self::CATEGORY_POV;
    }

    public function getCisloPojistneSmlouvy(): ?string
    {
        return $this->cisloPojistneSmlouvy;
    }

    /**
     * @return array<string>
     */
    public static function getCategories(): array
    {
        return [
            SELF::CATEGORY_HAV,
            SELF::CATEGORY_POV,
            SELF::CATEGORY_MAJ,
            SELF::CATEGORY_ODP,
        ];
    }

    public function getMistoPu(): ?string
    {
        return $this->mistoPu;
    }

    /**
     * @return Collection<int, LikvidacniProtokol>
     */
    public function getLikvidacniProtokoly(): Collection
    {
        return $this->likvidacniProtokoly;
    }

    /**
     * @return Collection<int, User>
     */
    public function getDalsiUzivatele(): Collection
    {
        return $this->dalsiUzivatele;
    }

    public function addDalsiUzivatele(User $dalsiUzivatele): static
    {
        if (!$this->dalsiUzivatele->contains($dalsiUzivatele)) {
            $this->dalsiUzivatele->add($dalsiUzivatele);
        }

        return $this;
    }

    public function removeDalsiUzivatele(User $dalsiUzivatele): static
    {
        $this->dalsiUzivatele->removeElement($dalsiUzivatele);

        return $this;
    }

    /**
     * Auto set when new record created
     */
    #[ORM\PrePersist]
    public function setDatumPrijetiOdPojistovny(): self
    {
        $this->datumPrijetiOdPojistovny = new DateTimeImmutable();

        return $this;
    }

    /**
     * Get the value of popisVzniku
     */
    public function getPopisVzniku(): ?string
    {
        return $this->popisVzniku;
    }

    /**
     * Get the value of rozsahPoskozeni
     */
    public function getRozsahPoskozeni(): ?string
    {
        return $this->rozsahPoskozeni;
    }

    /**
     * @return Collection<int, Prohlidka>
     */
    public function getProhlidky(): Collection
    {
        return $this->prohlidky;
    }

    public function modifyAdditionalInfo(PojistnaUdalostAdditionalInfoEditInput $additionalInfoInput): self
    {
        $this->popisVzniku = $additionalInfoInput->popisVzniku;
        $this->rozsahPoskozeni = $additionalInfoInput->rozsahPoskozeni;
        $this->zpusobOpravy = $additionalInfoInput->zpusobOpravy;
        $this->regres = $additionalInfoInput->regres;
        $this->castkaRezerva = $additionalInfoInput->getCastkaRezerva();
        
        if (!$additionalInfoInput->regres) {
            $this->regresName =  null;
            $this->regresTel =  null;
            $this->regresAdresa =  null;
        } else {
            $this->regresName =  $additionalInfoInput->regresName;
            $this->regresTel =  $additionalInfoInput->regresTel;
            $this->regresAdresa =  $additionalInfoInput->regresAdresa;
        }

        return $this;
    }

    public function getRegresName(): ?string
    {
        return $this->regresName;
    }

    public function getRegresTel(): ?string
    {
        return $this->regresTel;
    }

    public function getRegresAdresa(): ?string
    {
        return $this->regresAdresa;
    }

    public function getCastkaRezerva(): ?int
    {
        return $this->castkaRezerva;
    }

    /**
     * @return Collection<int, VyzadanyDokument>
     */
    public function getVyzadaneDokumenty(): Collection
    {
        return $this->vyzadaneDokumenty;
    }

    public function addVyzadanyDokument(VyzadanyDokument $vyzadanyDokument): static
    {
        if (!$this->vyzadaneDokumenty->contains($vyzadanyDokument)) {
            $this->vyzadaneDokumenty->add($vyzadanyDokument);
            $vyzadanyDokument->modifyPojistnaUdalost($this);
        }

        return $this;
    }

    public function removeVyzadanyDokument(VyzadanyDokument $vyzadanyDokument): static
    {
        if ($this->vyzadaneDokumenty->removeElement($vyzadanyDokument)) {
            // set the owning side to null (unless already changed)
            if ($vyzadanyDokument->getPojistnaUdalost() === $this) {
                $vyzadanyDokument->modifyPojistnaUdalost(null);
            }
        }
        return $this;
    }

    public function getPSNazev(): ?string
    {
        return $this->PSNazev;
    }

    public function getPSPlatnostOd(): ?\DateTimeImmutable
    {
        return $this->PSPlatnostOd;
    }

    public function getPSMistoPojisteni(): ?string
    {
        return $this->PSMistoPojisteni;
    }

    public function getPSZPojisteni(): ?string
    {
        return $this->PSZPojisteni;
    }

    public function getPSZRizika(): ?Rizika
    {
        return $this->PSZRizika;
    }

    public function getPSVariantaPojisteni(): ?string
    {
        return $this->PSVariantaPojisteni;
    }

    public function getPSVPP(): ?PojistnePodminky
    {
        return $this->PSVPP;
    }

    public function getPSPojisteno(): ?string
    {
        return $this->PSPojisteno;
    }

    public function getPSPojistenoDetailLabel():string
    {
        if (is_null($this->PSPojisteno)) 
        {
            return '';
        }
        
        return PojistenaRizika::getDetailLabel($this->PSPojisteno);
    }

    /**
     * Get the value of PSLimitRizikoNa
     */
    public function getPSLimitRizikoNa(): ?string
    {
        return $this->PSLimitRizikoNa;
    }


    public function modifyPojistnaSmlouvaInfo(PojistnaSmlouvaEditInput $pojistnaSmlouvaEditInput): self
    {
        $this->PSNazev = $pojistnaSmlouvaEditInput->PSNazev;
        $this->cisloPojistneSmlouvy = $pojistnaSmlouvaEditInput->cisloPojistneSmlouvy;
        $this->PSPlatnostOd = $pojistnaSmlouvaEditInput->PSPlatnostOd;
        $this->PSMistoPojisteni = $pojistnaSmlouvaEditInput->PSMistoPojisteni;
        $this->PSZPojisteni = $pojistnaSmlouvaEditInput->PSZPojisteni;
        $this->castkaPojistna = $pojistnaSmlouvaEditInput->castkaPojistna ?? 0;
        $this->castkaSpoluucast = $pojistnaSmlouvaEditInput->castkaSpoluucast ?? 0;
        $this->PSZRizika = $pojistnaSmlouvaEditInput->PSZRizika;
        $this->castkaLimitRiziko = $pojistnaSmlouvaEditInput->castkaLimitRiziko ?? 0;
        $this->PSVariantaPojisteni = $pojistnaSmlouvaEditInput->PSVariantaPojisteni ?? '';
        $this->PSVPP = $pojistnaSmlouvaEditInput->PSVPP;
        if (empty($pojistnaSmlouvaEditInput->PSPojisteno))
        {
            $this->PSPojisteno = null;
        } else
        {
            $this->PSPojisteno = $pojistnaSmlouvaEditInput->PSPojisteno[0];
        }
        $this->PSLimitRizikoNa =  $pojistnaSmlouvaEditInput->PSLimitRizikoNa;


        return $this;
    }

    /**
     * @return Collection<int, LimitRizika>
     */
    public function getLimityRizika(): Collection
    {
        return $this->limityRizika;
    }
}
