<?php

namespace App\Entity;

use App\Dto\Zadavatel\ZadavatelEditInput;
use App\Repository\ZadavatelRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ZadavatelRepository::class)]
#[ORM\Table(name: "zadavatel")]
class Zadavatel
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: "integer")]
    private int $id;

    #[ORM\Column(type: "string", length: 255)]
    private string $nazevPojistovny;

    #[ORM\Column(type: "string", length: 255)]
    private string $kontaktniOsoba;

    #[ORM\Column(type: "string", length: 20)]
    private string $kontaktniTelefon;

    #[ORM\Column(type: "string", length: 255)]
    private string $kontaktniEmail;

    public function __construct(string $nazevPojistovny, string $kontaktniOsoba, string $kontaktniTelefon, string $kontaktniEmail)
    {
        $this->nazevPojistovny = $nazevPojistovny;
        $this->kontaktniOsoba = $kontaktniOsoba;
        $this->kontaktniTelefon = $kontaktniTelefon;
        $this->kontaktniEmail = $kontaktniEmail;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getNazevPojistovny() : string
    {
        return $this->nazevPojistovny;
    }

    public function getkontaktniOsoba() : string
    {
        return $this->kontaktniOsoba;
    }

    public function getKontaktniTelefon() : string
    {
        return $this->kontaktniTelefon;
    }

    public function getKontaktniEmail() : string
    {
        return $this->kontaktniEmail;
    }

    public function modify(ZadavatelEditInput $zadavatelEditInput): Zadavatel
    {
        $this->nazevPojistovny = $zadavatelEditInput->nazevPojistovny;
        $this->kontaktniOsoba = $zadavatelEditInput->kontaktniOsoba;
        $this->kontaktniTelefon = $zadavatelEditInput->kontaktniTelefon;
        $this->kontaktniEmail = $zadavatelEditInput->kontaktniEmail;
        

        return $this;
    }
}
