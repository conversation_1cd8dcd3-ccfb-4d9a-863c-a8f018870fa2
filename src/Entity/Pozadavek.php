<?php

namespace App\Entity;

use App\Dto\Pozadavek\PozadavekEditInput;
use App\Repository\PozadavekRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PozadavekRepository::class)]
class Pozadavek
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $typPozadavku = null;

    public function __construct(?string $typPozadavku) {
        $this->typPozadavku = $typPozadavku;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTypPozadavku(): ?string
    {
        return $this->typPozadavku;
    }

    public function modify(PozadavekEditInput $pozadavekEditInput): self
    {
        $this->typPozadavku = $pozadavekEditInput->typPozadavku;

        return $this;
    }
}
