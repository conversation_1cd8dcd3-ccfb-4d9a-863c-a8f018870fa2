<?php

namespace App\Entity;

use App\Repository\PojistnePodminkySouborRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PojistnePodminkySouborRepository::class)]
class PojistnePodminkySoubor
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\OneToOne(inversedBy: 'pojistnePodminkySoubor', cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: false)]
    private PojistnePodminky $pojistnePodminky;

    #[ORM\Column(length: 255)]
    private ?string $filename;

    #[ORM\Column(length: 255)]
    private ?string $originalFilename;


    public function __construct(string $filename, string $originalFilename, PojistnePodminky $pojistnePodminky)
    {
        $this->changeNames($filename, $originalFilename);
        $this->pojistnePodminky =  $pojistnePodminky;
    }

    public function changeNames(string $newFilename, string $newOriginalFilename):void
    {
        $this->filename =  $newFilename;
        $this->originalFilename = $newOriginalFilename;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPojistnePodminky(): PojistnePodminky
    {
        return $this->pojistnePodminky;
    }

    public function getFilename(): string
    {
        return $this->filename;
    }

    public function getOriginalFilename(): string
    {
        return $this->originalFilename;
    }

    public function getMimeType():string
    {
        return 'application/pdf';
    }

}
