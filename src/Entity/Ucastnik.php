<?php

namespace App\Entity;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikEditInput;
use App\Enum\RoleUcastnika;
use App\Repository\UcastnikRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

#[ORM\Entity(repositoryClass: UcastnikRepository::class)]
class Ucastnik
{

    public const TYP_KLIENT = 'Klient';


    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'ucastnici')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PojistnaUdalost $pojistnaUdalost = null;

    #[ORM\Column(length: 32)]
    private ?string $roleUcastnika = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $jmeno = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $prijmeni = null;

    #[ORM\Column(length: 32, nullable: true)]
    private ?string $rodneCislo = null;

    #[ORM\Column(length: 32,  nullable: true)]
    private ?string $telefon = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $email = null;

    #[ORM\Column(length: 64, nullable: true)]
    private ?string $cisloUctu = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $poznamka = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $pojistovna = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $cisloSmlouvy = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $firma = null;

    #[ORM\Column(length: 128, nullable: true)]
    private ?string $ico = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $sidlo = null;

    /**
     * @var Collection<int, Vozidlo>
     */
    #[ORM\OneToMany(targetEntity: Vozidlo::class, mappedBy: 'ucastnik')]
    private Collection $vozidla;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $adresa = null;

    #[ORM\Column]
    private bool $platceDPH = false;

    #[ORM\Column (options: ["default" => false])]
    private bool $company = false;

    public function __construct(
        PojistnaUdalost $pojistnaUdalost,
        string          $roleUcastnika,
        ?string          $jmeno,
        ?string          $prijmeni,
        ?string          $adresa,
        ?string          $rodneCislo,
        ?string          $telefon,
        ?string          $email,
        ?string          $cisloUctu,
        ?string          $poznamka,
        ?string          $pojistovna,
        ?string          $cisloSmlouvy,
        ?string          $firma,
        ?string          $ico,
        ?string          $sidlo,
        bool             $company,        
        ?bool            $platceDPH = false,
    ) {
        $this->pojistnaUdalost = $pojistnaUdalost;
        $this->setRoleUcastnika($roleUcastnika);
        $this->jmeno = $jmeno;
        $this->prijmeni = $prijmeni;
        $this->rodneCislo = $rodneCislo;
        $this->telefon = $telefon;
        $this->email = $email;
        $this->cisloUctu = $cisloUctu;
        $this->poznamka = $poznamka;
        $this->pojistovna = $pojistovna;
        $this->cisloSmlouvy = $cisloSmlouvy;
        $this->firma = $firma;
        $this->ico = $ico;
        $this->sidlo = $sidlo;
        $this->vozidla = new ArrayCollection();
        $this->adresa = $adresa;
        $this->platceDPH = $platceDPH;
        $this->company = $company;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return Collection<int, Vozidlo>
     */
    public function getVozidla(): Collection
    {
        return $this->vozidla;
    }

    public function addVozidlo(Vozidlo $vozidlo): static
    {
        if (!$this->vozidla->contains($vozidlo)) {
            $this->vozidla->add($vozidlo);
            $vozidlo->setUcastnik($this);
        }

        return $this;
    }

    public function removeVozidlo(Vozidlo $vozidlo): static
    {
        if ($this->vozidla->removeElement($vozidlo)) {
            // set the owning side to null (unless already changed)
            if ($vozidlo->getUcastnik() === $this) {
                $vozidlo->setUcastnik(null);
            }
        }

        return $this;
    }

    public function getPojistnaUdalost(): ?PojistnaUdalost
    {
        return $this->pojistnaUdalost;
    }

    public function setPojistnaUdalost(?PojistnaUdalost $pojistnaUdalost): static
    {
        $this->pojistnaUdalost = $pojistnaUdalost;
        return $this;
    }

    public function getJmeno(): ?string
    {
        return $this->jmeno;
    }

    public function getPrijmeni(): ?string
    {
        return $this->prijmeni;
    }

    public function getRodneCislo(): ?string
    {
        return $this->rodneCislo;
    }

    public function getTelefon(): ?string
    {
        return $this->telefon;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getFirma(): ?string
    {
        return $this->firma;
    }

    public function getIco(): ?string
    {
        return $this->ico;
    }

    public function getSidlo(): ?string
    {
        return $this->sidlo;
    }

    public function getPojistovna(): ?string
    {
        return $this->pojistovna;
    }

    public function getCisloSmlouvy(): ?string
    {
        return $this->cisloSmlouvy;
    }

    public function getCisloUctu(): ?string
    {
        return $this->cisloUctu;
    }

    public function getPoznamka(): ?string
    {
        return $this->poznamka;
    }

    public function getRoleUcastnika(): ?string
    {
        return $this->roleUcastnika;
    }

    // added when created new Enum
    public function getRoleUcastnikaAsEnum(): RoleUcastnika
    {
        return RoleUcastnika::fromString($this->roleUcastnika);
    }

    public function defineRoleUcastnikaFromEnum(RoleUcastnika $roleUcastnika): self
    {
        $this->roleUcastnika = $roleUcastnika->value;
        return $this;
    }


    private function setRoleUcastnika(string|RoleUcastnika $roleUcastnika): static
    {
        if ($roleUcastnika instanceof RoleUcastnika) {
            $this->roleUcastnika = $roleUcastnika->value;
            return $this;
        }

        // Pokud je vstup string, zkontrolujeme, zda odpovídá některé z hodnot v enumu
        $validRoles = array_map(fn(RoleUcastnika $role) => $role->value, RoleUcastnika::cases());

        if (!in_array($roleUcastnika, $validRoles)) {
            throw new \InvalidArgumentException('Invalid role');
        }

        $this->roleUcastnika = $roleUcastnika;
        return $this;
    }

    public function isCompany(): bool
    {
        return $this->company;
    }    

    public function isPojisteny(): bool
    {
        return $this->roleUcastnika === RoleUcastnika::ROLE_POJISTENY->value;
    }

    public function isVinik(): bool
    {
        return $this->roleUcastnika === RoleUcastnika::ROLE_VINIK->value;
    }

    public function isPoskozeny(): bool
    {
        return $this->roleUcastnika === RoleUcastnika::ROLE_POSKOZENY->value;
    }

    public function isPoverenaOsoba(): bool
    {
        return $this->roleUcastnika === RoleUcastnika::ROLE_POVERENA_OSOBA->value;
    }

    public function getTyp(): ?string
    {
        return $this->roleUcastnika;
    }

    public function updateFromInput(UcastnikEditInput $ucastnikEditInput): static
    {
        $this->setRoleUcastnika($ucastnikEditInput->roleUcastnika);
        $this->jmeno = $ucastnikEditInput->jmeno;
        $this->prijmeni = $ucastnikEditInput->prijmeni;
        $this->rodneCislo = $ucastnikEditInput->rodneCislo;
        $this->telefon = $ucastnikEditInput->telefon;
        $this->email = $ucastnikEditInput->email;
        $this->cisloUctu = $ucastnikEditInput->cisloUctu;
        $this->poznamka = $ucastnikEditInput->poznamka;
        $this->pojistovna = $ucastnikEditInput->pojistovna;
        $this->cisloSmlouvy = $ucastnikEditInput->cisloSmlouvy;
        $this->firma = $ucastnikEditInput->firma;
        $this->ico = $ucastnikEditInput->ico;
        $this->sidlo = $ucastnikEditInput->sidlo;
        $this->platceDPH =  $ucastnikEditInput->platceDPH;
        $this->company =  $ucastnikEditInput->isCompany;

        return $this;
    }


    public function getFullName(): string
    {
        if ($this->isCompany())
        {
            return $this->firma;
        }
        return $this->jmeno . ' ' . $this->prijmeni;
    }

    public function getAdresa(): ?string
    {
        return $this->adresa;
    }

    public function isPlatceDPH(): ?bool
    {
        return $this->platceDPH;
    }
}
