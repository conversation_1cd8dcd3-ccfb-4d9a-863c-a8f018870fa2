<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

abstract class AbstractSlozkaBasedSoubor extends AbstractSoubor
{

    #[ORM\ManyToOne(targetEntity: Slozka::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected ?Slozka $slozka = null;

    public function getSlozka(): ?Slozka
    {
        return $this->slozka;
    }

    public function updateSlozka(Slozka $slozka): self
    {
        $this->slozka = $slozka;
        return $this;
    }

}
