<?php

namespace App\Controller\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Helper\FlashMessageHelper;
use App\Service\PojistnaUdalost\PojistnaUdalostCloseService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class PojistnaUdalostCloseController extends AbstractController
{
    #[Route('/pou/{id}/close', methods: ['POST'], name: 'app_pojistna_udalost_close')]
    public function __invoke(Request $request, PojistnaUdalostCloseService $pojistnaUdalostCloseService, PojistnaUdalost $pojistnaUdalost): Response
    {
        $submittedToken = $request->getPayload()->get('token');

        if ($this->isCsrfTokenValid('close-pu', $submittedToken)) {
            // ... do PU close actions
            $pojistnaUdalostCloseService->closePojistnaUdalost($pojistnaUdalost);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Pojistná událost byla uzavřena');

            return $this->redirectToRoute('app_pojistna_udalost_list');
        }

        $this->addFlash(FlashMessageHelper::TYPE_DANGER, 'CHYBA PRI ZPRACOVANI UZAVRENI PU');
        return $this->redirectToRoute('app_pojistna_udalost_list');
    }
}
