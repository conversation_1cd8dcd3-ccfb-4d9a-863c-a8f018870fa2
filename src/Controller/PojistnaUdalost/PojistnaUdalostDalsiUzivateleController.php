<?php

namespace App\Controller\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostDalsiUzivateleInput;
use App\Entity\PojistnaUdalost;
use App\Form\PojistnaUdalost\PojistnaUdalostDalsiUzivateleType;
use App\Helper\FlashMessageHelper;
use App\Service\PojistnaUdalost\PojistnaUdalostService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pou/{id}/dalsi-uzivatele', name: 'app_pojistna_udalost_dalsi_uzivatele')]
class PojistnaUdalostDalsiUzivateleController extends AbstractController
{
    public function __invoke(
        Request $request,
        PojistnaUdalostService $pojistnaUdalostService,
        PojistnaUdalost $id
    ): Response {
        $pojistnaUdalost = $id;
        
        // Create DTO from entity
        $dalsiUzivateleInput = PojistnaUdalostDalsiUzivateleInput::createFromPojistnaUdalost($pojistnaUdalost);
        
        // Create form
        $form = $this->createForm(PojistnaUdalostDalsiUzivateleType::class, $dalsiUzivateleInput);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            // Update the entity with selected users
            $pojistnaUdalostService->updateDalsiUzivatele($pojistnaUdalost, $dalsiUzivateleInput);
            
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Seznam uživatelů byl úspěšně aktualizován');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }
        
        return $this->render('pojistna_udalost/dalsi_uzivatele.html.twig', [
            'form' => $form->createView(),
            'pojistnaUdalost' => $pojistnaUdalost,
        ]);
    }
}
