<?php

namespace App\Controller\PojistnaUdalost\Ukonceni;

use App\Entity\User;
use App\Helper\FlashMessageHelper;
use App\Repository\LikvidacniProtokolRepository;
use App\Repository\PojistnaUdalostRepository;
use App\Service\Soubor\SouborCopyService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pou/{pouId}/ukonceni/protokol/{protokolId}/copy', name: 'app_likvidacni_protokol_copy', methods: ['GET'])]
class LikvidacniProtokolCopyController extends AbstractController
{
    public function __invoke(
        Request $request,
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        LikvidacniProtokolRepository $likvidacniProtokolRepository,
        SouborCopyService $souborCopyService,
        int $pouId,
        int $protokolId
    ): Response {
        $loggedInUser = $this->getUser();
        assert($loggedInUser instanceof User);

        // Najít pojistnou událost
        $pojistnaUdalost = $pojistnaUdalostRepository->findOneByIdAndUser($pouId, $loggedInUser);
        
        if (!$pojistnaUdalost) {
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Pojistná událost nebyla nalezena.');
            return $this->redirectToRoute('app_dashboard');
        }

        // Najít likvidační protokol a ověřit, že patří k dané PU
        $likvidacniProtokol = $likvidacniProtokolRepository->find($protokolId);
        
        if (!$likvidacniProtokol || $likvidacniProtokol->getPojistnaUdalost()->getId() !== $pojistnaUdalost->getId()) {
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Likvidační protokol nebyl nalezen nebo nepatří k této pojistné události.');
            return $this->redirectToRoute('app_pojistna_udalost_ukonceni', ['id' => $pouId]);
        }

        // Získat cílovou složku "Pro pojišťovnu"
        $targetSlozka = $pojistnaUdalost->getSlozkaPojistovnaUkonceni();
        
        if (!$targetSlozka) {
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Složka Pro pojišťovnu nebyla nalezena.');
            return $this->redirectToRoute('app_pojistna_udalost_ukonceni', ['id' => $pouId]);
        }

        try {
            // Zkopírovat soubor
            $newSoubor = $souborCopyService->copyLikvidacniProtokolToFolder($likvidacniProtokol, $targetSlozka);
            
            // Úspěšná zpráva
            $this->addFlash(
                FlashMessageHelper::TYPE_SUCCESS, 
                sprintf(
                    'Soubor "%s" byl úspěšně zkopírován do složky Pro pojišťovnu.',
                    $likvidacniProtokol->getOriginalFilename()
                )
            );
            
        } catch (\Exception $e) {
            // Chybová zpráva
            $this->addFlash(
                FlashMessageHelper::TYPE_ERROR, 
                sprintf(
                    'Nepodařilo se zkopírovat soubor "%s": %s',
                    $likvidacniProtokol->getOriginalFilename(),
                    $e->getMessage()
                )
            );
        }

        // Redirect zpět na ukončení PU
        return $this->redirectToRoute('app_pojistna_udalost_ukonceni', ['id' => $pouId]);
    }
}
