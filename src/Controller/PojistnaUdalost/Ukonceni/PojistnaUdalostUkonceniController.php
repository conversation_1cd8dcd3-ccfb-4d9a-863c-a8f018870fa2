<?php

namespace App\Controller\PojistnaUdalost\Ukonceni;

use App\Entity\User;
use App\Repository\LikvidacniProtokolRepository;
use App\Repository\PojistnaUdalostRepository;
use App\Service\FileBrowser\FileBrowserService;
use App\Service\PojistnaUdalost\PojistnaUdalostCloseValidationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pou/{id}/ukonceni', name: 'app_pojistna_udalost_ukonceni')]
class PojistnaUdalostUkonceniController extends AbstractController
{

    public function __invoke(
        Request $request,
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        LikvidacniProtokolRepository $likvidacniProtokolRepository,
        FileBrowserService $fileBrowserService,
        PojistnaUdalostCloseValidationService $closeValidationService,
        CsrfTokenManagerInterface $csrfTokenManager,
        int $id
    ): Response {
        $loggedInUser = $this->getUser();

        $pojistnaUdalost = $pojistnaUdalostRepository->findOneByIdAndUser($id, $loggedInUser);

        // Získání parametru řazení protokolů
        $lporder = $request->query->get('lporder', 'desc');
        if (!in_array(strtolower($lporder), ['asc', 'desc'])) {
            $lporder = 'desc';
        }

        // Načtení likvidačních protokolů s řazením
        $likvidacniProtokoly = $likvidacniProtokolRepository->findByPojistnaUdalostOrderedByDate(
            $pojistnaUdalost->getId(),
            $lporder
        );

        // Získání složky pro dokumenty pro pojišťovnu
        $slozkaUkonceni = $pojistnaUdalost->getSlozkaPojistovnaUkonceni();

        // Načtení obsahu složky
        $folderContent = $fileBrowserService->getFolderContent($slozkaUkonceni);
        $breadcrumb = $fileBrowserService->getBreadcrumb($slozkaUkonceni);

        assert($loggedInUser instanceof User); // PHPStan type assertion

        // Kontrola možnosti uzavření PU
        $canClose = $closeValidationService->canClosePojistnaUdalost($pojistnaUdalost, $loggedInUser);
        $validationErrors = $closeValidationService->getValidationErrors($pojistnaUdalost, $loggedInUser);

        // Generování CSRF tokenu pro uzavření PU
        $csrfToken = $csrfTokenManager->getToken('close-pu');

        return $this->render('pojistna_udalost/ukonceni/index.html.twig', [
            'pou' => $pojistnaUdalost,
            'submenu_page' => 'ukonceni',
            // Data pro file browser
            'slozka_ukonceni' => $slozkaUkonceni,
            'soubory_ukonceni' => $folderContent['soubory'],
            'podslozky_ukonceni' => $folderContent['podslozky'],
            'breadcrumb_ukonceni' => $breadcrumb,
            'file_browser_service' => $fileBrowserService,
            // Data pro likvidační protokoly
            'likvidacni_protokoly' => $likvidacniProtokoly,
            'lporder' => $lporder,
            // Data pro uzavření PU
            'can_close_pu' => $canClose,
            'close_validation_errors' => $validationErrors,
            'csrf_token' => $csrfToken
        ]);
    }
}
