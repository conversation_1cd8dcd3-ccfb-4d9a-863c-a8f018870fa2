<?php

namespace App\Controller\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostNewInput;
use App\Dto\PojistnaUdalost\Ucastnik\UcastnikNewInput;
use App\Enum\RoleUcastnika;
use App\Factory\PojistnaUdalost\PojistnaUdalostNewFactory;
use App\Factory\PojistnaUdalost\PojistnaUdalostSlozkaFactory;
use App\Factory\PojistnaUdalost\Ucastnik\UcastnikFactory;
use App\Form\PojistnaUdalost\PojistnaUdalostNewType;
use App\Helper\FlashMessageHelper;
use App\Service\PojistnaUdalost\Ucastnik\UcastnikNewInputValidationService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pou/new', name: 'app_pojistna_udalost_new')]
class PojistnaUdalostNewController extends AbstractController
{

    public function __construct(
        private UcastnikNewInputValidationService $ucastnikNewInputValidationService
    ) {}


    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        PojistnaUdalostNewFactory $pojistnaUdalostNewFactory,
        PojistnaUdalostSlozkaFactory $pojistnaUdalostSlozkaFactory,
        UcastnikFactory $ucastnikFactory
    ): Response {
        $pojistnaUdalostNewInput = new PojistnaUdalostNewInput();
        $form = $this->createForm(PojistnaUdalostNewType::class, $pojistnaUdalostNewInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Získání dat z formuláře
            /**
             * @var PojistnaUdalostNewInput
             */
            $pojistnaUdalostNewInput = $form->getData();

            $ucastniciAreValid = true;
            // validace pojsteneho - musi byt vzdy neco zadane

            $resultPojisteny = null;
            $resultPoskozeny = null;
            $resultPoverenaOsoba = null;
            $resultKontaktniOsoba = null;

            $resultPojisteny =  $this->validateUcastnikByRoleAndReturnDTO($pojistnaUdalostNewInput, $form, RoleUcastnika::ROLE_POJISTENY);
            if (!$resultPojisteny['valid']) {
                $ucastniciAreValid = false;
            }

            // validace ostatnich zaznamu nebo zkopirovani podle checkboxu
            // dalsi ucastniky validujeme jenom kdyz je prislusny checkbox nastaveny na zadavani udaji ruznych od pojisteneho

            if ($pojistnaUdalostNewInput->poskozeny_different_from_pojisteny) {
                $resultPoskozeny = $this->validateUcastnikByRoleAndReturnDTO($pojistnaUdalostNewInput, $form, RoleUcastnika::ROLE_POSKOZENY);
                if (!$resultPoskozeny['valid']) {
                    $ucastniciAreValid = false;
                }
            }


            if ($pojistnaUdalostNewInput->poverena_osoba_different_from_pojisteny) {
                $resultPoverenaOsoba = $this->validateUcastnikByRoleAndReturnDTO($pojistnaUdalostNewInput, $form, RoleUcastnika::ROLE_POVERENA_OSOBA);
                if (!$resultPoverenaOsoba['valid']) {
                    $ucastniciAreValid = false;
                }
            }

            if ($pojistnaUdalostNewInput->kontaktni_osoba_different_from_pojisteny) {
                $resultKontaktniOsoba = $this->validateUcastnikByRoleAndReturnDTO($pojistnaUdalostNewInput, $form, RoleUcastnika::ROLE_KONTAKTNI_OSOBA);
                if (!$resultKontaktniOsoba['valid']) {
                    $ucastniciAreValid = false;
                }
            }


            if (!$ucastniciAreValid) {
                $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Chyba ve formuláři');
                return $this->render('pojistna_udalost/new.html.twig', [
                    'form' => $form->createView(),
                ]);
            }

            // Vytvoření nové pojistné události a jejích souvisejících složek
            $newPojistnaUdalost = $pojistnaUdalostNewFactory->createPojistnaUdalost($pojistnaUdalostNewInput);
            $entityManager->persist($newPojistnaUdalost);
            $entityManager->flush();

            // persist all ucastniks data
            // Pojisteny always persist
            $newPojistenyUcastnik = $ucastnikFactory->createUcastnikFromInput(
                $resultPojisteny['dto'],
                $newPojistnaUdalost
            );           
            $entityManager->persist($newPojistenyUcastnik);


            // Poskozeny always persist
            if (!is_null($resultPoskozeny)) {
                $newPoskozenyUcastnik = $ucastnikFactory->createUcastnikFromInput(
                    $resultPoskozeny['dto'],
                    $newPojistnaUdalost
                );                
                
            } else
            {
                $newPoskozenyUcastnikDTO = new UcastnikNewInput();
                $newPoskozenyUcastnikDTO->assignUcastnikDataByRoleForNewPojistnaUdalostForm($pojistnaUdalostNewInput, RoleUcastnika::ROLE_POSKOZENY);
                $newPoskozenyUcastnik = $ucastnikFactory->createUcastnikFromInput(
                    $newPoskozenyUcastnikDTO,
                    $newPojistnaUdalost
                );   
            }
            $entityManager->persist($newPoskozenyUcastnik);

            if (!is_null($resultPoverenaOsoba)) {
                $newPoverenaOsobaUcastnik = $ucastnikFactory->createUcastnikFromInput(
                    $resultPoverenaOsoba['dto'],
                    $newPojistnaUdalost
                );                
                $entityManager->persist($newPoverenaOsobaUcastnik);
            }

            if (!is_null($resultKontaktniOsoba)) {
                $newKontaktniOsobaUcastnik = $ucastnikFactory->createUcastnikFromInput(
                    $resultKontaktniOsoba['dto'],
                    $newPojistnaUdalost
                );                
                $entityManager->persist($newKontaktniOsobaUcastnik);
            }            


            $rootFolder = $pojistnaUdalostSlozkaFactory->createRootSlozkaForPojistnaUdalost($newPojistnaUdalost);
            $pojistovnaZadaniSlozka = $pojistnaUdalostSlozkaFactory->createPojistovnaZadaniSlozkaForPojistnaUdalost($newPojistnaUdalost, $rootFolder);
            $pojistovnaUkonceniSlozka = $pojistnaUdalostSlozkaFactory->createPojistovnaUkonceniSlozkaForPojistnaUdalost($newPojistnaUdalost, $rootFolder);
            $klientSlozka = $pojistnaUdalostSlozkaFactory->createKlientSlozkaForPojistnaUdalost($newPojistnaUdalost, $rootFolder);
            $techSlozka = $pojistnaUdalostSlozkaFactory->createTechnikSlozkaForPojistnaUdalost($newPojistnaUdalost, $rootFolder);

            $entityManager->persist($rootFolder);
            $entityManager->persist($pojistovnaZadaniSlozka);
            $entityManager->persist($pojistovnaUkonceniSlozka);
            $entityManager->persist($klientSlozka);
            $entityManager->persist($techSlozka);

            $entityManager->flush();

            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Nová pojistná událost úspěšně přidána');
            return $this->redirectToRoute('app_pojistna_udalost_list');
        }

        return $this->render('pojistna_udalost/new.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    /**
     * 
     * Arrange validation of ucastnik data from main form 
     * 
     * @param PojistnaUdalostNewInput $pojistnaUdalostNewInput 
     * @param FormInterface $form 
     * @param RoleUcastnika $roleUcastnika 
     * @return array<mixed>
     */
    private function validateUcastnikByRoleAndReturnDTO(
        PojistnaUdalostNewInput $pojistnaUdalostNewInput,
        FormInterface $form,
        RoleUcastnika $roleUcastnika
    ): array {
        $ucastnikNewInput = new UcastnikNewInput();
        $result = [];

        switch ($roleUcastnika) {
            case RoleUcastnika::ROLE_KONTAKTNI_OSOBA:

                $ucastnikNewInput->assignUcastnikDataByRoleForNewPojistnaUdalostForm($pojistnaUdalostNewInput, RoleUcastnika::ROLE_KONTAKTNI_OSOBA);

                $result = $this->ucastnikNewInputValidationService->validateUcastnikByRoleForNewPojistnaUdalost(
                    $ucastnikNewInput,
                    RoleUcastnika::ROLE_KONTAKTNI_OSOBA
                );
                break;
            case RoleUcastnika::ROLE_POVERENA_OSOBA:

                $ucastnikNewInput->assignUcastnikDataByRoleForNewPojistnaUdalostForm($pojistnaUdalostNewInput, RoleUcastnika::ROLE_POVERENA_OSOBA);

                $result = $this->ucastnikNewInputValidationService->validateUcastnikByRoleForNewPojistnaUdalost(
                    $ucastnikNewInput,
                    RoleUcastnika::ROLE_POVERENA_OSOBA
                );
                break;
            case RoleUcastnika::ROLE_POSKOZENY:
                $ucastnikNewInput->assignUcastnikDataByRoleForNewPojistnaUdalostForm($pojistnaUdalostNewInput, RoleUcastnika::ROLE_POSKOZENY);

                $result = $this->ucastnikNewInputValidationService->validateUcastnikByRoleForNewPojistnaUdalost(
                    $ucastnikNewInput,
                    RoleUcastnika::ROLE_POSKOZENY
                );
                break;
            case RoleUcastnika::ROLE_POJISTENY:
                $ucastnikNewInput->assignUcastnikDataByRoleForNewPojistnaUdalostForm($pojistnaUdalostNewInput, RoleUcastnika::ROLE_POJISTENY);

                $result = $this->ucastnikNewInputValidationService->validateUcastnikByRoleForNewPojistnaUdalost(
                    $ucastnikNewInput,
                    RoleUcastnika::ROLE_POJISTENY
                );

                break;
        }


        if ($result[UcastnikNewInputValidationService::VALID_FIELD]) {
            // ucastnik data are valid
            return [
                'valid' => true,
                'dto' => $ucastnikNewInput
            ];
        }

        // add errors to form class
        foreach ($result[UcastnikNewInputValidationService::ERRORS_FIELD] as $oneError) {

            $error =  new FormError(mb_strtoupper($roleUcastnika->value) . ': ' . $oneError);
            $form->addError($error);
        }


        return [
            'valid' => false,
            'dto' => null
        ];
    }
}
