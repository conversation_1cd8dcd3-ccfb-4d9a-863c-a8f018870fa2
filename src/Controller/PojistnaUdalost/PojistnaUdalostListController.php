<?php

namespace App\Controller\PojistnaUdalost;

use App\Entity\User;
use App\Enum\StavLikvidace;
use App\Enum\PuListColumn;
use App\Repository\UserRepository;
use App\Repository\ZadavatelRepository;
use App\Service\PojistnaUdalost\FilterManager;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou', name: 'app_pojistna_udalost_list')]
class PojistnaUdalostListController extends AbstractController
{
    public function __invoke(
        Request $request,
        UserRepository $userRepository,
        ZadavatelRepository $zadavatelRepository,
        FilterManager $filterManager,
        PaginatorInterface $paginator
    ): Response {
        /** @var User */
        $user = $this->getUser();

        // Extract sort & direction from query string or set default
        $sort = $request->query->get('sort') ?? 'pou.id';
        $dir = strtoupper($request->query->get('direction')) === 'DESC' ? 'DESC' : 'ASC';

        // Define valid sort fields (must match DQL field paths)
        $validSortFields = [
            'pou.id',
            'pou.cisloPojistnaUdalost',
            'pou.cisloSkodniUdalost',
            'pou.datumVznikuSkody',
            'pou.datumPrijetiOdPojistovny',
            'pou.mistoPu',
        ];

        // Get the base query from FilterManager
        $query = $filterManager->getQuery($user, $request, $sort, $dir);

        // Paginate with sorting hints
        $pagination = $paginator->paginate(
            $query,
            $request->query->getInt('page', 1),
            50,
            [
                'defaultSortFieldName' => $sort,
                'defaultSortDirection' => $dir,
                'sortFieldWhitelist' => $validSortFields,
            ]
        );

        return $this->render('pojistna_udalost/list.html.twig', [
            'pagination' => $pagination,
            'categories' => $filterManager->getCategories($request),
            'dates' => $filterManager->getDates($request),
            'misc' => $filterManager->getMisc($request),
            'likvidatorList' => $userRepository->findLikvidatorsIncludingAdmins(),
            'technikList' => $userRepository->findTechniks(),
            'zadavatelList' => $zadavatelRepository->getAllQuery()->getResult(),
            'filterActive' => $filterManager->getActiveFilter($request),
            'columns' => PuListColumn::cases(),
            'stavy' => StavLikvidace::getStavyAsArray(),
        ]);
    }
}
