<?php

namespace App\Controller\PojistnaUdalost;

use App\Entity\Slozka;
use App\Repository\PojistnaUdalostRepository;
use App\Repository\PoznamkaRepository;
use App\Repository\SouborRepository;
use App\Repository\UcastnikRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PojistnaUdalostReadController extends AbstractController
{

    #[Route('/pou/{id}', name: 'app_pojistna_udalost_read')]
    public function __invoke(
        int $id,
        Request $request,
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        PoznamkaRepository $poznamkaRepository,
        UcastnikRepository $ucastnikRepository,
        SouborRepository $souborRepository
    ): Response {
        $sortOrder = $request->query->get('sortOrder', 'desc');
        $loggedInUser = $this->getUser();

        $pojistnaUdalost = $pojistnaUdalostRepository->findOneByIdAndUser($id, $loggedInUser);

        if (!$pojistnaUdalost) {
            throw $this->createNotFoundException('Pojistná událost nebyla nalezena nebo k ní nemáte přístup.');
        }

        $pojisteny = $ucastnikRepository->getPojistenyForPojistnaUdalost($pojistnaUdalost);
        
        // Získáme všechny poškozené, pověřené osoby a kontaktní osoby
        $poskozeni = $ucastnikRepository->getPoskozeniForPojistnaUdalost($pojistnaUdalost);
        $povereneOsoby = $ucastnikRepository->getPovereneOsobyForPojistnaUdalost($pojistnaUdalost);
        $kontaktniOsoby = $ucastnikRepository->getKontaktniOsobyForPojistnaUdalost($pojistnaUdalost);
        
        $slozkaOdPOjistnovny =  $pojistnaUdalost->getSlozkaPojistovnaZadani();
        $slozkaProPOjistnovnu =  $pojistnaUdalost->getSlozkaPojistovnaUkonceni();
        $slozkaOdKlienta = $pojistnaUdalost->getSlozkaKlient();
        $slozkaOdTechnika =  $pojistnaUdalost->getSlozkaTechnik();
        $souboryVeSlozkach [Slozka::DRUH_SLOZKY_POJISTOVNA_ZADANI] = $souborRepository->countBySlozka($slozkaOdPOjistnovny);
        $souboryVeSlozkach [Slozka::DRUH_SLOZKY_POJISTOVNA_UKONCENI] = $souborRepository->countBySlozka($slozkaProPOjistnovnu);
        $souboryVeSlozkach [Slozka::DRUH_SLOZKY_KLIENT] = $souborRepository->countBySlozka($slozkaOdKlienta);
        $souboryVeSlozkach [Slozka::DRUH_SLOZKY_TECHNIK] = $souborRepository->countBySlozka($slozkaOdTechnika);
        
        return $this->render('pojistna_udalost/read.html.twig', [
            'pou' => $pojistnaUdalost,
            'sortOrder' => $sortOrder,
            'pojisteny' => $pojisteny,
            'poskozeni' => $poskozeni,
            'povereneOsoby' => $povereneOsoby,
            'kontaktniOsoby' => $kontaktniOsoby,
            'soubory_ve_slozkach' => $souboryVeSlozkach
        ]);
    }
}
