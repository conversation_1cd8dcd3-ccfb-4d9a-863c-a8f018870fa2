<?php

namespace App\Controller\PojistnaUdalost;

use App\Dto\PojistnaUdalost\PojistnaUdalostEditInput;
use App\Entity\PojistnaUdalost;
use App\Helper\FlashMessageHelper;
use App\Form\PojistnaUdalost\PojistnaUdalostEditType;
use App\Service\PojistnaUdalost\PojistnaUdalostService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/edit', name: 'app_pojistna_udalost_edit')]
class PojistnaUdalostEditController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        PojistnaUdalostService $pojistnaUdalostService,        
        PojistnaUdalost $id
    ): Response {
        $pojistnaUdalost = $id;

        $pojistnaUdalostEditInput = PojistnaUdalostEditInput::createFromPojistnaUdalost($pojistnaUdalost);
        $form = $this->createForm(PojistnaUdalostEditType::class, $pojistnaUdalostEditInput);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            // Uložení změn v pojistné události
            $pojistnaUdalostService->updatePojistnaUdalost($pojistnaUdalost, $pojistnaUdalostEditInput);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Pojistná událost úspěšně upravena');
            return $this->redirectToRoute('app_pojistna_udalost_list', ['id' => $pojistnaUdalost->getId()]);
        }

        return $this->render('pojistna_udalost/edit.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}
