<?php

namespace App\Controller\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Entity\User;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pou/{id}/dalsi-uzivatele/remove/{userId}', name: 'app_pojistna_udalost_dalsi_uzivatele_remove')]
class PojistnaUdalostDalsiUzivateleRemoveController extends AbstractController
{
    public function __invoke(
        PojistnaUdalost $id,
        int $userId,
        EntityManagerInterface $entityManager
    ): Response {
        $pojistnaUdalost = $id;
        $userRepository = $entityManager->getRepository(User::class);
        $user = $userRepository->find($userId);
        
        if (!$user) {
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Uživatel nebyl nalezen');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }
        
        $pojistnaUdalost->removeDalsiUzivatele($user);
        $entityManager->flush();
        
        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Uživatel byl odebrán z pojistné události');
        return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
    }
}
