<?php

namespace App\Controller\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikVozidloNewInput;
use App\Entity\PojistnaUdalost;
use App\Entity\Ucastnik;
use App\Factory\PojistnaUdalost\Ucastnik\UcastnikVozidloFactory;
use App\Form\PojistnaUdalost\Ucastnik\UcastnikVozidloNewType;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('pou/{id}/ucastnikvozidlo/new', name: 'app_pojistna_udalost_ucastnik_vozidlo_new')]
class UcastnikVozidloNewController extends AbstractController
{
    public function __invoke(Request $request, EntityManagerInterface $entityManager, UcastnikVozidloFactory $ucastnikVozidloFactory, Ucastnik $ucastnik): Response
    {
        $ucastnikVozidloNewInput = new UcastnikVozidloNewInput();
        $pojistnaUdalost = $ucastnik->getPojistnaUdalost();
        
        if ($pojistnaUdalost->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Přidání vozidla k uzavřené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        $isCarAllowed = $pojistnaUdalost->isCarAllowed();

        if (!$isCarAllowed) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Přidání vozidla není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }
        
        $form = $this->createForm(UcastnikVozidloNewType::class, $ucastnikVozidloNewInput);
        
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $ucastnikVozidloNewInput = $form->getData();
        
            $newUcastnikVozidlo = $ucastnikVozidloFactory->createUcastnikVozidloFromInput($ucastnikVozidloNewInput, $ucastnik);
            $entityManager->persist($newUcastnikVozidlo);
            $entityManager->flush();
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Vozidlo přidáno úspěšně');

            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        return $this->render('pojistna_udalost/ucastnik/vozidlo/new.html.twig', [
            'form' => $form,
            'ucastnik' => $ucastnik,
        ]);
    }
}
