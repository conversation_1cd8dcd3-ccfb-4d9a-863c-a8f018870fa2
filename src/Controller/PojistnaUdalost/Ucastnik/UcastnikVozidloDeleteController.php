<?php

namespace App\Controller\PojistnaUdalost\Ucastnik;

use App\Entity\Vozidlo;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('pou/ucastnikvozidlo/delete/{id}', name: 'app_pojistna_udalost_ucastnik_vozidlo_delete')]
class UcastnikVozidloDeleteController extends AbstractController
{
    public function __invoke(EntityManagerInterface $entityManager, Vozidlo $vozidlo): Response
    {
        $ucastnik = $vozidlo->getUcastnik();
        $pojistnaUdalost = $ucastnik->getPojistnaUdalost();

        if ($pojistnaUdalost->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Odstranění vozidla k uzavřené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }
        
        $isCarAllowed = $pojistnaUdalost->isCarAllowed();

        if (!$isCarAllowed) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Odstranení vozidla není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }
        
        $entityManager->remove($vozidlo);
        $entityManager->flush();
        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Vozidlo úspěšně smazáno');
        return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
    }
}
