<?php

namespace App\Controller\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikEditInput;
use App\Entity\Ucastnik;
use App\Form\PojistnaUdalost\Ucastnik\UcastnikEditType;
use App\Helper\FlashMessageHelper;
use App\Service\PojistnaUdalost\Ucastnik\UcastnikUpdateService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class UcastnikEditController extends AbstractController
{
    #[Route('/pou/ucastnik/edit/{id}', name: 'app_pojistna_udalost_ucastnik_edit')]
    public function __invoke(Request $request, UcastnikUpdateService $ucastnikService, Ucastnik $ucastnik): Response
    {
        // Získání pojistné události spojené s účastníkem
        $pojistnaUdalost = $ucastnik->getPojistnaUdalost();

        if ($pojistnaUdalost->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Editace účastníka k uzavřené události není povolena.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        // Zjištění, zda pojistná událost již má účastníka s rolí klienta
        $isPojistenyDisabled = $pojistnaUdalost->hasPojistenyParticipant();
        // pokud ale potřebujeme editovat poji3t2n0ho
        if ($ucastnik->isPojisteny()) {
            $isPojistenyDisabled = false;
        }

        // Vytvoření vstupního DTO pro editaci z entity účastníka
        $ucastnikEditInput = UcastnikEditInput::createFromUcastnik($ucastnik);

        $form = $this->createForm(UcastnikEditType::class, $ucastnikEditInput, [
            'is_pojisteny_disabled' => $isPojistenyDisabled,
        ]);


        // Zpracování požadavku formuláře
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            // Aktualizace účastníka pomocí služby
            $ucastnikService->updateUcastnik($ucastnik, $ucastnikEditInput);

            // Přidání flash zprávy o úspěšném uložení
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Účastník úspěšně upraven');

            // Přesměrování na stránku detailu pojistné události
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        // Zobrazení šablony s formulářem
        return $this->render('pojistna_udalost/ucastnik/edit.html.twig', [
            'form' => $form->createView(),
            'pojistnaUdalost' => $pojistnaUdalost,
        ]);
    }
}
