<?php

namespace App\Controller\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikVozidloEditInput;
use App\Entity\PojistnaUdalost;
use App\Entity\Ucastnik;
use App\Entity\Vozidlo;
use App\Form\PojistnaUdalost\Ucastnik\UcastnikVozidloEditType;
use App\Helper\FlashMessageHelper;
use App\Service\PojistnaUdalost\Ucastnik\UcastnikVozidloUpdateService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('pou/ucastnikvozidlo/edit/{id}', name: 'app_pojistna_udalost_ucastnik_vozidlo_edit')]
class UcastnikVozidloEditController extends AbstractController
{
    public function __invoke(Request $request, EntityManagerInterface $entityManager, UcastnikVozidloUpdateService $ucastnikVozidloService, int $id): Response
    {
        // Fetch Vozidlo entity by ID
        $vozidlo = $entityManager->getRepository(Vozidlo::class)->find($id);

        if (!$vozidlo) {
            throw $this->createNotFoundException('Vozidlo s ID ' . $id . ' nebylo nalezeno.');
        }
        
        // Fetch related entities
        $ucastnik = $vozidlo->getUcastnik();
        $pojistnaUdalost = $ucastnik->getPojistnaUdalost();

        if ($pojistnaUdalost->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Editace vozidla k uzavřené události není povolena.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }
        
        $isCarAllowed = $pojistnaUdalost->isCarAllowed();

        if (!$isCarAllowed) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Editace vozidla není povolena.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }
        
        // Create form and handle request
        $ucastnikVozidloEditInput = UcastnikVozidloEditInput::createFromUcastnikVozidlo($vozidlo);
        $form = $this->createForm(UcastnikVozidloEditType::class, $ucastnikVozidloEditInput);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            // Update ucastnik vozidlo
            $ucastnikVozidloService->updateUcastnikVozidlo($vozidlo, $ucastnikVozidloEditInput);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Vozidlo úspěšně upraveno');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        return $this->render('pojistna_udalost/ucastnik/vozidlo/edit.html.twig', [
            'form' => $form->createView(),
            'pojistnaUdalost' => $pojistnaUdalost,
        ]);
    }
}
