<?php

namespace App\Controller\PojistnaUdalost\Ucastnik;


use App\Entity\Ucastnik;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('pou/ucastnik/delete/{id}', name: 'app_pojistna_udalost_ucastnik_delete')]
class UcastnikDeleteController extends AbstractController
{
    public function __invoke(Request $request, EntityManagerInterface $entityManager, Ucastnik $ucastnik): Response
    {
        $pojistnaUdalost = $ucastnik->getPojistnaUdalost();

        if ($pojistnaUdalost->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Odstranění účastníka k uzavřené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        $entityManager->remove($ucastnik);
        $entityManager->flush();

        return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
    }
}
