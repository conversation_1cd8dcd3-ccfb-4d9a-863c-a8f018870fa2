<?php

namespace App\Controller\PojistnaUdalost\Ucastnik;

use App\Dto\PojistnaUdalost\Ucastnik\UcastnikNewInput;
use App\Entity\PojistnaUdalost;
use App\Factory\PojistnaUdalost\Ucastnik\UcastnikFactory;
use App\Form\PojistnaUdalost\Ucastnik\UcastnikNewType;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/pou/{id}/ucastnik/new', name: 'app_pojistna_udalost_ucastnik_new')]
class UcastnikNewController extends AbstractController
{
    public function __invoke(Request $request, EntityManagerInterface $entityManager, UcastnikFactory $ucastnikFactory, PojistnaUdalost $pojistnaUdalost): Response
    {
        if ($pojistnaUdalost->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Přidání účastníka k uzavřené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        $isPojistenyDisabled = $pojistnaUdalost->hasPojistenyParticipant();

        $ucastnikNewInput = new UcastnikNewInput();
        $form = $this->createForm(UcastnikNewType::class, $ucastnikNewInput, [
            'is_pojisteny_disabled' => $isPojistenyDisabled,
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $ucastnikNewInput = $form->getData();
            $newUcastnik = $ucastnikFactory->createUcastnikFromInput($ucastnikNewInput, $pojistnaUdalost);
            $entityManager->persist($newUcastnik);
            $entityManager->flush();

            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Účastník byl úspěšně přidán.');

            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        return $this->render('pojistna_udalost/ucastnik/new.html.twig', [
            'form' => $form->createView(),
            'pojistnaUdalost' => $pojistnaUdalost,
        ]);
    }
}