<?php

namespace App\Controller\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Helper\FlashMessageHelper;
use App\Repository\PojistnaUdalostRepository;
use App\Service\PojistnaUdalost\PojistnaUdalostToggleArchiveService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Attribute\Route;

class PojistnaUdalostToggleArchiveController extends AbstractController
{
    #[Route('/pou/{id}/toggle-archive', name: 'app_pou_toggle_archive')]
    public function __invoke(int $id, PojistnaUdalostRepository $pojistnaUdalostRepository,PojistnaUdalostToggleArchiveService $toggleArchiveService): RedirectResponse
    {
        $pojistnaUdalost = $pojistnaUdalostRepository->find($id);
        if (!$pojistnaUdalost) {
            throw $this->createNotFoundException('Pojistná událost nenalezena.');
        }
        
        if (!$pojistnaUdalost->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_DANGER, 'Pojistná událost musí být uzavřena pro archivaci.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        $toggleArchiveService->toggleArchivePojistnaUdalost($pojistnaUdalost);
        if ($pojistnaUdalost->isArchive())
        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Pojistná událost vložena do archívu');
        else $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Pojistná událost vyjmuta z archívu');

        return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
    }

}