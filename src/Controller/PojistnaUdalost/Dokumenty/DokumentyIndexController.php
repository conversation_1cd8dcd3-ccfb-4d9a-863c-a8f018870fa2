<?php

namespace App\Controller\PojistnaUdalost\Dokumenty;

use App\Repository\PojistnaUdalostRepository;
use App\Service\FileBrowser\FileBrowserService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pou/{id}/dokumenty', name: 'app_dokumenty_index')]
class DokumentyIndexController extends AbstractController
{

    public function __invoke(
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        FileBrowserService $fileBrowserService,
        int $id
    ): Response 
    {
        $loggedInUser = $this->getUser();
        $pojistnaUdalost = $pojistnaUdalostRepository->findOneByIdAndUser($id, $loggedInUser);

        // Získáme složku "Dokumenty od pojišťovny"
        $slozkaPojistovnaZadani = $pojistnaUdalost->getSlozkaPojistovnaZadani();

        // Načtení obsahu složky "Dokumenty od pojišťovny"
        $folderContentPojistovna = $fileBrowserService->getFolderContent($slozkaPojistovnaZadani);
        $breadcrumbPojistovna = $fileBrowserService->getBreadcrumb($slozkaPojistovnaZadani);

        // Získáme root složku pro sekci "Vše"
        $slozkaRoot = $pojistnaUdalost->getSlozkaRoot();

        // Načtení obsahu root složky
        $folderContentRoot = $fileBrowserService->getFolderContent($slozkaRoot);
        $breadcrumbRoot = $fileBrowserService->getBreadcrumb($slozkaRoot);

        return $this->render('pojistna_udalost/dokumenty/index.html.twig', [
            'pou' => $pojistnaUdalost,
            'submenu_page' => 'dokumenty',
            // Data pro sekci "Dokumenty od pojišťovny"
            'slozkaPojistovnaZadani' => $slozkaPojistovnaZadani,
            'souboryPojistovnaZadani' => $folderContentPojistovna['soubory'],
            'podslozkyPojistovnaZadani' => $folderContentPojistovna['podslozky'],
            'breadcrumbPojistovnaZadani' => $breadcrumbPojistovna,
            // Data pro sekci "Vše" (root složka)
            'slozkaRoot' => $slozkaRoot,
            'souboryRoot' => $folderContentRoot['soubory'],
            'podslozkyRoot' => $folderContentRoot['podslozky'],
            'breadcrumbRoot' => $breadcrumbRoot
        ]);
    }


}
