<?php

namespace App\Controller\PojistnaUdalost\AdditionaInfo;

use App\Dto\PojistnaUdalost\PojistnaUdalostAdditionalInfoEditInput;
use App\Entity\PojistnaUdalost;
use App\Form\PojistnaUdalost\PojistnaUdalostAdditionalInfoType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/infoPU/edit', name: 'app_pojistna_udalost_additional_pu_info_edit')]
class PojistnaUdalostAdditionalPUInfoEditController extends AbstractController
{
    public function __invoke(
        Request $request,
        PojistnaUdalost $id,
        EntityManagerInterface $entityManager
    ): Response {
        $pojistnaUdalost = $id;
        $dto = PojistnaUdalostAdditionalInfoEditInput::createFromPojistnaUdalost($pojistnaUdalost);

        $form = $this->createForm(PojistnaUdalostAdditionalInfoType::class, $dto);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Aktualizace entity z DTO
            $pojistnaUdalost->modifyAdditionalInfo($dto);

            $entityManager->flush();

            $this->addFlash('success', 'Doplňující informace byly úspěšně aktualizovány.');

            return $this->redirectToRoute('app_pojistna_udalost_additional_pu_info', ['id' => $pojistnaUdalost->getId()]);
        }

        return $this->render('pojistna_udalost/additional_info/edit.html.twig', [
            'pojistna_udalost' => $pojistnaUdalost,
            'form' => $form,
            'submenu_page' => 'info_pu'
        ]);
    }
}
