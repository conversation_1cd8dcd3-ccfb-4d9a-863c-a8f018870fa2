<?php

namespace App\Controller\PojistnaUdalost\AdditionaInfo;

use App\Entity\PojistnaUdalost;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/infoPU', name: 'app_pojistna_udalost_additional_pu_info')]
class PojistnaUdalostAddditionalPUInfoController extends AbstractController
{


    public function __invoke(
        Request $request,
        PojistnaUdalost $id
        ):Response
    {
        $pojistnaUdalost =  $id;


        return $this->render('pojistna_udalost/additional_info/detail.html.twig', [
            'pojistna_udalost' => $pojistnaUdalost,
             'submenu_page'=> 'info_pu'
        ]);

    }
}
