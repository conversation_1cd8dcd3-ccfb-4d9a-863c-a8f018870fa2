<?php

namespace App\Controller\PojistnaUdalost\PojistnaSmlouvaInfo;

use App\Dto\PojistnaUdalost\PojistnaSmlouva\PojistnaSmlouvaEditInput;
use App\Entity\LimitRizika;
use App\Entity\PojistnaUdalost;
use App\Entity\Rizika;
use App\Form\PojistnaUdalost\PojistnaSmlouva\PojistnaSmlouvaEditType;
use App\Helper\FlashMessageHelper;
use App\Repository\RizikaRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/pojistna-smlouva/edit', name: 'app_pojistna_smlouva_edit')]
class PojistnaSmlouvaEditController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        PojistnaUdalost $id,
        RizikaRepository $rizikaRepository
    ): Response {
        $pojistnaUdalost = $id;

        $pojistnaSmlouvaEditInput = PojistnaSmlouvaEditInput::createFromPojistnaUdalost($pojistnaUdalost);
        $form = $this->createForm(PojistnaSmlouvaEditType::class, $pojistnaSmlouvaEditInput);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            // Aktualizace údajů o pojistné smlouvě
            $pojistnaUdalost->modifyPojistnaSmlouvaInfo($pojistnaSmlouvaEditInput);
            
            // Odstranění starých limitů rizik
            foreach ($pojistnaUdalost->getLimityRizika() as $limitRizika) {
                $entityManager->remove($limitRizika);
            }
            
            // Určení aktivních sekcí podle zaškrtnuté volby PSPojisteno
            $aktivniSekce = [];
            if (!empty($pojistnaSmlouvaEditInput->PSPojisteno)) {
                $zvolenaVolba = $pojistnaSmlouvaEditInput->PSPojisteno[0];
                
                switch ($zvolenaVolba) {
                    case 'stavba':
                        $aktivniSekce = ['stavba'];
                        break;
                    case 'domacnost':
                        $aktivniSekce = ['domacnost'];
                        break;
                    case 'stav_dom':
                        $aktivniSekce = ['stavba', 'domacnost'];
                        break;
                }
            }
            
            // Přidání nových limitů rizik pouze pro aktivní sekce
            if (in_array('stavba', $aktivniSekce)) {
                foreach ($pojistnaSmlouvaEditInput->limityRizikaStavba as $limitRizikaInput) {
                    if ($limitRizikaInput->zRizika && $limitRizikaInput->castkaLimitRizika) {
                        $limitRizika = new LimitRizika(
                            $pojistnaUdalost,
                            $limitRizikaInput->zRizika,
                            'stavba',
                            $limitRizikaInput->castkaLimitRizika,
                            $limitRizikaInput->limitRizikaNa
                        );
                        $entityManager->persist($limitRizika);
                    }
                }
            }
            
            if (in_array('domacnost', $aktivniSekce)) {
                foreach ($pojistnaSmlouvaEditInput->limityRizikaDomacnost as $limitRizikaInput) {
                    if ($limitRizikaInput->zRizika && $limitRizikaInput->castkaLimitRizika) {
                        $limitRizika = new LimitRizika(
                            $pojistnaUdalost,
                            $limitRizikaInput->zRizika,
                            'domacnost',
                            $limitRizikaInput->castkaLimitRizika,
                            $limitRizikaInput->limitRizikaNa
                        );
                        $entityManager->persist($limitRizika);
                    }
                }
            }
            
            $entityManager->flush();
            
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Informace o pojistné smlouvě byly úspěšně aktualizovány');
            return $this->redirectToRoute('app_pojistna_smlouva_info', ['id' => $pojistnaUdalost->getId()]);
        }

        // Načtení všech rizik pro použití v šabloně
        $rizika = $rizikaRepository->findAll();

        return $this->render('pojistna_udalost/pojistna_smlouva/edit.html.twig', [
            'form' => $form->createView(),
            'pojistna_udalost' => $pojistnaUdalost,
            'submenu_page' => 'info_ps',
            'rizika' => $rizika
        ]);
    }
}
