<?php

namespace App\Controller\PojistnaUdalost\PojistnaSmlouvaInfo;

use App\Entity\PojistnaUdalost;
use App\Enum\PojistenaRizika;
use App\Repository\LimitRizikaRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/pojistna-smlouva', name: 'app_pojistna_smlouva_info')]
class PojistnaSmlouvaInfoController extends AbstractController

{
    public function __invoke(
        PojistnaUdalost $id,
        LimitRizikaRepository $limitRizikaRepository
    ): Response {
        $pojistnaUdalost =  $id;

        $pojisteneLimityStavba = $limitRizikaRepository->findBy([
            'pojistnaUdalost' => $id,
            'pojisteneRiziko' => PojistenaRizika::STAVBA->value,
        ]);

        $pojisteneLimityDomacnost = $limitRizikaRepository->findBy([
            'pojistnaUdalost' => $id,
            'pojisteneRiziko' => PojistenaRizika::DOMACNOST->value
        ]);

        $showStavbaSection = false;
        $showDomacnostSection = false;
        $PSpojisteno = $pojistnaUdalost->getPSPojisteno();

        if (!is_null($PSpojisteno)) {
            $showStavbaSection = ($PSpojisteno == PojistenaRizika::STAVBA->value) || ($PSpojisteno == PojistenaRizika::STAVBA_DOMACNOST->value);
            $showDomacnostSection =  ($PSpojisteno == PojistenaRizika::DOMACNOST->value) || ($PSpojisteno == PojistenaRizika::STAVBA_DOMACNOST->value);
        }

        

        return $this->render('pojistna_udalost/pojistna_smlouva/detail.html.twig', [
            'pojistna_udalost' => $pojistnaUdalost,
            'submenu_page' => 'info_ps',
            'limity_stavba' => $pojisteneLimityStavba,
            'limity_domacnost' => $pojisteneLimityDomacnost,
            'show_stavba_section' =>  $showStavbaSection,
            'show_domacnost_section' => $showDomacnostSection

        ]);
    }
}
