<?php

namespace App\Controller\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Helper\FlashMessageHelper;
use App\Service\PojistnaUdalost\PojistnaUdalostOpenService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class PojistnaUdalostOpenController extends AbstractController
{
    #[Route('/pou/{id}/open', methods: ['POST'], name: 'app_pojistna_udalost_open')]
    public function __invoke(Request $request, PojistnaUdalostOpenService $pojistnaUdalostOpenService, PojistnaUdalost $pojistnaUdalost): Response
    {
        $submittedToken = $request->getPayload()->get('token');

        if ($this->isCsrfTokenValid('open-pu', $submittedToken)) {
            $pojistnaUdalostOpenService->openPojistnaUdalost($pojistnaUdalost);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Pojistná událost byla znovuotevřena');

            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        $this->addFlash(FlashMessageHelper::TYPE_DANGER, 'CHYBA PRI ZPRACOVANI ZNOVUOTEVRENI PU');
        return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
    }
}
