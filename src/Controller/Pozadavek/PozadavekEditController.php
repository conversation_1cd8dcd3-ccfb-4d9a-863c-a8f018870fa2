<?php

namespace App\Controller\Pozadavek;

use App\Dto\Pozadavek\PozadavekEditInput;
use App\Entity\Pozadavek;
use App\Form\Pozadavek\PozadavekEditType;
use App\Helper\FlashMessageHelper;
use App\Service\Pozadavek\PozadavekModifierService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pozadavek/edit/{id}', name: 'app_pozadavek_edit')]
class PozadavekEditController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        Pozadavek $pozadavek,
        PozadavekModifierService $pozadavekModifierService
    ) : Response {
        $pozadavekEditInput = PozadavekEditInput::createFromPozadavek($pozadavek);
        $form = $this->createForm(PozadavekEditType::class, $pozadavekEditInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $pozadavekModifierService->updatePozadavek($pozadavek, $pozadavekEditInput);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Požadavek úspěšně upraven');

            return $this->redirectToRoute('app_pozadavek_list');
        }

        return $this->render('pozadavek/edit.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}