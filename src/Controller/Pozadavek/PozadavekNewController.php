<?php

namespace App\Controller\Pozadavek;

use App\Dto\Pozadavek\PozadavekNewInput;
use App\Factory\Pozadavek\PozadavekNewFactory;
use App\Form\Pozadavek\PozadavekNewType;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pozadavek/new', name: 'app_pozadavek_new')]
class PozadavekNewController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        PozadavekNewFactory $pozadavekNewFactory
    ): Response
    {
        $pozadavekNewInput = new PozadavekNewInput();
        $form = $this->createForm(PozadavekNewType::class, $pozadavekNewInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $newPozadavek = $pozadavekNewFactory->createPozadavek($pozadavekNewInput);
            $entityManager->persist($newPozadavek);
            $entityManager->flush();
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Nový požadavek přidán');

            return $this->redirectToRoute('app_pozadavek_list');
        }

        return $this->render('pozadavek/new.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}