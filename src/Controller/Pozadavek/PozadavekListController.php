<?php

namespace App\Controller\Pozadavek;

use App\Repository\PozadavekRepository;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pozadavek', name: 'app_pozadavek_list')]
class PozadavekListController extends AbstractController
{
    public function __invoke(
        Request $request,
        PozadavekRepository $pozadavekRepository,
        PaginatorInterface $paginator
    ): Response {
        $query = $pozadavekRepository->findAll();

        $pagination = $paginator->paginate(
            $query,
            $request->query->getInt('page', 1),
            50
        );

        return $this->render('pozadavek/list.html.twig', [
            'pagination' => $pagination,
        ]);
    }
}