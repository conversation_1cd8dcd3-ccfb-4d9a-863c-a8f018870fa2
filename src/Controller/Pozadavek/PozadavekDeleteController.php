<?php

namespace App\Controller\Pozadavek;

use App\Entity\Pozadavek;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('pozadavek/delete/{id}', name: 'app_pozadavek_delete')]
class PozadavekDeleteController extends AbstractController
{
    public function __invoke(
        EntityManagerInterface $entityManager,
        Pozadavek $pozadavek,
    ): Response {
        $entityManager->remove($pozadavek);
        $entityManager->flush();

        return $this->redirectToRoute('app_pozadavek_list');
    }
}