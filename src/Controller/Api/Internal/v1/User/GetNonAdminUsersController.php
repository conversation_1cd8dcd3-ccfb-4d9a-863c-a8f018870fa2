<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\User;

use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/internal/v1/users/non-admin', name: 'api_users_non_admin', methods: ['GET'])]
class GetNonAdminUsersController extends AbstractController
{
    public function __invoke(UserRepository $userRepository): JsonResponse
    {
        $users = $userRepository->findAll();
        
        $usersData = [];
        foreach ($users as $user) {
            // Přidáme pouze uživatele, kte<PERSON><PERSON> nejsou administrátoři
            if (!$user->isAdministrator()) {
                $usersData[] = [
                    'id' => $user->getId(),
                    'name' => $user->getName(),
                    'surname' => $user->getSurname(),
                ];
            }
        }
        
        return new JsonResponse($usersData);
    }
}
