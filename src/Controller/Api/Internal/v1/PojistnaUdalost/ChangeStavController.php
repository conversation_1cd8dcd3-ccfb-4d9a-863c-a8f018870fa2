<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\PojistnaUdalost;

use App\Entity\PojistnaUdalost;
use App\Enum\StavLikvidace;
use App\Service\Api\Response\SuccessResponse;
use App\Service\PojistnaUdalost\PojistnaUdalostService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/pojistna-udalost/{id}/stav', name: 'api_pojistna_udalost_stav_change', methods: ['POST'])]
#[IsGranted('ROLE_COM_ADMIN')]
class ChangeStavController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        PojistnaUdalostService $pojistnaUdalostService,
        int $id
    ): JsonResponse {
        $pojistnaUdalost = $entityManager->getRepository(PojistnaUdalost::class)->find($id);
        
        if (!$pojistnaUdalost) {
            throw $this->createNotFoundException('Pojistná událost nenalezena');
        }
        
        if ($pojistnaUdalost->isUzavreno()) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Editace stavu uzavřené události není povolena.'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        $data = json_decode($request->getContent(), true);
        $stavLikvidace = $data['stavLikvidace'] ?? null;
        
        if (!$stavLikvidace) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Stav likvidace nebyl zadán'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        try {
            $newStatus = StavLikvidace::fromString($stavLikvidace);
            $pojistnaUdalostService->changeStatus($pojistnaUdalost, $newStatus);
            
            $response = new SuccessResponse();
            $response->setMessage("Stav byl úspěšně změněn");
            $response->setData([
                'stavValue' => $newStatus->value,
                'stavLabel' => $newStatus->getLabel()
            ]);
            
            return $response->getResponseAsJson();
        } catch (\InvalidArgumentException $e) {
            return new JsonResponse([
                'status' => 'error',
                'message' => $e->getMessage()
            ], Response::HTTP_BAD_REQUEST);
        }
    }
}
