<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\PojistnaUdalost;

use App\Enum\StavLikvidace;
use App\Service\Api\Response\SuccessResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/pojistna-udalost/stavy', name: 'api_pojistna_udalost_stavy', methods: ['GET'])]
#[IsGranted('ROLE_COM_ADMIN')]
class GetStavyController extends AbstractController
{
    public function __invoke(): JsonResponse
    {
        $stavy = StavLikvidace::getStavyAsArrayExceptUzavreno();
        
        $response = new SuccessResponse();
        $response->setMessage("Seznam stavů likvidace");
        $response->setData([
            'stavy' => $stavy
        ]);
        
        return $response->getResponseAsJson();
    }
}
