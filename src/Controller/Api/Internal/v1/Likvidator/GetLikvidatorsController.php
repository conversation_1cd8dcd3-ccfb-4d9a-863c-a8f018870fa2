<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Likvidator;

use App\Repository\UserRepository;
use App\Service\Api\Response\SuccessResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/likvidators', name: 'api_likvidators', methods: ['GET'])]
#[IsGranted('ROLE_COM_ADMIN')]
class GetLikvidatorsController extends AbstractController
{
    public function __invoke(UserRepository $userRepository): JsonResponse
    {
        $likvidators = $userRepository->findLikvidators();
        
        $likvidatorsData = [];
        foreach ($likvidators as $likvidator) {
            $likvidatorsData[] = [
                'id' => $likvidator->getId(),
                'name' => $likvidator->getName(),
                'surname' => $likvidator->getSurname(),
            ];
        }
        
        $response = new SuccessResponse();
        $response->setMessage("Seznam likvidátorů");
        $response->setData([
            'likvidators' => $likvidatorsData
        ]);
        
        return $response->getResponseAsJson();
    }
}
