<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Likvidator;

use App\Dto\PojistnaUdalost\PojistnaUdalostLikvidatorEditInput;
use App\Entity\PojistnaUdalost;
use App\Repository\UserRepository;
use App\Service\Api\Response\SuccessResponse;
use App\Service\PojistnaUdalost\PojistnaUdalostLikvidatorService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/pojistna-udalost/{id}/likvidator', name: 'api_pojistna_udalost_likvidator_change', methods: ['POST'])]
#[IsGranted('ROLE_COM_ADMIN')]
class ChangeLikvidatorController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        PojistnaUdalostLikvidatorService $pojistnaUdalostLikvidatorService,
        UserRepository $userRepository,
        int $id
    ): JsonResponse {
        $pojistnaUdalost = $entityManager->getRepository(PojistnaUdalost::class)->find($id);
        
        if (!$pojistnaUdalost) {
            throw $this->createNotFoundException('Pojistná událost nenalezena');
        }
        
        if ($pojistnaUdalost->isUzavreno()) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Editace likvidátora k uzavřené události není povolena.'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        $data = json_decode($request->getContent(), true);
        $likvidatorId = $data['likvidatorId'] ?? null;
        
        $likvidator = null;
        if ($likvidatorId) {
            $likvidator = $userRepository->find($likvidatorId);
            if (!$likvidator) {
                return new JsonResponse([
                    'status' => 'error',
                    'message' => 'Likvidátor nenalezen'
                ], Response::HTTP_BAD_REQUEST);
            }
        }
        
        $pojistnaUdalostLikvidatorEditInput = new PojistnaUdalostLikvidatorEditInput();
        $pojistnaUdalostLikvidatorEditInput->likvidator = $likvidator;
        
        $pojistnaUdalostLikvidatorService->updatePojistnaUdalostLikvidator($pojistnaUdalost, $pojistnaUdalostLikvidatorEditInput);
        
        $response = new SuccessResponse();
        $response->setMessage("Likvidátor byl úspěšně změněn");
        
        $likvidatorName = null;
        if ($likvidator) {
            $likvidatorName = $likvidator->getName() . ' ' . $likvidator->getSurname();
        }
        
        $response->setData([
            'likvidatorId' => $likvidator ? $likvidator->getId() : null,
            'likvidatorName' => $likvidatorName
        ]);
        
        return $response->getResponseAsJson();
    }
}
