<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Prohlidka;

use App\Entity\PojistnaUdalost;
use App\Entity\Prohlidka;
use App\Entity\User;
use App\Repository\PojistnaUdalostRepository;
use App\Repository\UserRepository;
use App\Service\Api\Response\SuccessResponse;
use App\Service\Notifikace\NotifikaceCreator;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/internal/v1/prohlidka/add', name: 'api_prohlidka_add', methods: ['POST'])]
class AddProhlidkaController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        UserRepository $userRepository,
        NotifikaceCreator $notifikaceCreator
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);
        $technikId = $data['technikId'] ?? null;
        $pojistnaUdalostId = $data['pojistnaUdalostId'] ?? null;
        
        if (!$technikId || !$pojistnaUdalostId) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Chybí povinné parametry'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        $pojistnaUdalost = $pojistnaUdalostRepository->find($pojistnaUdalostId);
        if (!$pojistnaUdalost) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Pojistná událost nenalezena'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        if ($pojistnaUdalost->isUzavreno()) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Přidání prohlídky k uzavřené události není povoleno'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        $technik = $userRepository->find($technikId);
        if (!$technik) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Technik nenalezen'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        // Kontrola, zda technik již není přiřazen k pojistné události
        $isAlreadyAssigned = false;
        
        // Kontrola, zda technik není likvidátor
        if ($pojistnaUdalost->getLikvidator() === $technik) {
            $isAlreadyAssigned = true;
        }
        
        // Kontrola, zda technik není v kolekci dalsiUzivatele
        if (!$isAlreadyAssigned && $pojistnaUdalost->getDalsiUzivatele()->contains($technik)) {
            $isAlreadyAssigned = true;
        }
        
        // Pokud technik není přiřazen k pojistné události, přidáme ho do kolekce dalsiUzivatele
        if (!$isAlreadyAssigned) {
            $pojistnaUdalost->addDalsiUzivatele($technik);
        }
        
        // Vytvoření nové prohlídky
        $prohlidka = new Prohlidka($pojistnaUdalost, $technik);
        
        $entityManager->persist($prohlidka);
        $entityManager->flush();
        
        // Vytvoření notifikace pro technika
        $notifikaceCreator->createProhlidkaCreationNotifikace($prohlidka, true);
        
        $response = new SuccessResponse();
        $response->setMessage("Prohlídka byla úspěšně přidána");
        $response->setData([
            'prohlidkaId' => $prohlidka->getId(),
            'technikName' => $technik->getName() . ' ' . $technik->getSurname()
        ]);
        
        return $response->getResponseAsJson();
    }
}
