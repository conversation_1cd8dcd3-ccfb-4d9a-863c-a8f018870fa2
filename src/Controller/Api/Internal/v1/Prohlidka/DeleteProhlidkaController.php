<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Prohlidka;

use App\Entity\User;
use App\Repository\ProhlidkaRepository;
use App\Service\Api\Response\SuccessResponse;
use App\Service\Notifikace\NotifikaceCreator;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/prohlidka/{id}/delete', name: 'api_prohlidka_delete', methods: ['DELETE'])]
#[IsGranted('ROLE_COM_ADMIN')]
class DeleteProhlidkaController extends AbstractController
{
    public function __invoke(
        EntityManagerInterface $entityManager,
        ProhlidkaRepository $prohlidkaRepository,
        NotifikaceCreator $notifikaceCreator,
        int $id
    ): JsonResponse {
        $prohlidka = $prohlidkaRepository->find($id);
        
        if (!$prohlidka) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Prohlídka nenalezena'
            ], Response::HTTP_NOT_FOUND);
        }
        
        $pojistnaUdalost = $prohlidka->getPojistnaUdalost();
        
        if ($pojistnaUdalost->isUzavreno()) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Smazání prohlídky u uzavřené události není povoleno'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        // Kontrola, zda prohlídka nemá datum
        if ($prohlidka->getDatum() !== null) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Smazat lze pouze prohlídky bez nastaveného data'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        // Získání aktuálního uživatele, který prohlídku maže
        /**
         * @var User $currentUser
         */
        $currentUser = $this->getUser();
        
        // Vytvoření notifikace o zrušení prohlídky
        $notifikaceCreator->createProhlidkaRevocationNotifikace($prohlidka, $currentUser, true);
        
        $entityManager->remove($prohlidka);
        $entityManager->flush();
        
        $response = new SuccessResponse();
        $response->setMessage("Prohlídka byla úspěšně smazána");
        
        return $response->getResponseAsJson();
    }
}
