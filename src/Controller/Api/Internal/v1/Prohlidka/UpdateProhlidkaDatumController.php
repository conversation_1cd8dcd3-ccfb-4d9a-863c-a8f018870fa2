<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Prohlidka;

use App\Entity\Prohlidka;
use App\Repository\ProhlidkaRepository;
use App\Service\Api\Response\SuccessResponse;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/internal/v1/prohlidka/{id}/update-datum', name: 'api_prohlidka_update_datum', methods: ['POST'])]
class UpdateProhlidkaDatumController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        ProhlidkaRepository $prohlidkaRepository,
        int $id
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);
        $datum = $data['datum'] ?? null;
        
        if (!$datum) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Chybí povinný parametr datum'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        $prohlidka = $prohlidkaRepository->find($id);
        if (!$prohlidka) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Prohlídka nenalezena'
            ], Response::HTTP_NOT_FOUND);
        }
        
        // Kontrola, zda je pojistná událost uzavřena
        $pojistnaUdalost = $prohlidka->getPojistnaUdalost();
        if ($pojistnaUdalost->isUzavreno()) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Aktualizace prohlídky u uzavřené události není povolena'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        try {
            // Převod řetězce data na DateTimeImmutable
            $datumObj = new DateTimeImmutable($datum);
            
            // Aktualizace data prohlídky
            $prohlidka->setDatum($datumObj);
            
            $entityManager->flush();
            
            $response = new SuccessResponse();
            $response->setMessage("Datum prohlídky bylo úspěšně aktualizováno");
            $response->setData([
                'prohlidkaId' => $prohlidka->getId(),
                'datum' => $datumObj->format('d.m.Y')
            ]);
            
            return $response->getResponseAsJson();
        } catch (\Exception $e) {
            return new JsonResponse([
                'status' => 'error',
                'message' => 'Chyba při aktualizaci data prohlídky: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
