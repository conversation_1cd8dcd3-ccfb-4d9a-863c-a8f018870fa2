<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Soubor;

use App\Repository\SouborRepository;
use App\Service\Api\Response\SuccessResponse;
use App\Service\Soubor\SouborRemover;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/soubor/{id}/delete', name: 'api_soubor_delete', methods: ['DELETE'])]
#[IsGranted('ROLE_COM_ADMIN')]
class DeleteController extends AbstractController
{
    public function __invoke(
        int $id,
        SouborRepository $souborRepository,
        EntityManagerInterface $entityManager,
        SouborRemover $souborRemover
    ): JsonResponse {
        $soubor = $souborRepository->find($id);

        if (!$soubor) {
            return new JsonResponse([
                'error' => 'Soubor nebyl nalezen'
            ], Response::HTTP_NOT_FOUND);
        }

        // Kontrola oprávnění k pojistné události
        $slozka = $soubor->getSlozka();
        if (!$slozka) {
            return new JsonResponse([
                'error' => 'Soubor není přiřazen ke složce'
            ], Response::HTTP_BAD_REQUEST);
        }

        $pojistnaUdalost = $slozka->getPojistnaUdalost();
        if (!$pojistnaUdalost) {
            return new JsonResponse([
                'error' => 'Složka není přiřazena k pojistné události'
            ], Response::HTTP_BAD_REQUEST);
        }

        // Kontrola oprávnění DELETE_FILE
        if (!$this->isGranted('DELETE_FILE', $pojistnaUdalost)) {
            return new JsonResponse([
                'error' => 'Nemáte oprávnění k mazání souborů v této pojistné události'
            ], Response::HTTP_FORBIDDEN);
        }

        $fileName = $soubor->getOriginalFilename();
        $slozkaId = $slozka->getId();

        try {
            $entityManager->beginTransaction();
            
            $souborRemover->deleteFile($soubor);
            $entityManager->remove($soubor);
            $entityManager->flush();
            
            $entityManager->commit();

            $response = new SuccessResponse();
            $response->setMessage("Soubor \"{$fileName}\" byl úspěšně smazán");
            $response->setData([
                'deletedFileId' => $id,
                'deletedFileName' => $fileName,
                'slozkaId' => $slozkaId
            ]);

            return $response->getResponseAsJson();

        } catch (\Throwable $e) {
            $entityManager->rollback();
            
            return new JsonResponse([
                'error' => "Při mazání souboru \"{$fileName}\" došlo k chybě: " . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
