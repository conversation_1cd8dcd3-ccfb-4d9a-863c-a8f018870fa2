<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Soubor;

use App\Repository\SouborRepository;
use App\Service\Api\Response\SuccessResponse;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/internal/v1/soubor/{id}/rename', name: 'api_soubor_rename', methods: ['PUT'])]
#[IsGranted('ROLE_COM_ADMIN')]
class RenameController extends AbstractController
{
    public function __invoke(
        int $id,
        Request $request,
        SouborRepository $souborRepository,
        EntityManagerInterface $entityManager,
        ValidatorInterface $validator
    ): JsonResponse {
        $soubor = $souborRepository->find($id);
        
        if (!$soubor) {
            return new JsonResponse([
                'error' => 'Soubor nebyl nalezen'
            ], Response::HTTP_NOT_FOUND);
        }

        // Kontrola oprávnění k pojistné události
        $slozka = $soubor->getSlozka();
        if (!$slozka) {
            return new JsonResponse([
                'error' => 'Soubor není přiřazen ke složce'
            ], Response::HTTP_BAD_REQUEST);
        }

        $pojistnaUdalost = $slozka->getPojistnaUdalost();
        if (!$pojistnaUdalost) {
            return new JsonResponse([
                'error' => 'Složka není přiřazena k pojistné události'
            ], Response::HTTP_BAD_REQUEST);
        }

        // Kontrola oprávnění EDIT - přejmenování souborů
        if (!$this->isGranted('EDIT', $pojistnaUdalost)) {
            return new JsonResponse([
                'error' => 'Nemáte oprávnění k úpravě souborů v této pojistné události'
            ], Response::HTTP_FORBIDDEN);
        }

        $data = json_decode($request->getContent(), true);
        $newName = $data['newName'] ?? '';

        if (empty($newName)) {
            return new JsonResponse([
                'error' => 'Nový název souboru je povinný'
            ], Response::HTTP_BAD_REQUEST);
        }

        // Získáme původní příponu
        $originalExtension = pathinfo($soubor->getOriginalFilename(), PATHINFO_EXTENSION);
        $newNameWithoutExtension = pathinfo($newName, PATHINFO_FILENAME);
        
        // Sestavíme finální název s původní příponou
        $finalName = $newNameWithoutExtension . ($originalExtension ? '.' . $originalExtension : '');

        // Validace názvu - rozšířený regex pro české znaky
        $errors = $validator->validate($finalName, [
            new NotBlank(['message' => 'Název souboru nesmí být prázdný']),
            new Length([
                'max' => 255,
                'maxMessage' => 'Název souboru nesmí být delší než {{ limit }} znaků'
            ]),
            new Regex([
                'pattern' => '/^[a-zA-ZáčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ0-9\s\-_\.\(\)\[\]]+$/u',
                'message' => 'Název souboru může obsahovat pouze písmena (včetně českých znaků), číslice, mezery, pomlčky, podtržítka, tečky a závorky'
            ])
        ]);

        if ($errors->count() > 0) {
            $message = '';
            foreach ($errors as $error) {
                $message .= $error->getMessage() . ' ';
            }
            return new JsonResponse([
                'error' => trim($message)
            ], Response::HTTP_BAD_REQUEST);
        }

        // Kontrola duplicit ve stejné složce
        $existingSoubor = $souborRepository->findOneBy([
            'originalFilename' => $finalName,
            'slozka' => $soubor->getSlozka()
        ]);

        if ($existingSoubor && $existingSoubor->getId() !== $soubor->getId()) {
            return new JsonResponse([
                'error' => "Soubor s názvem \"{$finalName}\" již ve složce existuje"
            ], Response::HTTP_CONFLICT);
        }

        $oldName = $soubor->getOriginalFilename();

        try {
            $entityManager->beginTransaction();
            
            // Aktualizujeme název souboru
            $soubor->modifyOriginalFilename($finalName);
            $entityManager->flush();
            
            $entityManager->commit();

            $response = new SuccessResponse();
            $response->setMessage("Soubor byl úspěšně přejmenován z \"{$oldName}\" na \"{$finalName}\"");
            $response->setData([
                'fileId' => $id,
                'oldName' => $oldName,
                'newName' => $finalName,
                'slozkaId' => $soubor->getSlozka()?->getId()
            ]);

            return $response->getResponseAsJson();

        } catch (\Throwable $e) {
            $entityManager->rollback();
            
            return new JsonResponse([
                'error' => "Při přejmenování souboru došlo k chybě: " . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
