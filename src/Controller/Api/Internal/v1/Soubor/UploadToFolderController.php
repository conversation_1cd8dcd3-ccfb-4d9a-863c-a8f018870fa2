<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Soubor;

use App\Repository\SlozkaRepository;
use App\Service\Api\Response\SuccessResponse;
use App\Service\Soubor\SouborUploader;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/internal/v1/soubor/upload/{id}', name: 'api_soubor_upload_to_folder', methods: ['POST'])]
#[IsGranted('ROLE_COM_ADMIN')]
class UploadToFolderController extends AbstractController
{
    public function __invoke(
        int $id,
        Request $request,
        SlozkaRepository $slozkaRepository,
        ValidatorInterface $validator,
        SouborUploader $souborUploader
    ): JsonResponse {
        $slozka = $slozkaRepository->find($id);
        
        if (!$slozka) {
            return new JsonResponse(['error' => 'Složka nebyla nalezena'], Response::HTTP_NOT_FOUND);
        }

        // Kontrola oprávnění k pojistné události
        $pojistnaUdalost = $slozka->getPojistnaUdalost();
        if (!$pojistnaUdalost) {
            return new JsonResponse([
                'error' => 'Složka není přiřazena k pojistné události'
            ], Response::HTTP_BAD_REQUEST);
        }

        // Kontrola oprávnění EDIT - nahrávání souborů
        if (!$this->isGranted('EDIT', $pojistnaUdalost)) {
            return new JsonResponse([
                'error' => 'Nemáte oprávnění k nahrávání souborů do této pojistné události'
            ], Response::HTTP_FORBIDDEN);
        }

        try {
            $file = $request->files->get('file'); // Dropzone používá 'file' jako default
            if (!$file) {
                throw new Exception('Nebyl poskytnut žádný soubor');
            }

            // Validace souboru
            $errors = $validator->validate(
                $file,
                [
                    new NotBlank(),
                    new File([
                        'mimeTypes' => [
                            'application/pdf',
                            'application/msword',
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            'application/vnd.oasis.opendocument.text',
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                        ]
                    ])
                ]
            );

            if ($errors->count() > 0) {
                $message = '';
                foreach ($errors as $error) {
                    $message .= $error->getMessage() . ' ';
                }
                return new JsonResponse([
                    'error' => trim($message)
                ], Response::HTTP_BAD_REQUEST);
            }

            if ($file instanceof UploadedFile) {
                // Určíme typ uploadu podle složky
                $uploadType = $this->determineUploadType($slozka);
                $pojistnaUdalost = $slozka->getPojistnaUdalost();
                
                // Předáme konkrétní složku do uploaderu
                $soubor = $souborUploader->storeSouborDoSlozky($file, $pojistnaUdalost, $uploadType, $slozka);
            }

            $response = new SuccessResponse();
            $response->setMessage('Soubor byl úspěšně nahrán');
            $response->setData([
                'fileName' => $file->getClientOriginalName(),
                'slozkaId' => $slozka->getId()
            ]);

            return $response->getResponseAsJson();

        } catch (Exception $e) {
            return new JsonResponse([
                'error' => $e->getMessage()
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * 
     * @param mixed $slozka 
     * @return string 
     */
    private function determineUploadType($slozka): string
    {
        // Mapování typů složek na upload typy
        switch ($slozka->getDruhSlozky()) {
            case 'pojistovna_zadani':
                return 'odpojistovny';
            case 'klient':
                return 'odklienta';
            case 'technik':
                return 'odtechnika';
            case 'pojistovna_ukonceni':
                return 'propojistovnu';
            default:
                return 'propojistovnu'; // default fallback
        }
    }
}
