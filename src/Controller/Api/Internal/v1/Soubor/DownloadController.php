<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Soubor;

use App\Repository\SouborRepository;
use App\Service\Soubor\SouborReader;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\String\Slugger\SluggerInterface;

#[Route('/api/internal/v1/soubor/{id}/download', name: 'api_soubor_download', methods: ['GET'])]
#[IsGranted('ROLE_COM_ADMIN')]
class DownloadController extends AbstractController
{
    public function __invoke(
        int $id,
        SouborRepository $souborRepository,
        SouborReader $souborReader,
        SluggerInterface $slugger
    ): Response {
        $soubor = $souborRepository->find($id);
        
        if (!$soubor) {
            throw $this->createNotFoundException('Soubor nebyl nalezen.');
        }

        $response = new StreamedResponse(function () use ($soubor, $souborReader) {
            $outputStream = fopen('php://output', 'wb');
            $fileStream = $souborReader->readSouborAsStream($soubor);
            stream_copy_to_stream($fileStream, $outputStream);
        });

        $response->headers->set('Content-Type', $soubor->getMimeType());
        
        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_INLINE,
            $soubor->getOriginalFilename(),
            $slugger->slug($soubor->getOriginalFilename(), '_')->toString()
        );
        
        $response->headers->set('Content-Disposition', $disposition);

        return $response;
    }
}
