<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Soubor;

use App\Entity\Slozka;
use App\Repository\SlozkaRepository;
use App\Service\Api\Response\SuccessResponse;
use App\Service\FileBrowser\FileBrowserService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/soubor/folder/{id}', name: 'api_soubor_folder_content', methods: ['GET'])]
#[IsGranted('ROLE_COM_ADMIN')]
class GetFolderContentController extends AbstractController
{
    public function __invoke(
        int $id,
        Request $request,
        SlozkaRepository $slozkaRepository,
        FileBrowserService $fileBrowserService
    ): JsonResponse {
        $slozka = $slozkaRepository->find($id);
        
        if (!$slozka) {
            return new JsonResponse(['error' => 'Složka nebyla nalezena'], Response::HTTP_NOT_FOUND);
        }

        // Získáme sort parametry z query stringu
        $sortField = $request->query->get('sort', 'createdAt');
        $sortOrder = $request->query->get('order', 'desc');
        
        // Validace sort parametrů
        if (!in_array($sortField, ['createdAt'])) {
            $sortField = 'createdAt';
        }
        
        if (!in_array($sortOrder, ['asc', 'desc'])) {
            $sortOrder = 'desc';
        }

        try {
            $folderContent = $fileBrowserService->getFolderContent($slozka, $sortField, $sortOrder);
            
            // Pro breadcrumb potřebujeme najít uživatelskou root složku
            // To je složka, která byla předána do file browser komponenty
            // Najdeme ji tak, že půjdeme nahoru dokud nenarazíme na složku typu ROOT
            // a pak vezmeme její první child (to je naše uživatelská root)
            $rootSlozka = $slozka;
            
            // Najdeme systémovou ROOT složku
            while ($rootSlozka->getParent() !== null) {
                if ($rootSlozka->getParent()->getDruhSlozky() === Slozka::DRUH_SLOZKY_ROOT) {
                    // Našli jsme child ROOT složky - to je naše uživatelská root
                    break;
                }
                $rootSlozka = $rootSlozka->getParent();
            }
            
            $breadcrumb = $fileBrowserService->getBreadcrumb($slozka, $rootSlozka);

            // Připravíme data pro JSON response
            $souboryData = [];
            foreach ($folderContent['soubory'] as $soubor) {
                $souboryData[] = [
                    'id' => $soubor->getId(),
                    'originalFilename' => $soubor->getOriginalFilename(),
                    'mimeType' => $soubor->getMimeType(),
                    'createdAt' => $soubor->getCreatedAt()?->format('d.m.Y H:i'),
                    'icon' => $fileBrowserService->getFileIcon($soubor),
                    'iconColor' => $fileBrowserService->getFileIconColor($soubor),
                ];
            }

            $podslozkyData = [];
            foreach ($folderContent['podslozky'] as $podslozka) {
                $podslozkyData[] = [
                    'id' => $podslozka->getId(),
                    'nazev' => $podslozka->getNazev(),
                ];
            }

            $response = new SuccessResponse();
            $response->setMessage("Obsah složky načten");
            $response->setData([
                'soubory' => $souboryData,
                'podslozky' => $podslozkyData,
                'breadcrumb' => $breadcrumb,
                'slozka' => [
                    'id' => $slozka->getId(),
                    'nazev' => $fileBrowserService->getFolderDisplayName($slozka),
                ],
                'sort' => [
                    'field' => $sortField,
                    'order' => $sortOrder
                ]
            ]);

            return $response->getResponseAsJson();

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Nepodařilo se načíst obsah složky'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
