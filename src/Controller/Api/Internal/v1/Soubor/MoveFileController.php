<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Soubor;

use App\Entity\Slozka;
use App\Entity\Soubor;
use App\Repository\SlozkaRepository;
use App\Repository\SouborRepository;
use App\Service\Api\Response\SuccessResponse;
use App\Service\Soubor\SouborMoveService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/soubor/{fileId}/move-to-folder/{targetFolderId}', name: 'api_soubor_move_to_folder', methods: ['POST'])]
#[IsGranted('ROLE_COM_ADMIN')]
class MoveFileController extends AbstractController
{
    public function __invoke(
        int $fileId,
        int $targetFolderId,
        SouborRepository $souborRepository,
        SlozkaRepository $slozkaRepository,
        SouborMoveService $souborMoveService,
        EntityManagerInterface $entityManager
    ): JsonResponse {
        
        try {
            // Načtení souboru
            $soubor = $souborRepository->find($fileId);
            if (!$soubor) {
                return new JsonResponse([
                    'error' => 'Soubor nebyl nalezen'
                ], Response::HTTP_NOT_FOUND);
            }

            // Načtení cílové složky
            $targetSlozka = $slozkaRepository->find($targetFolderId);
            if (!$targetSlozka) {
                return new JsonResponse([
                    'error' => 'Cílová složka nebyla nalezena'
                ], Response::HTTP_NOT_FOUND);
            }

            // Kontrola oprávnění k source pojistné události (EDIT - úprava souborů)
            $sourcePojistnaUdalost = $soubor->getSlozka()->getPojistnaUdalost();
            if (!$sourcePojistnaUdalost) {
                return new JsonResponse([
                    'error' => 'Zdrojová složka není přiřazena k pojistné události'
                ], Response::HTTP_BAD_REQUEST);
            }
            
            if (!$this->isGranted('EDIT', $sourcePojistnaUdalost)) {
                return new JsonResponse([
                    'error' => 'Nemáte oprávnění k úpravě souborů v zdrojové pojistné události'
                ], Response::HTTP_FORBIDDEN);
            }
            
            // Kontrola oprávnění k target pojistné události (EDIT - nahrávání souborů)
            $targetPojistnaUdalost = $targetSlozka->getPojistnaUdalost();
            if (!$targetPojistnaUdalost) {
                return new JsonResponse([
                    'error' => 'Cílová složka není přiřazena k pojistné události'
                ], Response::HTTP_BAD_REQUEST);
            }
            
            if (!$this->isGranted('EDIT', $targetPojistnaUdalost)) {
                return new JsonResponse([
                    'error' => 'Nemáte oprávnění k nahrávání souborů do cílové pojistné události'
                ], Response::HTTP_FORBIDDEN);
            }

            // TODO: Placeholder pro business rules validation
            $validationResult = $this->validateMove($soubor, $targetSlozka);
            if (!$validationResult['valid']) {
                return new JsonResponse([
                    'error' => $validationResult['message']
                ], Response::HTTP_BAD_REQUEST);
            }

            // Kontrola, že se soubor nepřesouvá do stejné složky
            if ($soubor->getSlozka()->getId() === $targetSlozka->getId()) {
                return new JsonResponse([
                    'error' => 'Soubor je již v cílové složce'
                ], Response::HTTP_BAD_REQUEST);
            }

            // Přesunutí souboru
            $souborMoveService->moveFilePhysically($soubor, $targetSlozka);
            
            // OPRAVA: Uložení změn do databáze
            // moveFilePhysically() aktualizuje entitu, ale nevolá flush()
            $entityManager->flush();

            // Úspěšná odpověď
            $response = new SuccessResponse();
            $response->setMessage('Soubor byl úspěšně přesunut');
            $response->setData([
                'fileId' => $soubor->getId(),
                'fileName' => $soubor->getOriginalFilename(),
                'sourceFolder' => [
                    'id' => $soubor->getSlozka()->getId(),
                    'name' => $soubor->getSlozka()->getNazev()
                ],
                'targetFolder' => [
                    'id' => $targetSlozka->getId(),
                    'name' => $targetSlozka->getNazev()
                ]
            ]);

            return $response->getResponseAsJson();

        } catch (Exception $e) {
            return new JsonResponse([
                'error' => 'Chyba při přesouvání souboru: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Placeholder pro validaci business pravidel
     * TODO: Implementovat skutečnou validační logiku
     * 
     * @return array<mixed>
     */
    private function validateMove(Soubor $soubor, Slozka $targetSlozka): array
    {
        // Prozatím vše validní
        // V budoucnu zde bude logika pro:
        // - Kontrola typu složky (pojistovna_zadani -> klient?)
        // - Kontrola velikosti cílové složky
        // - Kontrola duplicitních názvů
        // - Další business pravidla
        
        return ['valid' => true, 'message' => ''];
    }
}
