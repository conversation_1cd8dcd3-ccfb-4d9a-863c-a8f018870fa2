<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Slozka;

use App\Entity\Slozka;
use App\Repository\SlozkaRepository;
use App\Service\Api\Response\SuccessResponse;
use App\Service\Slozka\SlozkaManagerService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/slozka/create-subfolder/{parentId}', name: 'api_slozka_create_subfolder', methods: ['POST'])]
#[IsGranted('ROLE_COM_ADMIN')]
class CreateSubfolderController extends AbstractController
{
    public function __invoke(
        int $parentId,
        Request $request,
        SlozkaRepository $slozkaRepository,
        SlozkaManagerService $slozkaManagerService
    ): JsonResponse {
        
        // Získání parent složky
        $parentSlozka = $slozkaRepository->find($parentId);
        
        if (!$parentSlozka) {
            return new JsonResponse([
                'statusCode' => 404,
                'status' => 'error',
                'error' => 'Rodičovská složka nebyla nalezena'
            ], Response::HTTP_NOT_FOUND);
        }

        // Kontrola oprávnění - uživatel musí mít přístup k pojistné události
        $pojistnaUdalost = $parentSlozka->getPojistnaUdalost();
        
        if (!$pojistnaUdalost) {
            return new JsonResponse([
                'statusCode' => 400,
                'status' => 'error',
                'error' => 'Rodičovská složka není přiřazena k pojistné události'
            ], Response::HTTP_BAD_REQUEST);
        }
        
        // Kontrola oprávnění k pojistné události (EDIT - vytváření složek)
        if (!$this->isGranted('EDIT', $pojistnaUdalost)) {
            return new JsonResponse([
                'statusCode' => 403,
                'status' => 'error',
                'error' => 'Nemáte oprávnění k této pojistné události'
            ], Response::HTTP_FORBIDDEN);
        }

        // Získání dat z requestu
        $data = json_decode($request->getContent(), true);
        
        if (!$data || !isset($data['nazev'])) {
            return new JsonResponse([
                'statusCode' => 400,
                'status' => 'error',
                'error' => 'Chybí název složky'
            ], Response::HTTP_BAD_REQUEST);
        }

        $nazevSlozky = trim($data['nazev']);

        // Validace názvu složky
        $validationErrors = $slozkaManagerService->validateSlozkaName($nazevSlozky);
        
        if (!empty($validationErrors)) {
            return new JsonResponse([
                'statusCode' => 400,
                'status' => 'error',
                'error' => 'Neplatný název složky',
                'validation_errors' => $validationErrors
            ], Response::HTTP_BAD_REQUEST);
        }

        try {
            // Vytvoření nové USER podsložky s druhem PU_NEURCENO
            $newSlozka = $slozkaManagerService->createUserSubfolder(
                $nazevSlozky,
                $parentSlozka,
                $pojistnaUdalost,
                Slozka::DRUH_SLOZKY_PU_NEURCENO
            );

            // Příprava dat pro response
            $slozkaData = [
                'id' => $newSlozka->getId(),
                'nazev' => $newSlozka->getNazev(),
                'typSlozky' => $newSlozka->getTypSlozky(),
                'druhSlozky' => $newSlozka->getDruhSlozky(),
                'parentId' => $newSlozka->getParent()?->getId(),
                'cesta' => $newSlozka->getCesta(),
                'canBeDeleted' => $newSlozka->canBeDeleted()
            ];

            $response = new SuccessResponse();
            $response->setMessage("Složka '{$nazevSlozky}' byla úspěšně vytvořena");
            $response->setData([
                'slozka' => $slozkaData
            ]);

            return $response->getResponseAsJson();

        } catch (\InvalidArgumentException $e) {
            return new JsonResponse([
                'statusCode' => 400,
                'status' => 'error',
                'error' => $e->getMessage()
            ], Response::HTTP_BAD_REQUEST);

        } catch (\RuntimeException $e) {
            return new JsonResponse([
                'statusCode' => 500,
                'status' => 'error',
                'error' => 'Nepodařilo se vytvořit složku: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);

        } catch (\Exception $e) {
            return new JsonResponse([
                'statusCode' => 500,
                'status' => 'error',
                'error' => 'Neočekávaná chyba při vytváření složky'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
