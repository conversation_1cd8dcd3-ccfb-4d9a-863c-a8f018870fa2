<?php

declare(strict_types=1);

namespace App\Controller\Api\Internal\v1\Slozka;

use App\Entity\Slozka;
use App\Repository\SlozkaRepository;
use App\Service\Api\Response\SuccessResponse;
use App\Service\Slozka\SlozkaManagerService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/internal/v1/slozka/delete/{id}', name: 'api_slozka_delete', methods: ['DELETE'])]
#[IsGranted('ROLE_COM_ADMIN')]
class DeleteFolderController extends AbstractController
{
    public function __invoke(
        int $id,
        SlozkaRepository $slozkaRepository,
        SlozkaManagerService $slozkaManagerService
    ): JsonResponse {
        
        // Najdeme složku
        $slozka = $slozkaRepository->find($id);
        
        if (!$slozka) {
            return new JsonResponse([
                'statusCode' => 404,
                'status' => 'error',
                'error' => 'Složka nebyla nalezena'
            ], Response::HTTP_NOT_FOUND);
        }

        try {
            // Validace - pouze USER složky
            if (!$slozka->isUserSlozka()) {
                return new JsonResponse([
                    'statusCode' => 403,
                    'status' => 'error',
                    'error' => 'Lze mazat pouze uživatelské složky'
                ], Response::HTTP_FORBIDDEN);
            }

            // Validace - složka musí být prázdná
            if (!$slozka->canBeDeleted()) {
                return new JsonResponse([
                    'statusCode' => 400,
                    'status' => 'error',
                    'error' => 'Složka není prázdná. Nejdříve smažte všechny soubory a podsložky.'
                ], Response::HTTP_BAD_REQUEST);
            }

            // Validace - kontrola oprávnění k pojistné události
            $pojistnaUdalost = $slozka->getPojistnaUdalost();
            if (!$pojistnaUdalost) {
                return new JsonResponse([
                    'statusCode' => 400,
                    'status' => 'error',
                    'error' => 'Složka není přiřazena k pojistné události'
                ], Response::HTTP_BAD_REQUEST);
            }

            // Kontrola oprávnění uživatele k pojistné události
            if (!$this->isGranted('DELETE_FILE', $pojistnaUdalost)) {
                return new JsonResponse([
                    'statusCode' => 403,
                    'status' => 'error',
                    'error' => 'Nemáte oprávnění k této pojistné události'
                ], Response::HTTP_FORBIDDEN);
            }

            // Smazání složky
            $folderName = $slozka->getNazev();
            $slozkaManagerService->deleteFolder($slozka);

            // Úspěšná odpověď
            $response = new SuccessResponse();
            $response->setMessage("Složka '{$folderName}' byla úspěšně smazána");
            $response->setData([
                'deleted_folder_id' => $id,
                'deleted_folder_name' => $folderName
            ]);

            return $response->getResponseAsJson();

        } catch (\InvalidArgumentException $e) {
            return new JsonResponse([
                'statusCode' => 400,
                'status' => 'error',
                'error' => $e->getMessage()
            ], Response::HTTP_BAD_REQUEST);

        } catch (\RuntimeException $e) {
            return new JsonResponse([
                'statusCode' => 500,
                'status' => 'error',
                'error' => 'Nepodařilo se smazat složku: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);

        } catch (\Exception $e) {
            return new JsonResponse([
                'statusCode' => 500,
                'status' => 'error',
                'error' => 'Neočekávaná chyba při mazání složky'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
