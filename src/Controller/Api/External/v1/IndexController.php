<?php

declare(strict_types=1);

namespace App\Controller\Api\External\v1;

use App\Service\Api\Response\SuccessResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

class IndexController extends AbstractController
{
    #[Route('/api/v1/external', methods:'GET')]
    public function __invoke(): JsonResponse
    {
        $response = new SuccessResponse();
        $response->setMessage("Welcome to InIn API gateway!");
        $response->setData([
            'In' => 'In'
        ]);

        return $response->getResponseAsJson();
    }
}
