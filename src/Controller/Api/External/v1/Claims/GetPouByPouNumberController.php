<?php

declare(strict_types=1);

namespace App\Controller\Api\External\v1\Claims;

use App\Repository\PojistnaUdalostRepository;
use App\Service\Api\Response\SuccessResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;

class GetPouByPouNumberController extends AbstractController
{
    #[Route('/api/v1/external/claim/{pouNumber}', methods:'GET')]
    public function __invoke(PojistnaUdalostRepository $pojistnaUdalostRepository, string $pouNumber): JsonResponse
    {
        $responseDataAsJson = $pojistnaUdalostRepository->getByPouNumberQueryAsArrayForApi($pouNumber);

        $response = new SuccessResponse();

        if (empty($responseDataAsJson)) {
            $response->setMessage("Pojistna udalost not found");
        } else {
            $response->setMessage("Pojistna udalost found");
        }

        $response->setData(
            $responseDataAsJson
        );

        return $response->getResponseAsJson();
    }
}
