<?php

declare(strict_types=1);

namespace App\Controller\Api\External\v1\Claims;

use App\Enum\EventCode;
use App\Service\Api\Claims\Notification\NotificationService;
use App\Service\Api\Claims\Notification\ResponseGeneratorService;
use App\Service\Api\Claims\SystemProviderService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use OpenApi\Attributes as OA;

class NotificationController extends AbstractController
{
    #[Route('/api/v1/external/{systemId}/claims/notification', methods: 'POST')]
    #[OA\Tag(name: 'Claims')]  // pouze pro organizaci v dokumentaci
    #[OA\Post(
        summary: 'Notifikace o změně škodní události',
        description: 'Endpoint pro příjem notifikací o změnách ve škodní události'
    )]
    #[OA\Parameter(
        name: 'systemId',
        in: 'path',
        required: true,
        schema: new OA\Schema(type: 'string'),
        description: 'ID externího systému'
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(
            required: ['claimNumber', 'eventCode'],
            properties: [
                new OA\Property(
                    property: 'claimNumber',
                    type: 'integer',
                    format: 'int64',
                    description: 'Číslo škodní události'
                ),
                new OA\Property(
                    property: 'eventCode',
                    type: 'string',
                    enum: ['HANDOVER', 'DOCUMENT_UPDATE', 'ACTIVITY_UPDATE', 'HISTORY_LOG_UPDATE'],
                    description: 'Typ události'
                )
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Notifikace úspěšně zpracována',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'status',
                    type: 'string',
                    example: 'success',
                    description: 'Status zpracování'
                ),
                new OA\Property(
                    property: 'message',
                    type: 'string',
                    example: 'Notification successfully processed',
                    description: 'Popis výsledku zpracování'
                ),
                new OA\Property(
                    property: 'notificationId',
                    type: 'integer',
                    format: 'int64',
                    example: 123456789,
                    description: 'Unikátní ID přijaté notifikace v systému'
                )
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Neplatný formát požadavku',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'status',
                    type: 'string',
                    example: 'error',
                    description: 'Status odpovědi'
                ),
                new OA\Property(
                    property: 'code',
                    type: 'string',
                    example: 'INVALID_REQUEST',
                    description: 'Kód chyby'
                ),
                new OA\Property(
                    property: 'message',
                    type: 'string',
                    example: 'Invalid request format',
                    description: 'Popis chyby'
                ),
                new OA\Property(
                    property: 'details',
                    type: 'object',
                    description: 'Detailní informace o chybě',
                    properties: [
                        new OA\Property(
                            property: 'field',
                            type: 'string',
                            example: 'eventCode',
                            description: 'Název pole s chybou'
                        ),
                        new OA\Property(
                            property: 'error',
                            type: 'string',
                            example: 'Field is required',
                            description: 'Popis chyby pro dané pole',
                            enum: ['Field is required', 'Value is invalid']
                        )
                    ]
                )
            ]
        )
    )]
    #[OA\Response(
        response: 401,
        description: 'Neplatné nebo chybějící přihlašovací údaje',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'status',
                    type: 'string',
                    example: 'error',
                    description: 'Status odpovědi'
                ),
                new OA\Property(
                    property: 'code',
                    type: 'string',
                    example: 'UNAUTHORIZED',
                    description: 'Kód chyby'
                ),
                new OA\Property(
                    property: 'message',
                    type: 'string',
                    example: 'Authentication credentials are missing or invalid',
                    description: 'Popis chyby autentizace'
                )
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Požadovaný zdroj nebyl nalezen (neexistující endpoint nebo neplatné systemId)',
        content: new OA\JsonContent(
            properties: [
                new OA\Property(
                    property: 'status',
                    type: 'string',
                    example: 'error',
                    description: 'Status odpovědi'
                ),
                new OA\Property(
                    property: 'code',
                    type: 'string',
                    example: 'NOT_FOUND',
                    description: 'Kód chyby'
                ),
                new OA\Property(
                    property: 'message',
                    type: 'string',
                    example: 'Requested resource not found',
                    description: 'Popis chyby'
                )
            ]
        )
    )]
    public function __invoke(
        Request $request,
        string $systemId,
        ResponseGeneratorService $responseGenerator,
        SystemProviderService $systemProvider,
        NotificationService $notificationService,
    ): JsonResponse {
        // TODO: implement system provider
        if (
            !$systemProvider->isSystemValid($systemId)
        ) {
            return $responseGenerator->generateNotFoundRequestResponse();
        }

        // TODO: implement system provider check
        // if (!$system->isAvailable()) {
        //     return $responseGenerator->generateServiceUnavailableResponse();
        // }

        if (!$this->authorize($request)) {
            return $responseGenerator->generateUnauthorizedRequestResponse();
        }

        $whitelist = explode(',', $_ENV['NOTIFICATION_WHITELIST']);
        if (!in_array($request->getClientIp(), $whitelist)) {
            return $responseGenerator->generateForbiddenRequestResponse();
        }

        $claimNumber = $request->get('claimNumber');
        if (!$claimNumber) {
            return $responseGenerator->generateInvalidRequestResponse('claimNumber', 'Field is required');
        }

        $claimNumber = filter_var($claimNumber, FILTER_VALIDATE_INT);
        if (!$claimNumber) {
            return $responseGenerator->generateInvalidRequestResponse('claimNumber', 'Value is invalid');
        }

        $eventCode = $request->get('eventCode');
        if (!$eventCode) {
            return $responseGenerator->generateInvalidRequestResponse('eventCode', 'Field is required');
        }

        $eventCode = EventCode::fromString($request->get('eventCode'), false);
        if (!$eventCode) {
            return $responseGenerator->generateInvalidRequestResponse('eventCode', 'Value is invalid');
        }

        //TODO: implement notification handling
        // $success = $notificationService->sendNotification($system, $claimNumber, $eventCode);
        // if (!$success) {
        //     return $responseGenerator->generateInternalServerErrorResponse('Notification failed to send.');
        // }

        return $responseGenerator->generateSuccessResponse(123456789);
    }

    private function authorize(Request $request): bool
    {
        $authHeader = $request->headers->get('Authorization');
        if (!$authHeader || !str_starts_with($authHeader, 'Basic ')) {
            return false;
        }

        $encodedCredentials = substr($authHeader, 6); // Remove "Basic " prefix
        $decodedCredentials = base64_decode($encodedCredentials);
        if (!$decodedCredentials or !str_contains($decodedCredentials, ':')) {
            return false;
        }

        [$username, $password] = explode(':', $decodedCredentials, 2);

        $validUsername = $_ENV['BASIC_AUTH_USERNAME'];
        $validPassword = $_ENV['BASIC_AUTH_PASSWORD'];
        if ($username !== $validUsername or $password !== $validPassword) {
            return false;
        }

        return true;
    }
}
