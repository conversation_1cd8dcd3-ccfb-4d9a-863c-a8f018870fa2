<?php

namespace App\Controller\Poznamka;

use App\Repository\PojistnaUdalostRepository;
use App\Repository\PoznamkaRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/notes', name: 'app_poznamka_list')]
class PoznamkaListController extends AbstractController
{

    public function __invoke(
        Request $request,
        PoznamkaRepository $poznamkaRepository,
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        int $id
    ): Response {
        $loggedInUser = $this->getUser();
        $sortOrder = $request->query->get('sortOrder', 'desc');
        
        // Získání připnutých a nepřipnutých poznámek
        $pinnedPoznamky = $poznamkaRepository->findPinnedNotesByPojistnaUdalostId($id, $sortOrder);
        $unpinnedPoznamky = $poznamkaRepository->findUnpinnedNotesByPojistnaUdalostId($id, $sortOrder);
        
        $pojistnaUdalost = $pojistnaUdalostRepository->findOneByIdAndUser($id, $loggedInUser);

        return $this->render('poznamka/list.html.twig', [
            'pou' => $pojistnaUdalost,
            'pinnedPoznamky' => $pinnedPoznamky,
            'unpinnedPoznamky' => $unpinnedPoznamky,
            'sortOrder' => $sortOrder,
            'submenu_page'=> 'poznamky'
        ]);
    }
}
