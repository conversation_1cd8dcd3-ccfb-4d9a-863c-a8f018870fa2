<?php

namespace App\Controller\Poznamka;

use App\Dto\Poznamka\PoznamkaResolveInput;
use App\Entity\Poznamka;
use App\Entity\User;
use App\Helper\FlashMessageHelper;
use App\Security\Voter\PoznamkaVoter;
use App\Service\Poznamka\PoznamkaService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

class PoznamkaResolveController extends AbstractController
{
    #[Route('/note/{id}/resolve', name: 'app_poznamka_resolve')]
    public function __invoke(PoznamkaService $poznamkaService, Poznamka $poznamka): Response
    {
        if ($poznamka->getPojistnaUdalost()->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Označení poznámky k uzavřené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $poznamka->getPojistnaUdalost()->getId()]);
        }

        $currentUser = $this->getUser();
        if (!$currentUser instanceof User) {
            throw new AccessDeniedException('Nemáte právo označit tuto poznámku jako vyřešenou.');
        }

        $this->denyAccessUnlessGranted(PoznamkaVoter::RESOLVE, $poznamka, 'Nemáte oprávnění označit tuto poznámku jako vyřešenou.');


        $poznamkaResolveInput = PoznamkaResolveInput::createFromPoznamka($poznamka);
        $poznamkaService->resolvePoznamka($poznamka, $poznamkaResolveInput);
        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Poznámka byla úspěšně označena jako vyřešená.');

        return $this->redirectToRoute('app_poznamka_detail', ['id' => $poznamka->getId()]);
    }
}
