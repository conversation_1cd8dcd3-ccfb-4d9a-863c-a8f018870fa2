<?php

namespace App\Controller\Poznamka;

use App\Entity\Poznamka;
use App\Entity\User;
use App\Helper\FlashMessageHelper;
use App\Service\Soubor\SouborRemover;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

class PoznamkaDeleteController extends AbstractController
{
    #[Route('/note/{id}/delete', name: 'app_poznamka_delete')]
    public function __invoke(
        Poznamka $poznamka,
        EntityManagerInterface $entityManager,
        SouborRemover $souborRemover
    ): Response {
        if ($poznamka->getPojistnaUdalost()->isUzavreno()) {
            $this->addFlash('warning', 'Odstranění poznámky k uzav<PERSON>ené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $poznamka->getPojistnaUdalost()->getId()]);
        }

        try {
            // Ensure $poznamka->getAutor() returns an instance of User
            if (!$poznamka->getAutor() instanceof User) {
                throw new \Exception('Invalid author type.');
            }

            /** @var User $currentUser */
            $currentUser = $this->getUser();

            if ($poznamka->getAutor()->getId() !== $currentUser->getId()) {
                throw new AccessDeniedException('Nemáte oprávnění smazat tuto poznámku.');
            }

            $soubory = $poznamka->getSoubory();
            foreach ($soubory as $file) {
                $souborRemover->deleteFile($file);
                $entityManager->remove($file);
            }

            $entityManager->remove($poznamka);
            $entityManager->flush();

            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Komentář/Úkol ' . $poznamka->getId() . ' byl úspěšně smazán.');
        } catch (\Exception $e) {
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Došlo k chybě při mazání poznámky.');
        }
        return $this->redirectToRoute('app_poznamka_list', ['id' => $poznamka->getPojistnaUdalost()->getId()]);
    }
}
