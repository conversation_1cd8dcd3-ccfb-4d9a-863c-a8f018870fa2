<?php

namespace App\Controller\Poznamka;

use App\Dto\Poznamka\PoznamkaNewInput;
use App\Entity\PojistnaUdalost;
use App\Entity\SouborForPoznamka;
use App\Entity\User;
use App\Factory\Poznamka\PoznamkaNewFactory;
use App\Form\Poznamka\PoznamkaNewType;
use App\Helper\FlashMessageHelper;
use App\Repository\PojistnaUdalostRepository;
use App\Service\Notifikace\NotifikaceCreator;
use App\Service\SouborForPoznamka\SouborForPoznamkaUploader;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/new-note', name: 'app_poznamka_new')]
class PoznamkaNewController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        PoznamkaNewFactory $poznamkaNewFactory,
        PojistnaUdalostRepository $pouRepository,
        PojistnaUdalost $pojistnaUdalost,
        SouborForPoznamkaUploader $souborForPoznamkaUploader,
        NotifikaceCreator $notifikaceCreator
    ): RedirectResponse|Response {
        if ($pojistnaUdalost->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Přidání poznámky k uzavřené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        $poznamkaNewInput = new PoznamkaNewInput();
        $form = $this->createForm(PoznamkaNewType::class, $poznamkaNewInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $newPoznamka = $poznamkaNewFactory->createPoznamka($poznamkaNewInput);
            $newPoznamka->modifyPojistnaUdalost($pouRepository->find($pojistnaUdalost->getId()));
            /** @var User $author */
            $author = $this->getUser();
            $newPoznamka->modifyAutor($author);
            $entityManager->persist($newPoznamka);
            $entityManager->flush();


            $soubory = $form->get('soubory')->getData();
            if (!empty($soubory)) {
                // handle file upload


                foreach ($soubory as $file) {
                    // store soubor in slozka
                    if ($file instanceof UploadedFile) {
                        /**
                         * @var SouborForPoznamka $soubor
                         */
                        $soubor = $souborForPoznamkaUploader->storeSouborForPoznamka(
                            $file,
                            $newPoznamka
                        );

                        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Soubor ' . $soubor->getOriginalFilename() . ' úspěšně uložen');
                    }
                }
            }

            // Pokud je poznámka vytvořena jako úkol a má přiřazeného řešitele, vytvoříme notifikaci
            if ($newPoznamka->isTask() && $newPoznamka->getResitel()) {
                try {
                    $notifikaceCreator->createPoznamkaTaskAssignmentNotifikace($newPoznamka, true);
                    $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Notifikace o přiřazení úkolu byla odeslána řešiteli');
                } catch (\Exception $e) {
                    $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Nepodařilo se odeslat notifikaci o přiřazení úkolu: ' . $e->getMessage());
                }
            }

            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Nová poznámka úspěšně přidána');
            return $this->redirectToRoute('app_poznamka_list', ['id' => $pojistnaUdalost->getId()]);
        }

        return $this->render('poznamka/new.html.twig', [
            'form' => $form->createView(),
            'cislo_pu' => $pojistnaUdalost->getCisloPojistnaUdalost(),
            'pou' => $pojistnaUdalost,
            'submenu_page' => 'poznamky'
        ]);
    }
}
