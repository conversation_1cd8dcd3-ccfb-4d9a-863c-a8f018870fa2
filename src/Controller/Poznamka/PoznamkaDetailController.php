<?php

namespace App\Controller\Poznamka;

use App\Entity\Poznamka;
use App\Entity\User;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

class PoznamkaDetailController extends AbstractController
{
    #[Route('/note/{id}', name: 'app_poznamka_detail')]
    public function __invoke(Poznamka $poznamka, Request $request): Response
    {

        $fromTasklist = $request->query->get('taskslist', 0);


        $user = $this->getUser();

        if (!$user instanceof User) {
            throw new AccessDeniedException('Nemáte právo zobrazit tuto poznámku.');
        }

        if (!$poznamka->canView($user)) {
            throw new AccessDeniedException('Nemáte právo zobrazit tuto poznámku.');
        }

        return $this->render('poznamka/detail.html.twig', [
            'poznamka' => $poznamka,
            'pou' => $poznamka->getPojistnaUdalost(),
            'submenu_page' => ($fromTasklist == 1 ? 'ukoly' : 'poznamky'),
            'from_tasklist' => $fromTasklist
        ]);
    }
}
