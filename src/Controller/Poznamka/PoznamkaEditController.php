<?php

namespace App\Controller\Poznamka;

use App\Dto\Poznamka\PoznamkaEditDueDateInput;
use App\Dto\Poznamka\PoznamkaEditInput;
use App\Entity\Poznamka;
use App\Entity\SouborForPoznamka;
use App\Entity\User;
use App\Form\Poznamka\PoznamkaEditDueDateType;
use App\Helper\FlashMessageHelper;
use App\Form\Poznamka\PoznamkaEditType;
use App\Security\Voter\PoznamkaVoter;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use App\Service\Poznamka\PoznamkaService;
use App\Service\SouborForPoznamka\SouborForPoznamkaUploader;
use Symfony\Component\HttpFoundation\File\UploadedFile;

#[Route('/note/{id}/edit', name: 'app_poznamka_edit')]
class PoznamkaEditController extends AbstractController
{


    public function __invoke(
        Request $request,
        PoznamkaService $poznamkaService,
        Poznamka $poznamka,
        SouborForPoznamkaUploader $souborForPoznamkaUploader
    ): Response {

        if ($poznamka->getPojistnaUdalost()->isUzavreno()) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Upravení poznámky k uzavřené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $poznamka->getPojistnaUdalost()->getId()]);
        }

        // Získání aktuálního uživatele
        /** @var User $currentUser */
        $currentUser = $this->getUser();

        if ($this->isGranted(PoznamkaVoter::FULL_EDIT, $poznamka)) {
            return $this->editFull($request, $poznamkaService, $poznamka, $souborForPoznamkaUploader);
        }

        $this->denyAccessUnlessGranted(PoznamkaVoter::DATE_EDIT, $poznamka, 'Nemáte oprávnění upravit tuto poznámku.');

        return $this->editDueDate($request, $poznamkaService, $poznamka);
    }


    private function editFull(
        Request $request,
        PoznamkaService $poznamkaService,
        Poznamka $poznamka,
        SouborForPoznamkaUploader $souborForPoznamkaUploader
    ): Response {
        // Získání návratové cesty z parametru, pokud existuje
        $returnRoute = $request->query->get('returnRoute', 'app_poznamka_list');
        $poznamkaEditInput = PoznamkaEditInput::createFromPoznamka($poznamka);
        $form = $this->createForm(PoznamkaEditType::class, $poznamkaEditInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $poznamka = $poznamkaService->updatePoznamka($poznamka, $poznamkaEditInput);

            $soubory = $form->get('soubory')->getData();
            if (!empty($soubory)) {
                // handle file upload


                foreach ($soubory as $file) {
                    // store soubor in slozka
                    if ($file instanceof UploadedFile) {
                        /**
                         * @var SouborForPoznamka $soubor
                         */
                        $soubor = $souborForPoznamkaUploader->storeSouborForPoznamka(
                            $file,
                            $poznamka
                        );

                        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Soubor ' . $soubor->getOriginalFilename() . ' úspěšně uložen');
                    }
                }
            }


            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Poznámka úspěšně upravena');

            // Přesměrování na správnou stránku podle parametru returnRoute
            if ($returnRoute === 'app_poznamka_task_list') {
                return $this->redirectToRoute('app_poznamka_task_list', ['id' => $poznamka->getPojistnaUdalost()->getId()]);
            } else {
                return $this->redirectToRoute('app_poznamka_list', ['id' => $poznamka->getPojistnaUdalost()->getId()]);
            }
        }

        return $this->render('poznamka/edit.html.twig', [
            'form' => $form->createView(),
            'cislo_pu' => $poznamka->getPojistnaUdalost()->getCisloPojistnaUdalost(),
            'pou' => $poznamka->getPojistnaUdalost(),
            'submenu_page' => 'poznamky',
            'returnRoute' => $returnRoute
        ]);
    }

    private function editDueDate(
        Request $request,
        PoznamkaService $poznamkaService,
        Poznamka $poznamka
    ): Response {
        $editInput = PoznamkaEditDueDateInput::createFromPoznamka($poznamka);
        $form = $this->createForm(PoznamkaEditDueDateType::class, $editInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $poznamkaService->updatePoznamkaDueDate($poznamka, $editInput);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Poznámka úspěšně vyřešena');

            // Získání návratové cesty z parametru, pokud existuje
            $returnRoute = $request->query->get('returnRoute', 'app_poznamka_list');

            if ($returnRoute === 'app_poznamka_task_list') {
                return $this->redirectToRoute('app_poznamka_task_list', ['id' => $poznamka->getPojistnaUdalost()->getId()]);
            } else if ($returnRoute === 'app_poznamka_list') {
                return $this->redirectToRoute('app_poznamka_list', ['id' => $poznamka->getPojistnaUdalost()->getId()]);
            } else {
                return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $poznamka->getPojistnaUdalost()->getId()]);
            }
        }

        return $this->render('poznamka/edit_due_date.html.twig', [
            'form' => $form->createView(),
            'cislo_pu' => $poznamka->getPojistnaUdalost()->getCisloPojistnaUdalost(),
            'pou' => $poznamka->getPojistnaUdalost(),
            'submenu_page' => 'poznamky'
        ]);
    }
}
