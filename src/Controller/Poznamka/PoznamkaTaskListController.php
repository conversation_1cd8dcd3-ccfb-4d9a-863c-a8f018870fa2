<?php

namespace App\Controller\Poznamka;

use App\Repository\PojistnaUdalostRepository;
use App\Repository\PoznamkaRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/notes/tasks', name: 'app_poznamka_task_list')]
class PoznamkaTaskListController extends AbstractController
{

    public function __invoke(
        Request $request,
        PoznamkaRepository $poznamkaRepository,
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        int $id
    ): Response {
        $loggedInUser = $this->getUser();
        $sortOrder = $request->query->get('sortOrder', 'desc');
        $pojistnaUdalost = $pojistnaUdalostRepository->findOneByIdAndUser($id, $loggedInUser);

        
        $tasks = $poznamkaRepository->findAllUnfinishedTasksByPojistnaUdalostId($id, $sortOrder);

        return $this->render('poznamka/tasklist.html.twig', [
            'pou' => $pojistnaUdalost,
            'tasks' => $tasks,
            'sortOrder' => $sortOrder,
            'submenu_page'=> 'ukoly'
        ]);
    }
}
