<?php

namespace App\Controller\Rizika;

use App\Dto\Rizika\RizikaEditInput;
use App\Entity\Rizika;
use App\Helper\FlashMessageHelper;
use App\Form\Rizika\RizikaEditType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use App\Service\Rizika\RizikaService;

#[Route('/rizika/{id}/edit', name: 'app_rizika_edit')]
class RizikaEditController extends AbstractController
{
    public function __invoke(Request $request, EntityManagerInterface $entityManager,RizikaService $rizikaService, int $id): Response {
        $rizika = $entityManager->getRepository(Rizika::class)->find($id);

        if (!$rizika) {
            throw $this->createNotFoundException('Riziko pojistný typ nenalezena');
        }

        $rizikaEditInput = RizikaEditInput::createFromRizika($rizika);
        $form = $this->createForm(RizikaEditType::class, $rizikaEditInput);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $rizikaService->updateRizika($rizika, $rizikaEditInput);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Pojistný typ rizika úspěšně upraven');
            return $this->redirectToRoute('app_rizika_list', ['id' => $rizika->getId()]);
        }

        return $this->render('rizika/edit.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}