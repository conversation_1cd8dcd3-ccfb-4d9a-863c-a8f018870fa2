<?php

namespace App\Controller\Rizika;

use App\Dto\Rizika\RizikaNewInput;
use App\Factory\Rizika\RizikaNewFactory;
use App\Form\Rizika\RizikaNewType;
use App\Helper\FlashMessageHelper;
use App\Service\Rizika\RizikaNewService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/rizika/new', name: 'app_rizika_new')]
class RizikaNewController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        RizikaNewFactory $rizikaNewFactory,
    ): Response {
        $rizikaNewInput = new RizikaNewInput();
        $form = $this->createForm(RizikaNewType::class, $rizikaNewInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {         
            $newRizika = $rizikaNewFactory->createRizika($rizikaNewInput);
            $entityManager->persist($newRizika);
            $entityManager->flush();
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Nové riziko přidáno');

            return $this->redirectToRoute('app_rizika_list');
        }

        return $this->render('rizika/new.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}