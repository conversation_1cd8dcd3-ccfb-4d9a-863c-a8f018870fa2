<?php

namespace App\Controller\Rizika;

use App\Repository\RizikaRepository;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/rizika', name: 'app_rizika_list')]
class RizikaListController extends AbstractController
{
    public function __invoke(Request $request,
        RizikaRepository $rizikaRepository,
        PaginatorInterface $paginator
    ): Response {

        $query = $rizikaRepository->getAllQuery();

        $pagination = $paginator->paginate(
            $query,
            $request->query->getInt('page', 1),
            50
        );

        return $this->render('rizika/list.html.twig', [
            'pagination' => $pagination,
        ]);
    }
}
