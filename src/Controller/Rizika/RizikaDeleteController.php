<?php

namespace App\Controller\Rizika;

use App\Entity\Rizika;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/rizika/{id}/delete', name: 'app_rizika_delete')]
class RizikaDeleteController extends AbstractController
{
    public function __invoke(Request $request, EntityManagerInterface $entityManager, Rizika $rizika): Response
    {
        $entityManager->remove($rizika);
        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Pojistný typ rizika úspěšně odstraněn');
        $entityManager->flush();

        return $this->redirectToRoute('app_rizika_list');
    }
}
