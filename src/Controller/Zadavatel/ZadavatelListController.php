<?php

namespace App\Controller\Zadavatel;

use App\Repository\ZadavatelRepository;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/zadavatel', name: 'zadavatel_list')]
class ZadavatelListController extends AbstractController
{
    public function __invoke(
        Request $request,
        ZadavatelRepository $zadavatelRepository,
        PaginatorInterface $paginator
    ): Response {

        $query  = $zadavatelRepository->getAllQuery();

        $pagination = $paginator->paginate(
            $query, /* query NOT result */
            $request->query->getInt('page', 1), /*page number*/
            10
        );        

        return $this->render('zadavatel/list.html.twig', [
            'pagination' => $pagination,
        ]);
    }
}
