<?php

namespace App\Controller\Zadavatel;

use App\Dto\Zadavatel\ZadavatelNewInput;
use App\Factory\Zadavatel\ZadavatelNewFactory;
use App\Helper\FlashMessageHelper;
use App\Form\Zadavatel\ZadavatelNewType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/zadavatel/new', name: 'zadavatel_new')]
class ZadavatelNewController extends AbstractController
{
    public function __invoke(
        Request $request,
        ZadavatelNewFactory $zadavatelNewFactory,
        EntityManagerInterface $entityManager
    ): Response
    {
        $zadavatelNewInput = new ZadavatelNewInput();
        $form = $this->createForm(ZadavatelNewType::class, $zadavatelNewInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $zadavatelNewInput = $form->getData();
            $newZadavatel = $zadavatelNewFactory->createZadavatel($zadavatelNewInput);
            $entityManager->persist($newZadavatel);
            $entityManager->flush();

            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Nový zadavatel úspěšně přidán');
            return $this->redirectToRoute('zadavatel_list');
        }

        return $this->render('zadavatel/new.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}
