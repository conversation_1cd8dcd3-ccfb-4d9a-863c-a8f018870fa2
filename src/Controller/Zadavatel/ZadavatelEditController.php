<?php

namespace App\Controller\Zadavatel;

use App\Dto\Zadavatel\ZadavatelEditInput;
use App\Form\Zadavatel\ZadavatelEditType;
use App\Helper\FlashMessageHelper;
use App\Repository\ZadavatelRepository;
use App\Service\Zadavatel\ZadavatelUpdateService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/zadavatel/{id}/edit', name: 'zadavatel_edit')]
class ZadavatelEditController extends AbstractController
{
    public function __invoke(Request $request, ZadavatelUpdateService $zadavatelService, ZadavatelRepository $zadavatelRepository, int $id): Response
    {
        $zadavatel = $zadavatelRepository->find($id);
        if (!$zadavatel) {
            throw $this->createNotFoundException('Zadavatel nenalezen.');
        }

        $zadavatelEditInput = ZadavatelEditInput::createFromZadavatel($zadavatel);
        $form = $this->createForm(ZadavatelEditType::class, $zadavatelEditInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $updatedZadavatel = $zadavatelService->updateZadavatel($zadavatel, $zadavatelEditInput);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Zadavatel úspěšně upraven');
            return $this->redirectToRoute('zadavatel_list');
        }

        return $this->render('zadavatel/edit.html.twig', [
            'form' => $form->createView(),
            'zadavatel' => $zadavatel
        ]);
    }
}
