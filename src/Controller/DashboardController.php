<?php

namespace App\Controller;

use App\Entity\User;
use App\Service\PojistnaUdalost\PojistnaUdalostDashboardService;
use App\Service\Poznamka\PoznamkaDashboardService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class DashboardController extends AbstractController
{
    #[Route('/dashboard', name: 'app_dashboard')]
    public function index(
        PojistnaUdalostDashboardService $pojistnaUdalostDashboardService,
        PoznamkaDashboardService $poznamkaDashboardService
    ): Response {
        return $this->render('dashboard/index.html.twig', [
            'puCounts' => $pojistnaUdalostDashboardService->getPojistnaUdalostCounts($this->getUser()),
            'messageCounts' => $pojistnaUdalostDashboardService->getMessageCounts($this->getUser()),
            'taskBoxes' => $poznamkaDashboardService->getTaskBoxes($this->getUser()),
            'user' => $this->getUser()
        ]);
    }
}
