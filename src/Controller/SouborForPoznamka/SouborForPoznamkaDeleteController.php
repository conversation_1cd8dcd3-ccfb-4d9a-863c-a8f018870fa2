<?php

namespace App\Controller\SouborForPoznamka;

use App\Helper\FlashMessageHelper;
use App\Repository\SouborForPoznamkaRepository;
use App\Service\Soubor\SouborRemover;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class SouborForPoznamkaDeleteController extends AbstractController
{
    #[Route('/note/soubor/{id}/delete', name: 'app_note_soubor_delete', methods: ['GET'])]
    public function __invoke(
        SouborForPoznamkaRepository $souborForPoznamkaRepository,
        EntityManagerInterface $entityManager,
        SouborRemover $souborRemover,
        int $id
    ): Response {
        $soubor = $souborForPoznamkaRepository->find($id);

        if (!$soubor) {
            throw $this->createNotFoundException('Soubor nebyl nalezen');
        }

        $poznamka = $soubor->getPoznamka();
        $poznamkaId = $poznamka->getId();

        try {
            $souborRemover->deleteFile($soubor);
            $entityManager->remove($soubor);
            $entityManager->flush();
        } catch (\Throwable $th) {
            $entityManager->rollback();
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Při mazání souboru ' . $soubor->getOriginalFilename() . ' došlo k chybě');
        }

        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Soubor ' . $soubor->getOriginalFilename() . ' úspěšně vymazán');

        return $this->redirectToRoute('app_poznamka_detail', ['id' => $poznamkaId]);
    }
}
