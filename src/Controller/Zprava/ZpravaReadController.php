<?php

namespace App\Controller\Zprava;

use App\Entity\Notifikace;
use App\Entity\PojistnaUdalost;
use App\Repository\NotifikaceRepository;
use App\Repository\PojistnaUdalostRepository;
use App\Repository\UserRepository;
use App\Repository\ZadavatelRepository;
use App\Service\Notifikace\FilterManager;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Polyfill\Intl\Icu\Exception\NotImplementedException;

class ZpravaReadController extends AbstractController
{
    #[Route('/zprava/{id}', name: 'app_zprava_read')]
    public function index(Notifikace $notifikace): Response
    {
        return $this->render('zprava/read.html.twig', [
            'notification' => $notifikace,
        ]);
    }

    #[Route('/zprava/{id}/precteno', name: 'app_zprava_toggle_read')]
    public function toggleRead(
        Request $request,
        Notifikace $notifikace,
        EntityManagerInterface $em
    ): Response
    {
        $notifikace->togglePrecteno();
        $em->persist($notifikace);
        $em->flush();

        if (!$request->request->get('redirect', true))
            return $this->redirectToRoute('app_zprava_list');
    
        return new Response(json_encode(['read' => $notifikace->isPrecteno()]));
    }
}
