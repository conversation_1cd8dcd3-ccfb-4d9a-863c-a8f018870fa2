<?php

namespace App\Controller\Zprava;

use App\Entity\User;
use App\Service\Notifikace\FilterManager;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/zprava', name: 'app_zprava_list')]
class ZpravaListController extends AbstractController
{
    public function __invoke(
        Request $request,
        FilterManager $filterManager,
        PaginatorInterface $paginator
    ): Response {
        /**
         * @var User $user
         */
        $user = $this->getUser();
        $query = $filterManager->getQuery($user, $request);

        $pagination = $paginator->paginate(
            $query,
            $request->query->getInt('page', 1),
            100
        );

        return $this->render('zprava/list.html.twig', [
            'pagination' => $pagination,
            'types' => $filterManager->getTypes($request),
            'dates' => $filterManager->getDates($request),
            'filterActive' => $filterManager->getActiveFilter($request),
        ]);
    }
}
