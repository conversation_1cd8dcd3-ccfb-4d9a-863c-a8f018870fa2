<?php

namespace App\Controller\UserRole;

use App\Entity\UserRole;
use App\Helper\FlashMessageHelper;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class UserRoleDeleteController extends AbstractController
{
    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    #[Route('/user/role/check-delete/{id}', name: 'app_user_role_check_delete', methods: ['GET'])]
    public function checkDelete(UserRole $userRole): JsonResponse
    {
        $hasUsers = $this->userRepository->hasUsersWithRole($userRole);
        
        return new JsonResponse([
            'canDelete' => !$hasUsers,
            'message' => $hasUsers ? 'Tuto roli nelze smazat, protože je přiřazena uživatelům.' : null
        ]);
    }

    #[Route('/user/role/delete/{id}', name: 'app_user_role_delete', methods: ['POST'])]
    public function __invoke(Request $request, UserRole $userRole, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$userRole->getId(), $request->request->get('_token'))) {
            // Check if users are assigned to this role
            $hasUsers = $this->userRepository->hasUsersWithRole($userRole);
            
            if ($hasUsers) {
                $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Tuto roli nelze smazat, protože je přiřazena uživatelům.');
            } else {
                $entityManager->remove($userRole);
                $entityManager->flush();
                
                $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Role byla úspěšně smazána.');
            }
        } else {
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Neplatný CSRF token.');
        }

        return $this->redirectToRoute('app_user_role_list');
    }
}
