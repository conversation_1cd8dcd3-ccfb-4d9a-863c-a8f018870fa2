<?php

namespace App\Controller\UserRole;

use App\Dto\UserRole\UserRoleEditInput;
use App\Entity\UserRole;
use App\Form\UserRole\UserRoleEditType;
use App\Helper\FlashMessageHelper;
use App\Repository\UserRepository;
use App\Service\UserRole\UserRoleEditService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class UserRoleEditController extends AbstractController
{
    private UserRoleEditService $userRoleEditService;
    private UserRepository $userRepository;

    public function __construct(
        UserRoleEditService $userRoleEditService,
        UserRepository $userRepository
    ) {
        $this->userRoleEditService = $userRoleEditService;
        $this->userRepository = $userRepository;
    }

    #[Route('/user/role/edit/{id}', name: 'app_user_role_edit')]
    public function __invoke(Request $request, UserRole $userRole): Response
    {
        // Check if users with this role exist
        $hasUsers = $this->userRepository->hasUsersWithRole($userRole);
        
        $userRoleInput = UserRoleEditInput::fromEntity($userRole);
        $form = $this->createForm(UserRoleEditType::class, $userRoleInput, [
            'has_users' => $hasUsers, // Pass this to the form
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // If users with this role exist, preserve the original name
            if ($hasUsers) {
                $userRoleInput->name = $userRole->getName();
            }
            
            $updatedUserRole = $this->userRoleEditService->editUserRole($userRole, $userRoleInput);
            
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, sprintf('Role "%s" byla úspěšně aktualizována.', $updatedUserRole->getName()));
            
            return $this->redirectToRoute('app_user_role_list');
        }

        return $this->render('user_role/edit/index.html.twig', [
            'form' => $form->createView(),
            'userRole' => $userRole,
            'has_users' => $hasUsers,
        ]);
    }
}
