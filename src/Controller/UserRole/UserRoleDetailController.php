<?php

namespace App\Controller\UserRole;

use App\Entity\UserRole;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class UserRoleDetailController extends AbstractController
{
    #[Route('/user/role/detail/{id}', name: 'app_user_role_detail')]
    public function __invoke(UserRole $userRole): Response
    {
        return $this->render('user_role/detail/index.html.twig', [
            'userRole' => $userRole,
        ]);
    }
}
