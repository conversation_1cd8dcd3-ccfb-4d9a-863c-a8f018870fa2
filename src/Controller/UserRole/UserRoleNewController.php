<?php

namespace App\Controller\UserRole;

use App\Dto\UserRole\UserRoleNewInput;
use App\Form\UserRole\UserRoleNewType;
use App\Helper\FlashMessageHelper;
use App\Service\UserRole\UserRoleService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class UserRoleNewController extends AbstractController
{
    private UserRoleService $userRoleService;

    public function __construct(UserRoleService $userRoleService)
    {
        $this->userRoleService = $userRoleService;
    }

    #[Route('/user/role/new', name: 'app_user_role_new')]
    public function __invoke(Request $request): Response
    {
        $userRoleInput = new UserRoleNewInput();
        $form = $this->createForm(UserRoleNewType::class, $userRoleInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $userRole = $this->userRoleService->createUserRole($userRoleInput);
            
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, sprintf('Role "%s" byla úspěšně vytvořena.', $userRole->getName()));
            
            return $this->redirectToRoute('app_user_role_list');
        }

        return $this->render('user_role/new/index.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}
