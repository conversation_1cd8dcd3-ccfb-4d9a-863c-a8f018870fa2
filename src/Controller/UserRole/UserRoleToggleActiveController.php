<?php

namespace App\Controller\UserRole;

use App\Entity\UserRole;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class UserRoleToggleActiveController extends AbstractController
{
    #[Route('/user/role/toggle-active/{id}', name: 'app_user_role_toggle_active')]
    public function __invoke(UserRole $userRole, EntityManagerInterface $entityManager): Response
    {
        $userRole->toggleActive();
        
        $entityManager->flush();
        
        $status = $userRole->isActive() ? 'aktivována' : 'deaktivována';
        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, sprintf('Role "%s" byla ú<PERSON>ně %s.', $userRole->getName(), $status));

        return $this->redirectToRoute('app_user_role_list');
    }
}
