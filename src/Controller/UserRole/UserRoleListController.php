<?php

namespace App\Controller\UserRole;

use App\Repository\UserRepository;
use App\Repository\UserRoleRepository;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class UserRoleListController extends AbstractController
{
    private UserRepository $userRepository;
    
    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }
    
    #[Route('/user/role/list', name: 'app_user_role_list')]
    public function __invoke(Request $request, UserRoleRepository $userRoleRepository, PaginatorInterface $paginator): Response
    {
        $query = $userRoleRepository->createQueryBuilder('ur')
            ->orderBy('ur.name', 'ASC')
            ->getQuery();

        $pagination = $paginator->paginate(
            $query, 
            $request->query->getInt('page', 1), 
            10
        );
        
        // Check which roles can be deleted (don't have users assigned)
        $deletableRoles = [];
        foreach ($pagination->getItems() as $role) {
            $deletableRoles[$role->getId()] = !$this->userRepository->hasUsersWithRole($role);
        }

        return $this->render('user_role/list/index.html.twig', [
            'pagination' => $pagination,
            'currentUser' => $this->getUser(),
            'searchString' => '',
            'deletableRoles' => $deletableRoles
        ]);
    }
}
