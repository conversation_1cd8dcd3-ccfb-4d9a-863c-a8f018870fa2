<?php

namespace App\Controller\Soubor;


use App\Repository\SlozkaRepository;
use App\Repository\SouborRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class SouborListController extends AbstractController
{
    #[Route('/soubor/slozka/{slozkaId}', name: 'app_soubor_list_by_slozka')]
    public function __invoke(
        int $slozkaId,
        SouborRepository $souborRepository,
        SlozkaRepository $slozkaRepository
    ): Response {
        $slozka = $slozkaRepository->find($slozkaId);
        
        if (!$slozka) {
            throw $this->createNotFoundException('Složka nebyla nalezena.');
        }
        
        $soubory = $souborRepository->findBySlozka($slozka);
        
        return $this->render('soubor/list.html.twig', [
            'soubory' => $soubory,
            'slozka' => $slozka
        ]);
    }
}
