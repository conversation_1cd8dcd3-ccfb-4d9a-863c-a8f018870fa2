<?php

namespace App\Controller\Soubor;

use App\Repository\SouborRepository;
use App\Service\Soubor\SouborReader;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\String\Slugger\SluggerInterface;

class SouborReadController extends AbstractController
{

    #[Route('/soubor/{id}', name: 'app_soubor_read', methods: ['GET'])]
    public function __invoke(
        SouborRepository $souborRepository,
        SouborReader $souborReader,
        SluggerInterface $slugger,
        int $id
    ):Response
    {

        $soubor =  $souborRepository->find($id);
        
        if (!$soubor) {
            throw $this->createNotFoundException('Soubor nebyl nalezen.');
        }             
        
        $response = new StreamedResponse(function () use ($soubor, $souborReader) {
            $outputStream = fopen('php://output', 'wb');
            $fileStream = $souborReader->readSouborAsStream($soubor);
            stream_copy_to_stream($fileStream, $outputStream);
        });
        $response->headers->set('Content-Type', $soubor->getMimeType());
        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_INLINE,
            $soubor->getOriginalFilename(),
            $slugger->slug($soubor->getOriginalFilename(),'_'),

        );
        $response->headers->set('Content-Disposition', $disposition);

        return $response;
    }

}
