<?php

namespace App\Controller\Soubor;

use App\Helper\FlashMessageHelper;
use App\Repository\SouborRepository;
use App\Service\Soubor\SouborRemover;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class SouborDeleteController extends AbstractController
{

    #[Route('/soubor/{id}/delete', name: 'app_soubor_delete', methods: ['GET'])]
    public function __invoke(
        SouborRepository $souborRepository,
        EntityManagerInterface $entityManager,
        SouborRemover $souborRemover,
        int $id
    ): Response {

        $soubor = $souborRepository->find($id);

        if (!$soubor) {
            throw $this->createNotFoundException('Soubor nebyl nalezen');
        }

        $slozka =  $soubor->getSlozka();
        $slozkaId = $slozka->getId();

        try {
            $souborRemover->deleteFile($soubor);
            $entityManager->remove($soubor);
            $entityManager->flush();
        } catch (\Throwable $th) {
            $entityManager->rollback();
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Při mazání souboru ' . $soubor->getOriginalFilename() . ' došlo k chybě');
        }

        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Soubor ' . $soubor->getOriginalFilename() . ' úspěšně vymazán');

        return $this->redirectToRoute('app_soubor_list_by_slozka', ['slozkaId' => $slozkaId]);
    }
}
