<?php

namespace App\Controller\Soubor;

use App\Repository\PojistnaUdalostRepository;
use App\Service\Soubor\SouborUploader;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SouborUploadController extends AbstractController
{


    #[Route('/soubor/upload/{id}/{type}', name: 'app_soubor_upload', methods: ['POST'])]
    public function __invoke(
        Request $request,
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        ValidatorInterface $validator,
        SouborUploader $souborUploader,
        int $id,
        string $type
    ): Response {
        $pojistnaUdalost =  $pojistnaUdalostRepository->find($id);

        try {

            $file = $request->files->get('soubor');
            if (!$file) {
                throw new \Exception('Nebyl poskytnut žádný soubor');
            }

            // validate mime type

            $errors = $validator->validate(
                $file,
                [
                    new NotBlank(),
                    new File([
                        'mimeTypes' => [
                            'application/pdf',
                            'application/msword',
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                            'application/vnd.oasis.opendocument.text',
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                        ]
                    ])
                ]
            );

            if ($errors->count() > 0) {
                $message = '';
                foreach ($errors as $error) {
                    $message .= $error->getMessage() . ' <br>';
                }
                return $this->json([
                    'success' => false,
                    'message' => $message,
                    'type' => $type
                ], 400);
            }


            if ($file instanceof UploadedFile) {
                $soubor = $souborUploader->storeSouborDoSlozky($file, $pojistnaUdalost, $type);
            }

            return $this->json([
                'success' => true,
                'message' => sprintf('Soubor úspěšně nahrán do kategorie %s', $type),
                'fileName' => 'NEJAKY FILE',
                'type' => $type
            ]);
        } catch (Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage(),
                'type' => $type
            ], 400);
        }
    }
}
