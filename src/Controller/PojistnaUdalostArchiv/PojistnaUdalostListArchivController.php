<?php

namespace App\Controller\PojistnaUdalostArchiv;

use App\Repository\PojistnaUdalostArchivRepository;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/archivpu', name: 'app_pojistna_udalost_list_archiv')]
class PojistnaUdalostListArchivController extends AbstractController
{
    public function __invoke(Request $request,
        PojistnaUdalostArchivRepository $pojistnaUdalostArchivRepository,
        PaginatorInterface $paginator
    ): Response {

        $query = $pojistnaUdalostArchivRepository->getAllQuery();

        $pagination = $paginator->paginate(
            $query,
            $request->query->getInt('page', 1),
            50
        );

        return $this->render('pojistna_udalost_archiv/list.html.twig', [
            'pagination' => $pagination,
        ]);
    }
}