<?php

namespace App\Controller\PojistnaUdalostArchiv;

use App\Repository\PojistnaUdalostArchivRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Doctrine\ORM\EntityManagerInterface;
use App\Service\PojistnaUdalost\PojistnaUdalostRemoveFromArchiveService;
use App\Entity\PojistnaUdalost;
use App\Helper\FlashMessageHelper;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class PojistnaUdalostChangeArchiveController extends AbstractController
{
    #[Route('/archivpu/{id}/change', name: 'app_pojistna_udalost_change_archive')]
    public function __invoke(int $id, EntityManagerInterface $entityManager, PojistnaUdalostRemoveFromArchiveService $pojistnaUdalostRemoveFromArchiveService): Response
    {
        $pojistnaUdalost = $entityManager->getRepository(PojistnaUdalost::class)->find($id);

        if (!$pojistnaUdalost) {
            throw $this->createNotFoundException('Pojistna udalost nenalezena');
        }

        $pojistnaUdalostRemoveFromArchiveService->removeFromArchivePojistnaUdalost($pojistnaUdalost);
        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Pojistná událost úspěšně vyjmuta z archivu');
        
        return $this->redirectToRoute('app_pojistna_udalost_list_archiv');
    }
}
