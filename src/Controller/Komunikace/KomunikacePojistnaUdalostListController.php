<?php

namespace App\Controller\Komunikace;

use App\Repository\PojistnaUdalostRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/pou/{id}/communication', name: 'app_komunikace_pu_list')]
class KomunikacePojistnaUdalostListController extends AbstractController
{

    public function __invoke(
        Request $request,
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        int $id
    ): Response {
        $loggedInUser = $this->getUser();
        $sortOrder = $request->query->get('sortOrder', 'desc');
               
        $pojistnaUdalost = $pojistnaUdalostRepository->findOneByIdAndUser($id, $loggedInUser);

        return $this->render('komunikace/list.html.twig', [
            'pou' => $pojistnaUdalost,
            'sortOrder' => $sortOrder,
            'submenu_page'=> 'komunikace'
        ]);
    }
}
