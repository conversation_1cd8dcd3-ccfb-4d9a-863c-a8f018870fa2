<?php

namespace App\Controller\LikvidacniProtokol;

use App\Entity\LikvidacniProtokol;
use App\Helper\FlashMessageHelper;
use App\Repository\PojistnaUdalostRepository;
use App\Service\LikvidacniProtokol\LikvidacniProtokolUploader;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Validator\ValidatorInterface;


#[IsGranted('ROLE_COM_ADMIN')]
class LikvidacniProtokolUploadController extends AbstractController
{

    #[Route('/protokol/upload/{id}', name: 'app_likvidacni_protokol_upload', methods: ['POST'])]
    public function _invoke(
        Request $request,
        LikvidacniProtokolUploader $likvidacniProtokolUploader,
        PojistnaUdalostRepository $pojistnaUdalostRepository,
        ValidatorInterface $validator,
        int $id
    ): Response {
        $pojistnaUdalost =  $pojistnaUdalostRepository->find($id);

        /**
         * @var UploadedFile $soubor
         */
        $soubor = $request->files->get('protokol');
        $description = $request->request->get('description');

        // validate mime type

        $errors = $validator->validate(
            $soubor,
            [
                new NotBlank(),
                new File([
                    'mimeTypes' => [
                        'application/pdf',
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'application/vnd.oasis.opendocument.text'
                    ]
                ])
            ]
        );

        $errors2 = $validator->validate(
            $description,
            new NotBlank()
        );
        

        if ($errors->count() > 0 || $errors2->count() > 0) {
            foreach ($errors as $error) {
                $this->addFlash(FlashMessageHelper::TYPE_ERROR, $error->getMessage());
            }
            foreach ($errors2 as $error) {
                $this->addFlash(FlashMessageHelper::TYPE_ERROR, $error->getMessage());
            }
            return $this->redirectToRoute('app_pojistna_udalost_ukonceni', ['id' => $id]);
        }

        // validate files size

        if ($soubor instanceof UploadedFile) {

            $likvidacniProtokol = $likvidacniProtokolUploader->uploadLikvidacniProtokolSoubor($soubor, $pojistnaUdalost, $description);
        }

        if ($likvidacniProtokol instanceof LikvidacniProtokol)
        {
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS,'Soubor '.$likvidacniProtokol->getOriginalFilename().' byl úspěšně nahraný');
        }

        if (is_null($likvidacniProtokol))
        {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING,' Stale se nějaká chyba při nahrávání souboru, kontaktujte podporu');
        }

        
        return $this->redirectToRoute('app_pojistna_udalost_ukonceni', ['id' => $id]);
    }
}
