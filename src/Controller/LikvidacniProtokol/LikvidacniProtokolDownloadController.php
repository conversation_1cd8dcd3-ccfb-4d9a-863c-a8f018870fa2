<?php

namespace App\Controller\LikvidacniProtokol;

use App\Repository\LikvidacniProtokolRepository;
use App\Service\LikvidacniProtokol\LikvidacniProtokolReader;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\String\Slugger\SluggerInterface;

#[IsGranted('ROLE_COM_ADMIN')]
class LikvidacniProtokolDownloadController extends AbstractController
{

    #[Route('/protokol/{id}/download', name: 'app_likvidacni_protokol_download', methods: ['GET'])]
    public function __invoke(
        LikvidacniProtokolRepository $likvidacniProtokolRepository,
        LikvidacniProtokolReader $likvidacniProtokolReader,
        SluggerInterface $slugger,
        int $id
    ):Response
    {

        $likvidacniProtokol =  $likvidacniProtokolRepository->find($id);
        
        $response = new StreamedResponse(function () use ($likvidacniProtokol, $likvidacniProtokolReader) {
            $outputStream = fopen('php://output', 'wb');
            $fileStream = $likvidacniProtokolReader->readSouborAsStream($likvidacniProtokol->getFilename());
            stream_copy_to_stream($fileStream, $outputStream);
        });
        $response->headers->set('Content-Type', $likvidacniProtokol->getMimeType());
        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_ATTACHMENT,
            $likvidacniProtokol->getOriginalFilename(),
            $slugger->slug($likvidacniProtokol->getOriginalFilename(),'_'),

        );
        $response->headers->set('Content-Disposition', $disposition);

        return $response;
    }
}
