<?php

namespace App\Controller\LikvidacniProtokol;

use App\Helper\FlashMessageHelper;
use App\Repository\LikvidacniProtokolRepository;
use App\Service\LikvidacniProtokol\LikvidacniProtokolRemover;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class LikvidacniProtokolDeleteController extends AbstractController
{

    #[Route('/protokol/{id}/delete', name: 'app_likvidacni_protokol_delete', methods: ['GET'])]
    public function __invoke(
        LikvidacniProtokolRepository $likvidacniProtokolRepository,
        EntityManagerInterface $entityManager,
        LikvidacniProtokolRemover $likvidacniProtokolRemover,
        int $id
    ):Response
    {

        

        $likvidacniProtokol = $likvidacniProtokolRepository->find($id);
        if (!$likvidacniProtokol) {
            throw $this->createNotFoundException('Soubor nebyl nalezen');
        }

        $pojistnaUdalost =  $likvidacniProtokol->getPojistnaUdalost();

        try {
            $likvidacniProtokolRemover->deleteFile($likvidacniProtokol);
            $entityManager->remove($likvidacniProtokol);
            $entityManager->flush();

        } catch (\Throwable $th) {
            $entityManager->rollback();
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Při mazání souboru ' . $likvidacniProtokol->getOriginalFilename() . ' došlo k chybě');
        }

        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Soubor ' . $likvidacniProtokol->getOriginalFilename() . ' úspěšně vymazán');

        return $this->redirectToRoute('app_pojistna_udalost_ukonceni', ['id' => $pojistnaUdalost->getId()]);
    }

    
}
