<?php

namespace App\Controller\VyzadanyDokument;

use App\Dto\VyzadanyDokument\VyzadanyDokumentNewInput;
use App\Entity\PojistnaUdalost;
use App\Entity\User;
use App\Form\VyzadanyDokument\VyzadanyDokumentNewType;
use App\Helper\FlashMessageHelper;
use App\Service\VyzadanyDokument\VyzadanyDokumentService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/new-vyzadany-dokument', name: 'app_vyzadany_dokument_new')]
class VyzadanyDokumentNewController extends AbstractController
{
    public function __invoke(
        Request $request,
        VyzadanyDokumentService $vyzadanyDokumentService,
        PojistnaUdalost $pojistnaUdalost
    ): RedirectResponse|Response {
        if ($vyzadanyDokumentService->isPojistnaUdalostUzavreno($pojistnaUdalost)) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Přidání vyžádaného dokumentu k uzavřené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        $vyzadanyDokumentNewInput = new VyzadanyDokumentNewInput();
        $vyzadanyDokumentNewInput->pojistnaUdalost = $pojistnaUdalost;
        $form = $this->createForm(VyzadanyDokumentNewType::class, $vyzadanyDokumentNewInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            /** @var User $author */
            $author = $this->getUser();
            $vyzadanyDokumentNewInput->autor = $author;

            if (!$vyzadanyDokumentService->isValidInput($vyzadanyDokumentNewInput)) {
                $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Musíte vybrat požadavky nebo zadat vlastní požadavek.');
                return $this->render('vyzadany_dokument/new.html.twig', [
                    'form' => $form->createView(),
                    'cislo_pu' => $pojistnaUdalost->getCisloPojistnaUdalost(),
                    'pou' => $pojistnaUdalost,
                    'submenu_page' => 'vyzadane_dokumenty'
                ]);
            }

            if ($vyzadanyDokumentNewInput->isCustom && $vyzadanyDokumentNewInput->coVyzadano) {
                $vyzadanyDokumentService->createCustomVyzadanyDokument($vyzadanyDokumentNewInput, $author);
            } 
            else if (!$vyzadanyDokumentNewInput->isCustom && !empty($vyzadanyDokumentNewInput->pozadavky)) {
                $vyzadanyDokumentService->createPredefinedVyzadanyDokumenty($vyzadanyDokumentNewInput, $author);
            }

            $vyzadanyDokumentService->saveChanges();

            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Nový vyžádaný dokument úspěšně přidán');
            return $this->redirectToRoute('app_vyzadany_dokument_list', ['id' => $pojistnaUdalost->getId()]);
        }

        return $this->render('vyzadany_dokument/new.html.twig', [
            'form' => $form->createView(),
            'cislo_pu' => $pojistnaUdalost->getCisloPojistnaUdalost(),
            'pou' => $pojistnaUdalost,
            'submenu_page' => 'vyzadane_dokumenty'
        ]);
    }
}
