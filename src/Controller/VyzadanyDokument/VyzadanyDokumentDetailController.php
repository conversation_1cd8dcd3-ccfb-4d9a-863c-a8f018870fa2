<?php

namespace App\Controller\VyzadanyDokument;

use App\Entity\VyzadanyDokument;
use App\Service\VyzadanyDokument\VyzadanyDokumentService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/vyzadany-dokument/{id}', name: 'app_vyzadany_dokument_detail')]
class VyzadanyDokumentDetailController extends AbstractController
{
    public function __invoke(
        VyzadanyDokument $vyzadanyDokument,
        VyzadanyDokumentService $vyzadanyDokumentService
    ): Response {
        $pojistnaUdalost = $vyzadanyDokument->getPojistnaUdalost();

        return $this->render('vyzadany_dokument/detail.html.twig', [
            'vyzadany_dokument' => $vyzadanyDokument,
            'pou' => $pojistnaUdalost,
            'submenu_page' => 'vyzadane_dokumenty'
        ]);
    }
}
