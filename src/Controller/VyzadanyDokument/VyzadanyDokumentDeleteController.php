<?php

namespace App\Controller\VyzadanyDokument;

use App\Entity\VyzadanyDokument;
use App\Helper\FlashMessageHelper;
use App\Service\VyzadanyDokument\VyzadanyDokumentService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/vyzadany-dokument/{id}/delete', name: 'app_vyzadany_dokument_delete')]
class VyzadanyDokumentDeleteController extends AbstractController
{
    public function __invoke(
        Request $request,
        VyzadanyDokumentService $vyzadanyDokumentService,
        VyzadanyDokument $vyzadanyDokument
    ): RedirectResponse {
        $pojistnaUdalost = $vyzadanyDokument->getPojistnaUdalost();

        if ($vyzadanyDokumentService->isPojistnaUdalostUzavreno($pojistnaUdalost)) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Smazání vyžádaného dokumentu k uzavřené události není povoleno.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        // Check CSRF token
        $token = $request->request->get('_token');
        if (!$this->isCsrfTokenValid('delete' . $vyzadanyDokument->getId(), $token)) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Neplatný bezpečnostní token.');
            return $this->redirectToRoute('app_vyzadany_dokument_list', ['id' => $pojistnaUdalost->getId()]);
        }

        // Delete the document
        $vyzadanyDokumentService->deleteVyzadanyDokument($vyzadanyDokument);
        
        $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Vyžádaný dokument byl úspěšně smazán.');
        return $this->redirectToRoute('app_vyzadany_dokument_list', ['id' => $pojistnaUdalost->getId()]);
    }
}