<?php

namespace App\Controller\VyzadanyDokument;

use App\Entity\PojistnaUdalost;
use App\Service\VyzadanyDokument\VyzadanyDokumentService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/pou/{id}/vyzadane-dokumenty', name: 'app_vyzadany_dokument_list')]
class VyzadanyDokumentListController extends AbstractController
{
    public function __invoke(
        PojistnaUdalost $pojistnaUdalost,
        VyzadanyDokumentService $vyzadanyDokumentService,
        string $sortOrder = 'DESC'
    ): Response {
        $vyzadaneDokumenty = $vyzadanyDokumentService->getVyzadanyDokumentyForPojistnaUdalost(
            $pojistnaUdalost->getId(),
            $sortOrder
        );

        return $this->render('vyzadany_dokument/list.html.twig', [
            'pou' => $pojistnaUdalost,
            'vyzadane_dokumenty' => $vyzadaneDokumenty,
            'submenu_page' => 'vyzadane_dokumenty'
        ]);
    }
}
