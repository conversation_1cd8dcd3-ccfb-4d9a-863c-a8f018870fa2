<?php

namespace App\Controller\VyzadanyDokument;

use App\Dto\VyzadanyDokument\VyzadanyDokumentEditInput;
use App\Entity\VyzadanyDokument;
use App\Form\VyzadanyDokument\VyzadanyDokumentEditType;
use App\Helper\FlashMessageHelper;
use App\Service\VyzadanyDokument\VyzadanyDokumentService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/vyzadany-dokument/{id}/edit', name: 'app_vyzadany_dokument_edit')]
class VyzadanyDokumentEditController extends AbstractController
{
    public function __invoke(
        Request $request,
        VyzadanyDokumentService $vyzadanyDokumentService,
        VyzadanyDokument $vyzadanyDokument
    ): RedirectResponse|Response {
        $pojistnaUdalost = $vyzadanyDokument->getPojistnaUdalost();

        if ($vyzadanyDokumentService->isPojistnaUdalostUzavreno($pojistnaUdalost)) {
            $this->addFlash(FlashMessageHelper::TYPE_WARNING, 'Úprava vyžádaného dokumentu k uzavřené události není povolena.');
            return $this->redirectToRoute('app_pojistna_udalost_read', ['id' => $pojistnaUdalost->getId()]);
        }

        $vyzadanyDokumentEditInput = VyzadanyDokumentEditInput::createFromVyzadanyDokument($vyzadanyDokument);
        $form = $this->createForm(VyzadanyDokumentEditType::class, $vyzadanyDokumentEditInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $vyzadanyDokumentService->updateVyzadanyDokument($vyzadanyDokument, $vyzadanyDokumentEditInput);
            $vyzadanyDokumentService->saveChanges();

            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Vyžádaný dokument úspěšně upraven');
            return $this->redirectToRoute('app_vyzadany_dokument_list', ['id' => $pojistnaUdalost->getId()]);
        }

        return $this->render('vyzadany_dokument/edit.html.twig', [
            'form' => $form->createView(),
            'vyzadany_dokument' => $vyzadanyDokument,
            'pou' => $pojistnaUdalost,
            'submenu_page' => 'vyzadane_dokumenty'
        ]);
    }
}
