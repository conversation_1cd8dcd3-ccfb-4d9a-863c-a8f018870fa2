<?php

namespace App\Controller\Ciselniky\PojistnePodminky\PojistnePodminkySoubor;

use App\Helper\FlashMessageHelper;
use App\Repository\PojistnePodminkySouborRepository;
use App\Service\Ciselniky\PojistnePodminkySouborRemover;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/ciselniky/ppsoubor/{id}/delete', name: 'app_ciselniky_pojistne_podminky_soubor_delete', methods: ['GET'])]
class PojistnePodminkySouborDeleteController extends AbstractController
{

    public function __invoke(
        PojistnePodminkySouborRepository $pojistnePodminkySouborRepository,
        PojistnePodminkySouborRemover $pojistnePodminkySouborRemover,
        EntityManagerInterface $entityManager,
        int $id
    ): Response {

        $pojistnePodminkySoubor = $pojistnePodminkySouborRepository->find($id);
        if (!$pojistnePodminkySoubor) {
            throw $this->createNotFoundException('Soubor nebyl nalezen');
        }

        $pojistnePodminky =  $pojistnePodminkySoubor->getPojistnePodminky();
        $pojistnePodminkyId =  $pojistnePodminkySoubor->getPojistnePodminky()->getId();

        try {

            $pojistnePodminkySouborRemover->deleteFile($pojistnePodminkySoubor);
            $pojistnePodminky->removePojistnePodminkySoubor();
            $entityManager->remove($pojistnePodminkySoubor);
            $entityManager->flush();

            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Soubor ' . $pojistnePodminkySoubor->getOriginalFilename() . ' byl smazaný');
        } catch (\Exception $e) {
            $entityManager->rollback();
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Při mazání souboru ' . $pojistnePodminkySoubor->getOriginalFilename() . ' došlo k chybě');
        }




        return $this->redirectToRoute('app_ciselniky_pojistne_podminky_edit', [
            'id' => $pojistnePodminkyId
        ]);
    }
}
