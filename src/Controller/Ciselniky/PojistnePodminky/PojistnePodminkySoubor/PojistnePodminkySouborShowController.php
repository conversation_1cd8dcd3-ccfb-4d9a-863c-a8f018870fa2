<?php

namespace App\Controller\Ciselniky\PojistnePodminky\PojistnePodminkySoubor;

use App\Entity\PojistnePodminkySoubor;
use App\Service\Ciselniky\PojistnePodminkySouborReader;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\String\Slugger\SluggerInterface;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/ciselniky/ppsoubor/{id}', name: 'app_ciselniky_pojistne_podminky_soubor_view', methods: ['GET'])]
class PojistnePodminkySouborShowController extends AbstractController
{


    public function __invoke(
        PojistnePodminkySouborReader $pojistnePodminkySouborReader,
        PojistnePodminkySoubor $pojistnePodminkySoubor,
        SluggerInterface $slugger
    ): Response {

        $response = new StreamedResponse(function () use ($pojistnePodminkySoubor, $pojistnePodminkySouborReader) {
            $outputStream = fopen('php://output', 'wb');
            $fileStream = $pojistnePodminkySouborReader->readSouborAsStream($pojistnePodminkySoubor->getFilename());
            stream_copy_to_stream($fileStream, $outputStream);
        });
        $response->headers->set('Content-Type', $pojistnePodminkySoubor->getMimeType());
        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_INLINE,
            $pojistnePodminkySoubor->getOriginalFilename(),
            $slugger->slug($pojistnePodminkySoubor->getOriginalFilename(),'_'),

        );
        $response->headers->set('Content-Disposition', $disposition);

        return $response;
    }
}
