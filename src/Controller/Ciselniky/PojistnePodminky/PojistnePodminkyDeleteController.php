<?php

namespace App\Controller\Ciselniky\PojistnePodminky;

use App\Entity\PojistnePodminky;
use App\Service\Ciselniky\PojistnePodminkySouborRemover;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('ciselniky/pp/delete/{id}', name: 'app_ciselniky_pojistne_podminky_delete')]
class PojistnePodminkyDeleteController extends AbstractController
{
    public function __invoke(EntityManagerInterface $entityManager, 
    PojistnePodminky $pojistnePodminky,
    PojistnePodminkySouborRemover $pojistnePodminkySouborRemover
    ): Response
    {
        if ($pojistnePodminky->hasAttachedPojistnePodminkySoubor())
        {
            $pojistnePodminkySoubor = $pojistnePodminky->getPojistnePodminkySoubor();
            $pojistnePodminkySouborRemover->deleteFile($pojistnePodminkySoubor);
            $entityManager->remove($pojistnePodminkySoubor);
        }
        $entityManager->remove($pojistnePodminky);
        $entityManager->flush();

        return $this->redirectToRoute('app_ciselniky_pojistne_podminky_list');
    }
}
