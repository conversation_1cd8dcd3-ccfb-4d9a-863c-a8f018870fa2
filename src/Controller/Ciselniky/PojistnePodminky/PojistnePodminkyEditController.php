<?php

namespace App\Controller\Ciselniky\PojistnePodminky;

use App\Dto\Ciselniky\PojistnePodminkyEditInput;
use App\Entity\PojistnePodminky;
use App\Form\Ciselniky\PojistnePodminkyEditType;
use App\Helper\FlashMessageHelper;
use App\Service\Ciselniky\PojistnePodminkySouborUploader;
use App\Service\Ciselniky\PojistnePodminkyUpdateService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('ciselniky/pp/edit/{id}', name: 'app_ciselniky_pojistne_podminky_edit')]
class PojistnePodminkyEditController extends AbstractController
{
    public function __invoke(
        Request $request,
        PojistnePodminkyUpdateService $pojistnePodminkyService,
        PojistnePodminky $pojistnePodminky,
        PojistnePodminkySouborUploader $pojistnePodminkySouborUploader
    ): Response {
        $pojistnePodminkyEditInput = PojistnePodminkyEditInput::createFromPojistnePodminky($pojistnePodminky);
        $form = $this->createForm(PojistnePodminkyEditType::class, $pojistnePodminkyEditInput);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {

            /**
             * @var UploadedFile $soubor
             */
            $soubor = $form->get('soubor')->getData();

            if ($soubor instanceof UploadedFile) {

                $pojistnePodminky =  $pojistnePodminkySouborUploader->uploadPojistnePodminkySoubor(
                    $soubor,
                    $pojistnePodminky
                );
            }

            $pojistnePodminkyService->updatePojistnePodminky($pojistnePodminky, $pojistnePodminkyEditInput);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Pojistná podmínka úspěšně upravena');
            return $this->redirectToRoute('app_ciselniky_pojistne_podminky_list');
        }


        return $this->render('ciselniky/pojistne_podminky/edit.html.twig', [
            'form' => $form->createView(),
            'pojistne_podminky' => $pojistnePodminky
        ]);
    }
}
