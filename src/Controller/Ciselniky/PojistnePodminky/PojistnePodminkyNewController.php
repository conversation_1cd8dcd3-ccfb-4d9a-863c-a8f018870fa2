<?php

namespace App\Controller\Ciselniky\PojistnePodminky;

use App\Dto\Ciselniky\PojistnePodminkyNewInput;
use App\Entity\PojistnePodminky;
use App\Form\Ciselniky\PojistnePodminkyNewType;
use App\Service\Ciselniky\PojistnePodminkySouborUploader;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/ciselniky/pp/new', name: 'app_ciselniky_pojistne_podminky_new')]
class PojistnePodminkyNewController extends AbstractController
{
    public function __invoke(
        Request $request,
        EntityManagerInterface $entityManager,
        PojistnePodminkySouborUploader $pojistnePodminkySouborUploader
    ): Response {
        $pojistnePodminkyNewInput = new PojistnePodminkyNewInput();
        $form = $this->createForm(PojistnePodminkyNewType::class, $pojistnePodminkyNewInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $pojistnePodminky = new PojistnePodminky(
                $pojistnePodminkyNewInput->typVpp,
                $pojistnePodminkyNewInput->urlVpp
            );

            /**
             * @var UploadedFile $soubor
             */
            $soubor = $form->get('soubor')->getData();

            if ($soubor instanceof UploadedFile) {

                $pojistnePodminky =  $pojistnePodminkySouborUploader->uploadPojistnePodminkySoubor(
                    $soubor,
                    $pojistnePodminky
                );
            }

            $entityManager->persist($pojistnePodminky);
            $entityManager->flush();

            return $this->redirectToRoute('app_ciselniky_pojistne_podminky_list');
        }

        return $this->render('ciselniky/pojistne_podminky/new.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}
