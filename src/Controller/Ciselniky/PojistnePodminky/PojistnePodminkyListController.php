<?php

namespace App\Controller\Ciselniky\PojistnePodminky;

use App\Repository\PojistnePodminkyRepository;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
#[Route('/ciselniky/pp', name: 'app_ciselniky_pojistne_podminky_list')]
class PojistnePodminkyListController extends AbstractController
{
    public function __invoke(
        Request $request,
        PojistnePodminkyRepository $pojistnePodminkyRepository,
        PaginatorInterface $paginator
    ): Response {
        $query = $pojistnePodminkyRepository->getAllQuery();

        $pagination = $paginator->paginate(
            $query, /* query NOT result */
            $request->query->getInt('page', 1), /*page number*/
            50
        );

        return $this->render('ciselniky/pojistne_podminky/list.html.twig', [
            'pagination' => $pagination,
        ]);
    }
}
