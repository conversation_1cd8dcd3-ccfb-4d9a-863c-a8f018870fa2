<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class UserToggleActiveController extends AbstractController
{
    #[Route('/user/toggle-active/{user}', name: 'app_user_toggle_active')]
    public function __invoke(Request $request, User $user, EntityManagerInterface $entityManager): RedirectResponse
    {
        $currentUser = $this->getUser();
        if ($currentUser instanceof User && $currentUser->isAdministrator() && $user->getId() != $currentUser->getId()) {
            try {
                $user->setActive(!$user->getActive());
                $entityManager->persist($user);
                $entityManager->flush();
                
                if ($user->getActive()) {
                    $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Uživatel '.$user->getName().' '.$user->getSurname().' byl úspěšně aktivován.');
                } else {
                    $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Uživatel '.$user->getName().' '.$user->getSurname().' byl úspěšně deaktivován.');
                }
                
            } catch (\Exception $e) {
                $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Došlo k chybě při aktivaci/deaktivaci uživatele '.$user->getName().' '.$user->getSurname().'.');
            }    

        } else {
            $this->addFlash(FlashMessageHelper::TYPE_ERROR, 'Nemáte oprávnění ke aktivaci/deaktivaci uživatele '.$user->getName().' '.$user->getSurname().'.');
        }
        
        return $this->redirectToRoute('app_user_list');
    }
}
