<?php

namespace App\Controller\User;

use App\Entity\User;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class UserDeleteController extends AbstractController
{
    #[Route('/user/{id}/delete', name: 'app_user_delete', methods: ['POST'])]
    public function __invoke(User $user, EntityManagerInterface $entityManager, Request $request): Response
    {
        $currentUser = $this->getUser();        
        
        if ($currentUser instanceof User && $currentUser->isAdministrator() && $user->getId() !== $currentUser->getId()) {
            if ($this->isCsrfTokenValid('delete' . $user->getId(), $request->request->get('_token'))) {
                try {
                    $entityManager->remove($user);
                    $entityManager->flush();
                    $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Uživatel ' . $user->getName() . ' ' . $user->getSurname() . ' byl úspěšně smazán.');
                } catch (\Exception $e) {
                    $this->addFlash(FlashMessageHelper::TYPE_DANGER, 'Došlo k chybě při mazání uživatele ' . $user->getName() . ' ' . $user->getSurname() . '.');
                }  
            } else {
                $this->addFlash(FlashMessageHelper::TYPE_DANGER, 'Neplatný CSRF token. Uživatel ' . $user->getName() . ' ' . $user->getSurname() . ' nebyl smazán.');
            }
        } else {
            $this->addFlash(FlashMessageHelper::TYPE_DANGER, 'Nemáte oprávnění ke smazání uživatele ' . $user->getName() . ' ' . $user->getSurname() . '.');
        }
        
        return $this->redirectToRoute('app_user_list');
    }
}
