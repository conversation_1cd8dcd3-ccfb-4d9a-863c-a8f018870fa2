<?php

namespace App\Controller\User;

use App\Repository\UserRepository;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class UserSearchController extends AbstractController
{
    #[Route('/user/search', name: 'app_user_search')]
    public function __invoke(UserRepository $userRepository, Request $request, PaginatorInterface $paginator): Response
    {
        $query = $userRepository->searchByString(
            $request->query->get('q'),
        );

        $pagination = $paginator->paginate(
            $query, /* query NOT result */
            $request->query->getInt('page', 1), /*page number*/
            10
        );

        return $this->render('user/user_list/index.html.twig', [
            'pagination' => $pagination,
            'currentUser' => $this->getUser(),
            'searchString' => $request->query->get('q'),
        ]);
    }
}