<?php
namespace App\Controller\User;

use App\Entity\PojistnaUdalost;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Entity\User;
use App\Enum\PuListColumn;
use Doctrine\ORM\EntityManagerInterface;

class SetPuColsController extends AbstractController
{
    #[Route('/user/set-pu-cols/{columns?}', name: 'app_user_set_pu_cols')]
    public function __invoke(
        ?string $columns,
        EntityManagerInterface $em
    ) : Response {
        $toSet = $columns ? explode(',', $columns) : [];
        /** @var User */
        $user = $this->getUser();
        $user->setPuCols($toSet);
        $em->persist($user);
        $em->flush();
        return $this->redirectToRoute('app_pojistna_udalost_list');
    }

    #[Route('/user/reset-pu-cols', name: 'app_user_reset_pu_cols')]
    public function reset(EntityManagerInterface $em) : Response
    {
        /** @var User */
        $user = $this->getUser();
        $user->setPuCols(PuListColumn::values());
        $em->persist($user);
        $em->flush();
        return $this->redirectToRoute('app_pojistna_udalost_list');
    }
}