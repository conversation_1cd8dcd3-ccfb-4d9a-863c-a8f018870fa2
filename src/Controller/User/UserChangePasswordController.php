<?php

namespace App\Controller\User;

use App\Dto\User\UserChangePasswordInput;
use App\Entity\User;
use App\Form\UserChangePasswordType;
use App\Helper\FlashMessageHelper;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasher;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;

class UserChangePasswordController extends AbstractController
{
    #[Route('/user/change-password', name: 'app_user_change_password')]
    public function __invoke(Request $request, EntityManagerInterface $entityManager, UserRepository $userRepository, UserPasswordHasherInterface $userPasswordHasher): Response
    {
        $userChangePasswordInput = new UserChangePasswordInput();
        
        $form = $this->createForm(UserChangePasswordType::class, $userChangePasswordInput);
        
        $form->handleRequest($request);

        $user = $this->getUser();
        if ($form->isSubmitted() && $form->isValid() && $user instanceof User) {
            $userChangePasswordInput = $form->getData();

            if (!$userPasswordHasher->isPasswordValid($user, $userChangePasswordInput->oldPassword)) {
                $this->addFlash(FlashMessageHelper::TYPE_DANGER, 'Zadané staré heslo není správné.');
                return $this->redirectToRoute('app_user_change_password');
            }
            $hashedPassword = $userPasswordHasher->hashPassword($user, $userChangePasswordInput->newPassword);
            $user->setPassword($hashedPassword);

            $entityManager->persist($user);
            $entityManager->flush();
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Heslo bylo změněno.');

            return $this->redirectToRoute('app_dashboard');
        }
        
        return $this->render('user/change_password/index.html.twig', [
            'form' => $form,
        ]);
    }
}
