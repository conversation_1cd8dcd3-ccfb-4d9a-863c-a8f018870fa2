<?php
namespace App\Controller\User;
use App\Dto\User\ProfileEditInput;
use App\Form\User\ProfileEditType;
use App\Helper\FlashMessageHelper;
use App\Service\User\ProfileEditService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
class ProfileEditController extends AbstractController
{
    #[Route('/user/profile_edit', name: 'user_profile_edit')]
    public function __invoke(Request $request, ProfileEditService $profileEditService): Response
    {
        /** @var \App\Entity\User $user */
        $user = $this->getUser();
        $profileEditInput = ProfileEditInput::createFromUser($user);
        $form = $this->createForm(ProfileEditType::class, $profileEditInput); // Opraveno zde
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $profileEditInput = $form->getData();
            //$profileEditService->updateProfile($user, $profileEditInput); // Opraveno zde
            $profileEditService->updateUser($user, $profileEditInput);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Uživatel úspěšně upraven');
            return $this->redirectToRoute('user_profile');
        }
        return $this->render('user/profile_edit/index.html.twig', [
            'form' => $form->createView(),
            'zadavatel' => $user,
        ]);
    }
}