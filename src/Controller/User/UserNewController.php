<?php

namespace App\Controller\User;

use App\Dto\User\UserNewInput;
use App\Factory\User\UserFactory;
use App\Form\User\UserNewType;
use App\Helper\FlashMessageHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class UserNewController extends AbstractController
{

    #[Route('/user/new', name: 'app_user_new')]
    #[IsGranted('ROLE_COM_ADMIN')]
    public function __invoke(Request $request, EntityManagerInterface $entityManager, UserFactory $userFactory): Response
    {
        $userNewInput = new UserNewInput();

        $form = $this->createForm(UserNewType::class, $userNewInput);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $userNewInput = $form->getData();
            $newuser = $userFactory->createUser(
                $userNewInput->name,
                $userNewInput->surname,
                $userNewInput->password,
                $userNewInput->email,
                $userNewInput->role,
                $userNewInput->telephone                
            );
            $entityManager->persist($newuser);
            $entityManager->flush();
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Nový uživatel úspěšně uložen');

            return $this->redirectToRoute('app_user_list');
        }

        return $this->render('user/user_new/index.html.twig', [
            'form' => $form,
        ]);
    }
}
