<?php

namespace App\Controller\User;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class ProfileViewController extends AbstractController
{
    #[Route('/user/profile_view', name: 'user_profile')]
    public function __invoke(): Response
    {

        return $this->render('user/profile_view/index.html.twig', [
            'currentUser' => $this->getUser(),
        ]);

    }
}