<?php

namespace App\Controller\User;

use App\Dto\User\UserEditInput;
use App\Entity\User;
use App\Form\User\UserEditType;
use App\Helper\FlashMessageHelper;
use App\Service\User\UserEditService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted('ROLE_COM_ADMIN')]
class UserEditController extends AbstractController
{
    #[Route('/user/{id}/edit', name: 'app_user_edit')]
    public function __invoke(Request $request, UserEditService $userEditService, User $user): Response
    {

        $userEditInput = UserEditInput::createFromUser($user);
        $form = $this->createForm(UserEditType::class, $userEditInput);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $userEditInput =  $form->getData();
            $updatedUser = $userEditService->updateUser($user,$userEditInput);
            $this->addFlash(FlashMessageHelper::TYPE_SUCCESS, 'Uživatel úspěšně upraven');
            return $this->redirectToRoute('app_user_list');
        }

        return $this->render('user/user_edit/index.html.twig', [
            'form' => $form->createView(),
            'zadavatel' => $user
        ]);
    }
}
