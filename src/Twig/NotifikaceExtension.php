<?php

namespace App\Twig;

use App\Entity\User;
use App\Repository\NotifikaceRepository;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class NotifikaceExtension extends AbstractExtension
{
    private NotifikaceRepository $notifikaceRepository;

    public function __construct(NotifikaceRepository $notifikaceRepository)
    {
        $this->notifikaceRepository = $notifikaceRepository;
    }

    public function getFunctions()
    {
        return [
            new TwigFunction('unread_count', [$this, 'getUnreadNotificationCount']),
        ];
    }

    public function getUnreadNotificationCount(User $user): int
    {
        return $this->notifikaceRepository->getUnreadCount($user);
    }
}
