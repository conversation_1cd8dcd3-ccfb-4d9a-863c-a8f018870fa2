<?php

namespace App\Twig\Runtime;

use App\Service\Quill\QuillDeltaParser;
use Twig\Extension\RuntimeExtensionInterface;

class QuillDeltaParseExtensionRuntime implements RuntimeExtensionInterface
{
    public function __construct(
        private QuillDeltaParser $quillDeltaParser
    )
    {
    }

    public function getTextFromDelta(string $jsonStringifyContent, int $length = 0):string
    {
        $text = $this->quillDeltaParser->extractText($jsonStringifyContent);
        $result =  $text;

        if ($length > 0)
        {
            $result = mb_substr($text, 0, $length);
        }

        return $result;
    }
}
