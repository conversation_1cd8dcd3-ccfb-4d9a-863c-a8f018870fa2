init:
	make init-be
	make init-fe
init-be:
	composer install
init-fe:
	npm install
	npm run dev
	bin/console assets:install
pre-commit:
	# prepared when these tools will be installed
	composer phpstan
#	composer php-cs-fixer
	composer validate-schema
database-reset:
	bin/console doctrine:database:drop --force --if-exists
	bin/console doctrine:database:create
	make migrations-migrate
	make load-fixtures
migrations-diff:
	bin/console doctrine:cache:clear-metadata
	bin/console doctrine:migrations:diff --no-interaction
migrations-migrate:
	bin/console doctrine:migrations:migrate --no-interaction
load-fixtures:
	bin/console doctrine:fixtures:load --no-interaction
#test:
#	php bin/phpunit --testdox
deploy-prod:
	vendor/bin/dep deploy prod
deploy-demo:
	vendor/bin/dep deploy demo	
