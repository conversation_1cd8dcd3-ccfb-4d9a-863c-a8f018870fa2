# File Browser Component - Kompletní dokumentace

File browser komponenta pro správu souborů a složek s modulární architekturou.

## Použití komponenty

### Template include
```twig
{{ include('components/file_browser/file_browser.html.twig', {
    slozka: slozka_entity,
    soubory: soubory_array,
    podslozky: podslozky_array,
    breadcrumb: breadcrumb_array,
    view_mode: 'list',
    permissions: {
        upload: true,
        delete: true,
        rename: true,
        create_folder: false
    }
}) }}
```

### Povinné parametry
- **slozka**: Entita Slozka - složka, jej<PERSON><PERSON> obsah se zobrazuje
- **soubory**: Array - pole entit Soubor pro zobrazení
- **podslozky**: Array - pole entit Slozka pro zobrazení podsložek
- **breadcrumb**: Array - breadcrumb data pro navigaci

### Volitelné parametry
- **view_mode**: String - 'list' (default) nebo 'grid'
- **permissions**: Array - oprávnění uživatele:
  - `upload`: boolean - povolení nahrávání souborů
  - `delete`: boolean - povolení mazání souborů
  - `rename`: boolean - povolení přejmenování souborů
  - `create_folder`: boolean - povolení vytváření složek

### Příprava dat v controlleru

```php
use App\Service\FileBrowser\FileBrowserService;

public function __invoke(
    FileBrowserService $fileBrowserService,
    // ... další parametry
): Response {
    // Získání složky
    $slozka = $pojistnaUdalost->getSlozkaPojistovnaZadani();
    
    // Načtení obsahu složky
    $folderContent = $fileBrowserService->getFolderContent($slozka);
    $breadcrumb = $fileBrowserService->getBreadcrumb($slozka);
    
    return $this->render('template.html.twig', [
        'slozka' => $slozka,
        'soubory' => $folderContent['soubory'],
        'podslozky' => $folderContent['podslozky'],
        'breadcrumb' => $breadcrumb,
        // ... další data
    ]);
}
```

## Architektura

### Struktura souborů

```
assets/controllers/file_browser/
├── notification_helper.js         # Helper pro notifikace (success/error)
├── file_icon_helper.js           # Helper pro SVG ikony souborů
├── view_renderer.js               # Generování HTML pro list/grid view
├── context_menu_controller.js     # Správa kontextového menu
├── upload_controller.js           # Dropzone a upload funkcionalita
├── file_operations.js             # Operace se soubory (download, delete, rename)
├── error_handler.js               # Centrální error handling a validace
├── navigation_state_manager.js    # Robustní navigace s transakcemi
└── drag_drop_manager.js            # Drag & drop mezi file browsery
```

### Popis modulů

#### 1. `notification_helper.js`
- **Účel**: Zobrazování toast notifikací
- **Metody**: 
  - `showError(message)` - červená chybová zpráva
  - `showSuccess(message)` - zelená úspěšná zpráva
- **Použití**: Statické metody, importují se dynamicky

#### 2. `file_icon_helper.js`
- **Účel**: Generování SVG ikon podle MIME typu souboru
- **Metody**:
  - `getFileIconSVG(soubor)` - velká ikona pro grid view
  - `getFileIconSVGSmall(soubor)` - malá ikona pro list view
- **Podporované typy**: PDF, Word dokumenty, obrázky, obecné soubory

#### 3. `view_renderer.js`
- **Účel**: Generování HTML obsahu pro různé view módy
- **Metody**:
  - `generateContentHTML(data, isGridView)` - hlavní metoda
  - `generateListHTML(soubory, podslozky)` - tabulkový view s řazením
  - `generateGridHTML(soubory, podslozky)` - grid view
  - `generateEmptyStateHTML()` - prázdný stav
- **Závislosti**: Používá `FileIconHelper`
- **Funkce**: Obsahuje již implementované `contextmenu` event listenery pro kontextové menu

#### 4. `context_menu_controller.js`
- **Účel**: Správa kontextového menu (pravé tlačítko myši)
- **Funkce**:
  - Download souborů z context menu
  - Přejmenování souborů s modaly a validací
  - Mazání souborů s potvrzovacím dialogem
  - Podpora českých znaků v názvech souborů
- **Metody**:
  - `showContextMenu(event)` - zobrazí menu na pozici myši
  - `hideContextMenu()` - skryje menu
  - `downloadFileFromContext(event)` - download z menu
  - `renameFileFromContext(event)` - přejmenování s modalem
  - `deleteFileFromContext(event)` - smazání s potvrzením

#### 5. `upload_controller.js`
- **Účel**: Správa Dropzone a upload funkcionalit
- **Metody**:
  - `initializeDropzone()` - inicializace Dropzone
  - `showUpload(event)` - zobrazí upload zónu
  - `hideUpload(event)` - skryje upload zónu
  - `getDropzone()` - getter pro Dropzone instanci
- **Závislosti**: Dropzone library

#### 6. `file_operations.js`
- **Účel**: Základní operace se soubory a složkami
- **Metody**:
  - `downloadFile(event)` - download souboru
  - `navigateToFolder(event)` - navigace do složky (placeholder)
  - `goBack(event)` - zpět (placeholder)
  - `createFolder(event)` - vytvoření složky (placeholder)
  - `setButtonLoading(button, isLoading)` - loading stav tlačítek

#### 7. `error_handler.js`
- **Účel**: Centrální error handling a validace napříč celou komponentou
- **Kategorie chyb**:
  - **Kritické** - API operace, navigace (user feedback povinný)
  - **Nekritické** - localStorage, DOM targets (console log)
  - **Validační** - preventivní kontroly (console warning)
- **Metody**:
  - `handleCriticalOperation(operation, context)` - kritické operace s user feedback
  - `handleNonCritical(operation, context)` - nekritické s fallback
  - `validateRequired(value, fieldName)` - validace povinných hodnot
  - `validateDOMElement(element, elementName)` - validace DOM elementů
  - `fetchWithErrorHandling(url, options, context)` - API wrapper s error handling
- **Výhody**: Konzistentní UX, strukturované logy, snadná údržba

#### 8. `navigation_state_manager.js`
- **Účel**: Robustní navigace s transakcemi a race condition prevencí
- **Funkce**:
  - **Transactional navigation** - atomické operace (buď vše, nebo rollback)
  - **Cancel concurrent navigation** - jen nejnovější navigace se provede
  - **Loading states** - user feedback během navigace
  - **State validation** - konzistence URL = currentFolderId = obsah
  - **Error recovery** - automatický rollback při chybách
- **Metody**:
  - `navigateToFolder(folderId)` - hlavní navigační metoda
  - `createNavigation(folderId)` - vytvoření navigační transakce
  - `executeNavigation(navigation)` - provedení transakce
  - `rollbackNavigation(navigation)` - rollback při chybě
  - `showNavigationLoading(show)` - loading overlay a disabled controls
- **Výhody**: Žádné race conditions, atomické operace, user feedback

#### 9. `drag_drop_manager.js`
- **Účel**: Centrální správa drag & drop mezi file browser instancemi
- **Pattern**: Singleton s globální instancí `globalDragDropManager`
- **Funkce**:
  - **Registry systém** - automatická registrace všech instancí
  - **Event delegation** - funguje i po AJAX refresh
  - **Cross-browser validation** - nelze dropnout do stejného browseru
  - **API integrace** - skutečné přesouvání souborů
  - **Visual feedback** - CSS třídy pro drag/drop stavy
- **Metody**:
  - `registerFileBrowser(instanceId, controller)` - registrace instance
  - `handleDragStart/End(event, controller)` - drag detection
  - `handleDrop(event, controller)` - drop handling s API voláním
  - `performFileMove(fileId, targetFolderId, controller)` - API integrace
- **Výhody**: Funguje mezi instancemi, robustní error handling, user feedback

## Hlavní controller (`file_browser_controller.js`)

Hlavní controller funguje jako koordinátor a deleguje funkcionalitu na příslušné moduly:

### Inicializace
```javascript
connect() {
    // Inicializace sort state
    this.currentSort = this.loadSortFromStorage()
    
    // Vytvoření instancí modulů
    this.contextMenuController = new ContextMenuController(this)
    this.uploadController = new UploadController(this)
    this.fileOperations = new FileOperations(this)
    
    // Inicializace
    this.updateViewModeButtons()
    this.uploadController.initializeDropzone()
}
```

### Delegování metod
Všechny Stimulus action metody jsou delegovány na příslušné moduly:

```javascript
// File Operations
downloadFile(event) { this.fileOperations.downloadFile(event) }

// Upload Controller  
showUpload(event) { this.uploadController.showUpload(event) }

// Context Menu Controller
showContextMenu(event) { this.contextMenuController.showContextMenu(event) }

// Sort Operations
sortByCreatedAt(event) { /* řazení podle data vytvoření */ }
```

## Implementované funkce

### ✅ Hotové
- **List/Grid view** - přepínání mezi tabulkovým a grid zobrazením
- **Řazení souborů** - podle data vytvoření (ASC/DESC) s localStorage persistence
- **Upload** - Dropzone pro nahrávání souborů
- **Download** - stahování souborů
- **Context menu** - pravé tlačítko myši s možnostmi
- **Přejmenování** - s podporou českých znaků a validací
- **Mazání** - s potvrzovacím dialogem
- **Vytváření složek** - modal s validací a API integrací
- **Notifikace** - success/error zprávy
- **Ikony souborů** - podle MIME typu
- **Loading stavy** - pro async operace

### 🚧 Připravené k implementaci
- **Navigace** - procházení složek

## API Endpointy

Komponenta používá tyto API endpointy s **entity-level autorizací**:

- `GET /api/internal/v1/soubor/folder/{id}?sort=createdAt&order=desc` - načtení obsahu složky (VIEW)
- `POST /api/internal/v1/soubor/upload/{id}` - upload souborů (EDIT)
- `GET /api/internal/v1/soubor/{id}/download` - download souboru (VIEW)
- `DELETE /api/internal/v1/soubor/{id}/delete` - smazání souboru (DELETE_FILE)
- `PUT /api/internal/v1/soubor/{id}/rename` - přejmenování souboru (EDIT)
- `POST /api/internal/v1/slozka/create-subfolder/{parentId}` - vytvoření nové podsložky (EDIT)
- `DELETE /api/internal/v1/slozka/delete/{id}` - smazání složky (DELETE_FILE)
- `POST /api/internal/v1/soubor/{fileId}/move-to-folder/{targetFolderId}` - přesunutí souboru (EDIT pro source i target)

### Bezpečnostní kontroly

Všechny API endpointy implementují **PojistnaUdalostVoter** autorizaci:

```php
// Kontrola oprávnění k pojistné události
$pojistnaUdalost = $slozka->getPojistnaUdalost();
if (!$this->isGranted('DELETE_FILE', $pojistnaUdalost)) {
    return new JsonResponse([
        'statusCode' => 403,
        'status' => 'error',
        'error' => 'Nemáte oprávnění k této pojistné události'
    ], Response::HTTP_FORBIDDEN);
}
```

**Oprávnění:**
- `VIEW`: zobrazení dat pojistné události
- `EDIT`: úprava dat, nahrávání souborů, vytváření složek
- `DELETE_FILE`: mazání souborů a složek

**Přístup mají:**
- `ROLE_COM_ADMIN` - všechna oprávnění ke všem PU
- Přiřazený likvidátor - všechna oprávnění k přiřazené PU
- Další přiřazení uživatelé - všechna oprávnění k přiřazené PU

## Templates

### Hlavní komponenta
- `templates/components/file_browser/file_browser.html.twig` - hlavní template

### Sub-komponenty
- `templates/components/file_browser/_breadcrumb.html.twig` - breadcrumb navigace
- `templates/components/file_browser/_toolbar.html.twig` - toolbar s tlačítky
- `templates/components/file_browser/_upload_zone.html.twig` - upload zona
- `templates/components/file_browser/_file_list.html.twig` - list view s řazením
- `templates/components/file_browser/_file_grid.html.twig` - grid view
- `templates/components/file_browser/_context_menu.html.twig` - kontextové menu
- `templates/components/file_browser/_delete_confirmation_modal.html.twig` - modal pro smazání
- `templates/components/file_browser/_rename_modal.html.twig` - modal pro přejmenování
- `templates/components/file_browser/_create_folder_modal.html.twig` - modal pro vytvoření složky

## Výhody této struktury

1. **Čitelnost**: Každý modul má jasnou odpovědnost
2. **Udržovatelnost**: Snadné úpravy konkrétní funkcionality
3. **Testovatelnost**: Moduly lze testovat nezávisle
4. **Znovupoužitelnost**: Moduly lze použít v jiných projektech
5. **Škálovatelnost**: Snadné přidávání nových funkcí
6. **Menší soubory**: Původní 600+ řádků rozděleno do 6 modulů po ~100 řádcích

## Přidání nové funkcionality

1. **Nový modul**: Vytvořte nový soubor v `assets/controllers/file_browser/` složce
2. **Import**: Importujte modul v hlavním controlleru
3. **Inicializace**: Vytvořte instanci v `connect()` metodě
4. **Delegování**: Přidejte delegované metody pro Stimulus actions

## Příklad použití

```javascript
// V hlavním controlleru
import { NewModule } from './file_browser/new_module.js'

connect() {
    this.newModule = new NewModule(this)
}

// Delegovaná metoda
newAction(event) { this.newModule.handleAction(event) }
```

## Řešení problémů

### Chyba "Variable does not exist"
Ujistěte se, že předáváte všechny povinné parametry:
- `slozka`, `soubory`, `podslozky`, `breadcrumb`

### Upload nefunguje
Zkontrolujte:
- Permissions obsahují `upload: true`
- API endpoint je dostupný
- CSRF token je správný

### Context menu se nezobrazuje
Zkontrolujte:
- Permissions obsahují `delete: true` nebo `rename: true`
- JavaScript moduly jsou správně načtené
- Event listenery jsou připojené

## Drag & Drop funkcionalita

### Přehled
File browser komponenta podporuje drag & drop přesouvání souborů mezi různými file browser instancemi na stejné stránce.

### Architektura

#### `drag_drop_manager.js`
- **Účel**: Centrální správa drag & drop operací mezi všemi file browser instancemi
- **Singleton pattern**: Globální instance `globalDragDropManager`
- **Registry systém**: Automatická registrace všech file browser instancí
- **Event delegation**: Funguje i po AJAX refresh obsahu

### Klíčové funkce

#### 1. Registrace instancí
```javascript
// Automatická registrace při connect()
globalDragDropManager.registerFileBrowser(instanceId, controller)

// Automatické odregistrování při disconnect()
globalDragDropManager.unregisterFileBrowser(instanceId)
```

#### 2. Drag detection
- Detekce začátku drag operace na souborech
- Visual feedback s CSS třídou `.dragging`
- Uložení informací o tažené položce

#### 3. Drop detection
- Validace drop operací (nelze dropnout do stejného file browseru)
- Drop zone highlighting s CSS třídou `.drop-zone-active`
- Visual overlay s instrukcemi pro uživatele

#### 4. API integrace
- Skutečné API volání na `/api/internal/v1/soubor/{fileId}/move-to-folder/{targetFolderId}`
- Kompletní error handling
- Success/error notifikace
- Automatický refresh obou file browserů

### Použití

#### Více file browserů na stránce
```twig
{# File browser 1 #}
{{ include('components/file_browser/file_browser.html.twig', {
    slozka: slozka1,
    soubory: soubory1,
    podslozky: podslozky1,
    breadcrumb: breadcrumb1
}) }}

{# File browser 2 #}
{{ include('components/file_browser/file_browser.html.twig', {
    slozka: slozka2,
    soubory: soubory2,
    podslozky: podslozky2,
    breadcrumb: breadcrumb2
}) }}
```

#### Automatická funkcionalita
- Každý file browser se automaticky registruje
- Drag & drop funguje mezi všemi instancemi
- Žádná dodatečná konfigurace není potřeba

### CSS styly

#### `assets/styles/drag_drop.css`
```css
/* Dragged element */
.dragging {
    opacity: 0.5;
    transform: scale(0.95);
    border: 2px dashed #3b82f6;
}

/* Drop zone */
.drop-zone-active {
    background-color: #dbeafe;
    border: 2px dashed #3b82f6;
}

/* Overlay s instrukcemi */
.drop-zone-active::before {
    content: "📁 Přetáhněte sem soubor";
    /* ... další styly */
}
```

### API endpoint

#### Move file endpoint
```php
// POST /api/internal/v1/soubor/{fileId}/move-to-folder/{targetFolderId}
// Response: SuccessResponse s detaily přesunutí
```

### Omezení

#### Co lze táhnout
- ✅ Jednotlivé soubory
- ❌ Více souborů najednou
- ❌ Složky

#### Kam lze dropnout
- ✅ Do jiného file browseru (aktuálně zobrazená složka)
- ❌ Do stejného file browseru

### Placeholdery pro budoucnost

#### Oprávnění
```php
// V MoveFileController
private function canMoveFile($soubor, $targetSlozka): bool {
    // TODO: Implementovat skutečnou logiku oprávnění
    return true; // Prozatím vše povoleno
}
```

#### Validace
```php
// V MoveFileController
private function validateMove($soubor, $targetSlozka): array {
    // TODO: Implementovat business rules validaci
    return ['valid' => true]; // Prozatím vše validní
}
```

### Debug a troubleshooting

#### Console logy
```javascript
// Úspěšný flow:
🚀 Drag started: {fileId: X, sourceSlozkaId: Y}
🎯 Drop detected: {fileId: X, targetSlozkaId: Z}
🚀 Starting API call to move file X to folder Z
✅ File move successful
🔄 Refreshing file browsers
```

#### Časté problémy

**Drag nefunguje:**
- Zkontrolujte, že soubory mají `draggable="true"` atribut
- Ověřte, že DragDropManager je správně inicializován

**Drop nefunguje:**
- Zkontrolujte console logy pro chyby
- Ověřte, že API endpoint je dostupný
- Zkontrolujte oprávnění uživatele

**File browsery se nerefreshují:**
- Zkontrolujte, že controller má `refresh()` metodu
- Ověřte síťové připojení pro API volání
