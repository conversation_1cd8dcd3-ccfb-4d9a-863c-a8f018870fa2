# 🏢 InIn - webová aplikace pro správu pojistných událostí

## 📋 Obsah
- [P<PERSON>edpoklady](#předpoklady)
- [Quickstart](#quickstart)
- [Vývoj](#vývoj)
- [Příkazy Make](#příkazy-make)
- [<PERSON>r<PERSON><PERSON> s aplikací](#práce-s-aplikací)
- [Design](#design)
- [API](#api)
- [Logy](#logy)
- [Deployment](#deployment)
- [Testing](#testing)
- [Struktura projektu](#struktura-projektu)
- [Řešení problémů](#řešení-problémů)
- [Autoři a spolupracovníci](#autoři-a-spolupracovníci)

## 🔧 Předpoklady

Co potřebujete k instalaci softwaru a jak ho nainstalovat:

- 🗄️ MariaDB, ideálně v 11 a vyšší
- 🟢 Node.js (v.20)
- 🐘 PHP (8.3)
- 🎼 Composer
- 🌿 Git

## 🚀 Quickstart

### Instalace

1. Naklonování tohoto repozitáře
   ```bash
   <NAME_EMAIL>:umimeweby/inin.git
   cd inin
   ```

2. Použijte make pro inicializaci projektu
   ```bash
   make init
   ```
   
   Nebo manuálně:
   ```bash
   composer install
   npm install && npm run dev
   ```

3. Nastavte databázi
   ```bash
   make database-reset
   ```

4. Spusťte vývojový server
   ```bash
   symfony server:start
   ```

## 💻 Vývoj

1. Můžete využít lokální symfony webserver
   ```bash
   symfony server:start
   ```
   Více informací: https://symfony.com/doc/current/setup/symfony_server.html

2. Pro automatické generování stylů spusťte
   ```bash
   npm run watch
   ```

3. Přidejte [Tailwind utility classes](https://tailwindcss.com/docs/utility-first) podle libosti.

4. Před pushnutím do gitu vždy otestujte kód pomocí
   ```bash
   make pre-commit
   ```
   nebo
   ```bash
   composer phpstan
   ```

## ⚙️ Příkazy Make

Projekt obsahuje Makefile s užitečnými příkazy:

| Příkaz | Popis |
|--------|-------|
| `make init` | Inicializace celého projektu (backend + frontend) |
| `make init-be` | Inicializace pouze backendu |
| `make init-fe` | Inicializace pouze frontendu |
| `make pre-commit` | Spuštění kontrol před commitem (phpstan, validace schématu) |
| `make database-reset` | Reset databáze, migrace a načtení fixtures |
| `make migrations-diff` | Vytvoření nové migrace na základě změn v entitách |
| `make migrations-migrate` | Spuštění všech migrací |
| `make load-fixtures` | Načtení testovacích dat |

## 📱 Práce s aplikací

### Přihlášení
- Pro přihlášení použijte testovací účty vytvořené pomocí fixtures:
  - Admin: `'<EMAIL>` až `<EMAIL>` (heslo: `tajneHeslo123456`)
  - Likvidátor: `<EMAIL>` až `<EMAIL>` (heslo: `tajneHeslo123456`)

### Hlavní funkce
- Správa pojistných událostí
- Správa uživatelů a rolí
- Správa dokumentů
- Likvidace pojistných událostí

## 🎨 Design

Pro stylování front endu používáme Tailwind a knihovnu předpřipravených designových bloků Flowbite.

- [Tailwind](https://tailwindcss.com/docs/utility-first) - Utility-first CSS framework
- [Flowbite](https://flowbite.com/blocks/) - Knihovna komponent (máme licenci na PRO verzi)

Pro stylování v rámci tohoto projektu byly použity tyto komponenty:

- [Dashboard a obálka aplikace](https://flowbite.com/blocks/application/shells/#application-shell-with-sidebar-and-navbar)
- [Login stránka](https://flowbite.com/blocks/marketing/login/#default-login-page)
- [CRUD - create form](https://flowbite.com/blocks/marketing/login/#default-login-page)
- [CRUD - List table](https://flowbite.com/blocks/application/advanced-tables/#user-management-table-(crud))

## 🔌 API

Aplikace poskytuje dva typy API rozhraní: interní pro potřeby aplikace a externí pro integraci s dalšími systémy.

### Dokumentace API
Kompletní interaktivní dokumentace API je dostupná na:
- **Externí API**: `/api/doc/external` (vyžaduje Basic Auth přihlášení)
- **Interní API**: `/api/doc/internal` (přístupné pouze z localhostu)

Dokumentace obsahuje:
- Kompletní seznam všech endpointů
- Popis požadavků a odpovědí
- Možnost přímého testování endpointů
- Příklady použití

### Autentizace a zabezpečení
Každý typ API má vlastní autentizační mechanismus definovaný v `config/packages/security.yaml`:

- **Interní API** je dostupné pouze z localhostu (IP adresa 127.0.0.1) pro zajištění bezpečnosti interních operací
- **Externí API** je veřejně dostupné, ale může vyžadovat autentizaci podle konkrétního endpointu

#### Entity-level autorizace
Aplikace implementuje pokročilý systém autorizace na úrovni entit pomocí Symfony Voters:

- **PojistnaUdalostVoter**: Kontroluje přístup k pojistným událostem s granulárními oprávněními:
  - `VIEW`: zobrazení dat pojistné události
  - `EDIT`: úprava dat, nahrávání souborů, vytváření složek
  - `DELETE_FILE`: mazání souborů a složek

**Přístup k pojistné události mají:**
- Uživatelé s rolí `ROLE_COM_ADMIN` - všechna oprávnění ke všem PU
- Přiřazený likvidátor (`pojistnaUdalost.likvidator`) - všechna oprávnění k přiřazené PU
- Další přiřazení uživatelé (`pojistnaUdalost.dalsiUzivatele`) - všechna oprávnění k přiřazené PU
- Uživatelé s rolí `ROLE_LIKVIDATOR` - prozatím všechna oprávnění (bude omezeno v budoucnu)

**Zabezpečené API operace:**
- Mazání složek a souborů (`DELETE_FILE`)
- Přesouvání souborů mezi složkami (`EDIT` pro source i target PU)
- Vytváření nových složek (`EDIT`)
- Přejmenování souborů (`EDIT`)
- Nahrávání souborů (`EDIT`)

### Struktura API
- **Interní API**: `https://{hostName}/api/internal/v1/*`
- **Externí API**: `https://{hostName}/api/v1/external/*`

Pro detailní popis všech endpointů, včetně parametrů, příkladů požadavků a odpovědí, prosím využijte interaktivní dokumentaci dostupnou na výše uvedených URL.

### Technické detaily
Dokumentace je generována pomocí NelmioApiDocBundle a odpovídá specifikaci OpenAPI 3.0. Pro vývojáře je k dispozici detailní popis práce s dokumentací v sekci pro vývojáře v interaktivní dokumentaci.

### Logy

Aplikace implementuje komplexní systém logování API komunikace, který rozlišuje mezi externími a interními API voláními.

#### Struktura logů
Logy jsou ukládány ve struktuře:

```
var/log/
└── api/
├── outgoing/
│   └── api_outgoing_[environment].log    # Externí odchozí API volání
├── incoming/
│   └── api_incoming_[environment].log    # Externí příchozí API volání
├── internal/
│   └── api_internal_[environment].log    # Interní API komunikace
└── errors/
└── api_error_[environment].log       # API chyby
```

#### Typy logovaných informací
Každý log záznam obsahuje:
- HTTP metodu
- URL cestu
- Hlavičky požadavku (citlivé údaje jsou redakovány)
- Tělo požadavku (JSON)
- Status kód odpovědi
- Čas zpracování
- IP adresu klienta
- Další metadata

#### Kritické chyby
V produkčním prostředí jsou kritické chyby:
- Logovány do speciálního error logu
- Odesílány e-mailem na konfigurovanou adresu

#### Konfigurace
Úrovně logování jsou nastaveny podle prostředí:
- Development: úroveň `debug` (všechny informace)
- Production: úroveň `info` (standardní provoz) a `error` (chyby)

#### Bezpečnost
- Citlivé údaje (hesla, tokeny, API klíče) jsou automaticky redakovány
- Logy jsou ukládány ve formátu JSON pro snadné zpracování
- na serveru je implementována rotace logů pro správu velikosti souborů

#### Přístup k logům
Pro přístup k API logům v produkčním prostředí kontaktujte administrátora systému. V development prostředí jsou logy dostupné v adresáři `var/log/api/`.

#### LogRotate
Jak na demo tak na produkci je nastaveno logrotate api logů a to podle konfigurace níže. Logy se rotují denně a uchovávají se 120 dnů (4 měsíce)

```
/home/<USER>/public_html/shared/var/log/api/*/*.log {
    su ininprod ininprod
    daily
    missingok
    rotate 120
    compress
    delaycompress
    dateext
    dateformat -%Y%m%d
    notifempty
    create 0640 ininprod ininprod
    sharedscripts
}

```




## 🚢 Deployment

V rámci projektu je instalovaný deployer pomocí composeru. 

### Manuální deployment

Migrace na demo se spouští příkazem:
```bash
composer deployer deploy demo
```

Je potřeba mít na demo serveru u uživatele `inin` veřejný ssh klíč počítače / účtu, ze kterého se dělá deployment.

### Automatický deployment pomocí Bitbucket Pipelines

Projekt je nakonfigurován pro automatický deployment na demo server pomocí Bitbucket Pipelines. Automatický deployment se spustí při:
- Pushnutí do větve `demo`
- Vytvoření tagu

Proces automatického deploymentu zahrnuje:
1. Build a test - instalace závislostí a spuštění PHPStan analýzy
2. Deployment - nasazení aplikace na demo server pomocí Deployeru
3. Migrace - spuštění databázových migrací pro aktualizaci struktury databáze
4. Fixtures - automatické načtení demo dat do databáze

Pro správné fungování automatického deploymentu je potřeba:
1. Mít v Bitbucketu vytvořené deployment prostředí s názvem `demo`
2. Mít v Bitbucketu vygenerovaný SSH klíč (Repository settings > Access keys)
3. Mít veřejnou část SSH klíče přidanou do `~/.ssh/authorized_keys` na demo serveru

Tento automatizovaný proces zajišťuje, že demo server vždy obsahuje aktuální verzi aplikace s aktuálními demo daty.

## 🧪 Testing

### Fixtures
Pro testování můžete použít připravené fixtures, které vytvoří testovací data:

```bash
make load-fixtures
```

nebo

```bash
php bin/console doctrine:fixtures:load --no-interaction
```

Tím se vytvoří:
- Tři uživatelé s rolí admin: `<EMAIL>` až `<EMAIL>` (heslo: `tajneHeslo123456`)
- Tři uživatelé s rolí likvidátor: `<EMAIL>` až `<EMAIL>` (heslo: `tajneHeslo123456`)
- Testovací pojistné události, zadavatelé a další data

## 📂 Struktura projektu

- `assets/` - Frontend soubory (JavaScript, CSS)
  - `controllers/file_browser/` - **Modularní file browser s drag & drop** ([detailní dokumentace](FILE_BROWSER_ARCHITECTURE.md))
- `bin/` - Spustitelné soubory (console)
- `config/` - Konfigurační soubory
- `migrations/` - Databázové migrace
- `public/` - Veřejně dostupné soubory
- `src/` - Zdrojový kód aplikace
  - `Controller/` - Kontrolery
  - `Entity/` - Databázové entity
  - `Repository/` - Repozitáře pro práci s entitami
  - `Service/` - Služby
  - `Dto/` - Data Transfer Objects
  - `DataFixtures/` - Testovací data
- `templates/` - Twig šablony
- `translations/` - Překlady
- `tests/` - Testy

## 🔐 Proměnné prostředí

Aplikace používá proměnné prostředí pro konfiguraci různých aspektů systému. Tyto proměnné jsou definovány v `.env` souboru a mohou být přepsány v `.env.local` (který není verzován v Gitu) nebo v prostředí serveru.

### Jak nakonfigurovat proměnné prostředí

1. Zkopíruj `.env` do `.env.local`
2. Uprav hodnoty v `.env.local` podle potřeby
3. Nikdy neukládej citlivé údaje (hesla, API klíče) do verzovaných souborů (`.env`, `.env.dev`)

### Produkční a demo prostředí

Na produkčním a demo serveru je použitý soubor `.env.local.php` podle [oficiální dokumentace Symfony](https://symfony.com/doc/current/configuration.html#configuring-environment-variables-in-production). Tento soubor je generován příkazem:

```bash
composer dump-env prod
```

Tento přístup optimalizuje výkon aplikace v produkčním prostředí, protože:
- Eliminuje potřebu parsování `.env` souborů při každém požadavku
- Ukládá všechny proměnné prostředí do jednoho PHP souboru
- Zrychluje načítání aplikace

### Standardní Symfony proměnné

| Proměnná | Popis | Příklad hodnoty |
|----------|-------|-----------------|
| `APP_ENV` | Prostředí aplikace (dev, prod, test) | `dev` |
| `APP_SECRET` | Tajný klíč pro zabezpečení session a tokenů | `fda7174cf4bf98767905487na067e438` |
| `DATABASE_URL` | Připojovací řetězec k databázi | `mysql://someuser:@127.0.0.1:3306/dbname?serverVersion=10.11.2-MariaDB&charset=utf8mb4` |
| `MESSENGER_TRANSPORT_DSN` | Konfigurace pro Symfony Messenger | `doctrine://default?auto_setup=0` |
| `MAILER_DSN` | Konfigurace pro odesílání e-mailů | `smtp://:@localhost:1025` |

### Vlastní proměnné aplikace

| Proměnná | Popis | Příklad hodnoty |
|----------|-------|-----------------|
| `ERROR_MAIL_FROM` | E-mailová adresa, ze které se odesílají chybové zprávy | `<EMAIL>` |
| `ERROR_MAIL_TO` | E-mailová adresa, na kterou se odesílají chybové zprávy | `<EMAIL>` |
| `NOTIFICATION_WHITELIST` | Seznam povolených IP adres pro přístup k API Notification Endpoint (oddělené čárkou) | `127.0.0.1,************/27` |
| `BASIC_AUTH_USERNAME` | Uživatelské jméno pro Basic Auth API | `apiuser` |
| `BASIC_AUTH_PASSWORD` | Heslo pro Basic Auth API | `securepassword` |
| `API_DOC_PASSWORD` | Hashované heslo pro přístup k API dokumentaci | `$2y$13$gcnXRd0Gjr1hk/cnTLnWguQj8.q3ZNORunLArZECWffbsra5xAAvW` |

Hashované heslo pro API dokumentaci se vytvoří pomocí CLI příkazu `php bin/console security:hash-password`. Je potřeba vybrat hashování pro InMemory uživatele (`Symfony\Component\Security\Core\User\InMemoryUser`)

### Proměnné pro testovací prostředí

V testovacím prostředí (`.env.test`) jsou definovány další specifické proměnné:

| Proměnná | Popis | Příklad hodnoty |
|----------|-------|-----------------|
| `KERNEL_CLASS` | Třída jádra aplikace pro testy | `App\Kernel` |
| `SYMFONY_DEPRECATIONS_HELPER` | Nastavení pro potlačení varování o zastaralých funkcích | `999999` |
| `PANTHER_APP_ENV` | Prostředí pro Panther testy | `panther` |
| `PANTHER_ERROR_SCREENSHOT_DIR` | Adresář pro ukládání screenshotů chyb při testování | `./var/error-screenshots` |

### Důležité poznámky

- **Bezpečnost**: Nikdy neukládej citlivé údaje do verzovaných souborů
- **Produkce**: Pro produkční nasazení používej `composer dump-env prod` pro kompilaci `.env` souborů
- **Lokální vývoj**: Používej `.env.local` pro lokální nastavení, která se liší od výchozích hodnot
- **Priorita**: Skutečné proměnné prostředí mají přednost před `.env` soubory

## ❓ Řešení problémů

### Problémy s databází
Pokud narazíte na problémy s databází, zkuste:
```bash
make database-reset
```

### Problémy s assets
Pokud se nezobrazují správně styly nebo JavaScript:
```bash
npm run dev
bin/console assets:install
```

### Problémy s Composer
Pokud narazíte na problémy s Composerem:
```bash
composer clear-cache
composer install
```

## 👥 Autoři a spolupracovníci

- Richard Koza - hlavní programátor
- Vít Šafařík - programátor
- Matěj Kohout - programátor
- Rostislav Kavan - programátor
