# Read the documentation at https://github.com/thephpleague/flysystem-bundle/blob/master/docs/1-getting-started.md
flysystem:
    storages:
        default.storage:
            adapter: 'local'
            options:
                directory: '%kernel.project_dir%/var/storage/default'
        slozky.storage:
            adapter: 'local'
            options:
                directory: '%uploaded_files_target_directory%'
        pojistnePodminky.storage:
            adapter: 'local'
            options:
                directory: '%pojistne_podminky_soubor_directory%'
        likvidacniProtokol.storage:
            adapter: 'local'
            options:
                directory: '%likvidacni_protokol_directory%'                
