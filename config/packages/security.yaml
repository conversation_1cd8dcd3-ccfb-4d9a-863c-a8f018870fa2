security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
        Symfony\Component\Security\Core\User\InMemoryUser: 'auto'  # přidáno pro basic auth

    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Entity\User
                property: email
        # Přidáme nového providera pro API doc
        api_doc_users:
            memory:
                users:
                    apidoc: { password: '%env(API_DOC_PASSWORD)%', roles: ['ROLE_API_DOC'] }

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        # Přidáme nový firewall pro API dokumentaci (MUSÍ BÝT PŘED main!)
        api_doc:
            pattern: ^/api/doc/external
            provider: api_doc_users
            http_basic: ~            
        main:
            pattern: ^/
            user_checker: App\Security\UserChecker
            lazy: true
            provider: app_user_provider
            custom_authenticator: App\Security\LoginFormAuthenticator
            logout:
                path: app_logout
                # where to redirect after logout
                target: app_homepage

            remember_me:
                secret: '%kernel.secret%'
                lifetime: 604800
                path: /
                always_remember_me: true

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    role_hierarchy:
        ROLE_LIKVIDATOR: ROLE_USER
        ROLE_COM_ADMIN: [ROLE_LIKVIDATOR, ROLE_USER]
        ROLE_UW_ADMIN: [ROLE_LIKVIDATOR, ROLE_COM_ADMIN, ROLE_ALLOWED_TO_SWITCH]            

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        # Dokumentace API
        - { path: ^/api/doc/external, roles: ROLE_API_DOC }
        - { path: ^/api/doc/internal, roles: PUBLIC_ACCESS, ips: [127.0.0.1] }            
        # API přístup
        - { path: ^/api/internal/, roles: PUBLIC_ACCESS, ips: [127.0.0.1] }
        - { path: ^/api/v1/external/\w+/claims/notification$, methods: [POST], roles: PUBLIC_ACCESS, ips: '%env(NOTIFICATION_WHITELIST)%' }
        - { path: ^/api, roles: PUBLIC_ACCESS }
        - { path: ^/login, roles: PUBLIC_ACCESS }
        - { path: ^/reset-password, roles: PUBLIC_ACCESS }
        - { path: ^/, roles: ROLE_USER }
        # - { path: ^/profile, roles: ROLE_USER }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
