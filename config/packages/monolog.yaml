monolog:
    channels:
        - deprecation # Deprecations are logged in the dedicated "deprecation" channel when it exists
        - api_external_outgoing
        - api_external_incoming
        - api_internal        

when@dev:
    monolog:
        handlers:
            main:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug
                channels: ["!event","!api_external_outgoing", "!api_external_incoming", "!api_internal"]
            # uncomment to get logging in your browser
            # you may have to allow bigger header sizes in your Web server configuration
            #firephp:
            #    type: firephp
            #    level: info
            #chromephp:
            #    type: chromephp
            #    level: info
            console:
                type: console
                process_psr_3_messages: false
                channels: ["!event", "!doctrine", "!console"]

            # Nové API handlery
            api_external_outgoing:
                type: stream
                path: "%kernel.logs_dir%/api/outgoing/api_outgoing_%kernel.environment%.log"
                level: debug
                channels: ["api_external_outgoing"]
                formatter: monolog.formatter.json
            
            api_external_incoming:
                type: stream
                path: "%kernel.logs_dir%/api/incoming/api_incoming_%kernel.environment%.log"
                level: debug
                channels: ["api_external_incoming"]
                formatter: monolog.formatter.json
            
            api_internal:
                type: stream
                path: "%kernel.logs_dir%/api/internal/api_internal_%kernel.environment%.log"
                level: debug
                channels: ["api_internal"]
                formatter: monolog.formatter.json
            
            api_errors:
                type: stream
                path: "%kernel.logs_dir%/api/errors/api_errors_%kernel.environment%.log"
                level: error
                channels: ["api_external_outgoing", "api_external_incoming", "api_internal"]
                formatter: monolog.formatter.json                

when@test:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                action_level: error
                handler: nested
                excluded_http_codes: [404, 405]
                channels: ["!event","!api_external_outgoing", "!api_external_incoming", "!api_internal"]
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug

            # API handlery pro testovací prostředí
            api_external_outgoing:
                type: stream
                path: "%kernel.logs_dir%/api/outgoing/api_outgoing_%kernel.environment%.log"
                level: debug
                channels: ["api_external_outgoing"]
                formatter: monolog.formatter.json

            api_external_incoming:
                type: stream
                path: "%kernel.logs_dir%/api/incoming/api_incoming_%kernel.environment%.log"
                level: debug
                channels: ["api_external_incoming"]
                formatter: monolog.formatter.json

            api_internal:
                type: stream
                path: "%kernel.logs_dir%/api/internal/api_internal_%kernel.environment%.log"
                level: debug
                channels: ["api_internal"]
                formatter: monolog.formatter.json                

when@prod:
    monolog:
        handlers:
            main:
                type: fingers_crossed
                action_level: error
                handler: nested
                excluded_http_codes: [404, 405]
                buffer_size: 50 # How many messages should be saved? Prevent memory leaks
            nested:
                type: stream
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                level: debug
                formatter: monolog.formatter.json
            console:
                type: console
                process_psr_3_messages: false
                channels: ["!event", "!doctrine"]
            deprecation:
                type: stream
                channels: [deprecation]
                path: "%kernel.logs_dir%/%kernel.environment%.log"
                formatter: monolog.formatter.json

            # API handlery pro produkční prostředí
            api_external_outgoing:
                type: stream
                path: "%kernel.logs_dir%/api/outgoing/api_outgoing_%kernel.environment%.log"
                level: info # Na produkci možná chceme vyšší úroveň
                channels: ["api_external_outgoing"]
                formatter: monolog.formatter.json
                buffer_size: 50

            api_external_incoming:
                type: stream
                path: "%kernel.logs_dir%/api/incoming/api_incoming_%kernel.environment%.log"
                level: info
                channels: ["api_external_incoming"]
                formatter: monolog.formatter.json
                buffer_size: 50

            api_internal:
                type: stream
                path: "%kernel.logs_dir%/api/internal/api_internal_%kernel.environment%.log"
                level: info
                channels: ["api_internal"]
                formatter: monolog.formatter.json
                buffer_size: 50

            api_errors:
                type: stream
                path: "%kernel.logs_dir%/api/errors/api_errors_%kernel.environment%.log"
                level: error
                channels: ["api_external_outgoing", "api_external_incoming", "api_internal"]
                formatter: monolog.formatter.json
                buffer_size: 50


            # Handler pro kritické API chyby - email
            api_critical_errors_email:
                type: symfony_mailer
                from_email: '%env(ERROR_MAIL_FROM)%'
                to_email: '%env(ERROR_MAIL_TO)%'
                subject: 'ININ PROD API Critical Error!'
                level: critical
                formatter: monolog.formatter.html
                content_type: text/html
                channels: ["api_external_outgoing", "api_external_incoming", "api_internal"]

            # Handler pro kritické API chyby - log soubor
            api_critical_errors_log:
                type: stream
                path: "%kernel.logs_dir%/api/errors/api_errors_%kernel.environment%.log"
                level: critical
                channels: ["api_external_outgoing", "api_external_incoming", "api_internal"]
                formatter: monolog.formatter.json
                buffer_size: 50

            # Společný group handler pro kritické chyby
            api_critical_errors:
                type: group
                members: ["api_critical_errors_email", "api_critical_errors_log"]
                level: critical
                channels: ["api_external_outgoing", "api_external_incoming", "api_internal"]                
