<?php

/**
 * InIn DEPLOY SCRIPT 
 * 
 * last update 2025-04-19-1100
 **/

// dep deploy <stage>              // start deploy
// dep uw:migration-status <stage>    // check migrations (optional)
// dep database:migrate <stage>    // run migrations (optional)

namespace Deployer;

require 'recipe/symfony.php';

// Usable stages
// demo - demo server, staging, tests
// prod - production server
$demo_stage = 'demo';
$production_stage = 'prod';


// Config

// enable or disable git tagging
set('git_enable_tagging', true);  // Lze vypnout/zapnout tagging

set('repository', '*****************:umimeweby/inin.git');

add('shared_files', []);
add('shared_dirs', ['/var/storage']);
add('writable_dirs', []);

# set php version 
set('bin/php', function () {
    return '/usr/bin/php8.3';
});

// [Optional] Allocate tty for git clone. Default value is false.
// FOR WINDOWS SETTO FALSE
set('git_tty', true);

set('allow_anonymous_stats', false);

//UW Setup

$GLOBALS['env_variables'] = [
    "APP_ENV"      => 'prod',
    "MAILER_URL"      => 'nejakeURL',
    "APP_SECRET"   => "825217e6f759596201e3b2b6d133a11d",
];

// Hosts

host($demo_stage)
    ->set('deploy_path', '/home/<USER>/public_html')
    ->setHostname('tesarik.uwserver.cz')
    ->setRemoteUser('inin')
    ->set('http_user', 'inin')
    ->set('branch', 'demo')
    ->set('repository', '*****************:umimeweby/inin.git')
    ->add('shared_files', ['.env.local', '.env.local.php'])
    ->set('symfony_env', 'prod');

host($production_stage)
    ->set('deploy_path', '/home/<USER>/public_html')
    ->setHostname('ininprod.uwclient.cz')
    ->setRemoteUser('ininprod')
    ->set('remote_user', 'ininprod')
    ->set('http_user', 'ininprod')
    ->set('branch', 'master')
    ->set('repository', '*****************:umimeweby/inin.git')
    ->add('shared_files', ['.env.local', '.env.local.php'])
    ->set('symfony_env', 'prod');


// Hooks

after('deploy:failed', 'deploy:unlock');
after('deploy:vendors', 'uw:assets-compile');

if (get('git_enable_tagging', true)) {
    after('deploy:success', 'uw:create-git-tag');
}


// Needed to set APP_ENV variable to prod for deplyoment process and other before after settings
before('deploy', 'uw:setprodenv');

//tasks

// deployment task - needed - uw prepared

task('uw:setprodenv', function () {

    set('env', $GLOBALS['env_variables']);
    run('env');
});

task('uw:assets-compile', function () {
    $result = run('cd {{release_path}} && npm install');;
    writeln($result);
    $result = run('cd {{release_path}} && npm run build');
    writeln("npm: $result");
});


// tests and reporting  - uw prepared

task('test', function () {
    writeln('The {{alias}} is {{hostname}}');
});



task('uw:show-env', function () {
    $result = run('env');
    $result2 = remoteEnv();
    writeln($result);
    foreach ($result2 as $key => $value) {
        writeln($key . ' => ' . $value);
    }
});

task('uw:show-php-v', function () {
    $result = get('bin/php');
    writeln($result);
    $result = run('{{bin/php}} -v');
    writeln($result);
    $result = get('bin/composer');
    writeln($result);
    $result = run('{{bin/composer}}');
    writeln($result);
    $result = get('bin/console');
    writeln($result);
    $result = run('{{bin/console}}');
    writeln($result);
    $result = get('deploy_path');
    writeln($result);
});

task('uw:npm-v', function () {
    $result = run('npm -v');
    writeln('npm version ' . $result);
    $result = run('node -v');
    writeln('node version ' . $result);
});

// migrations related  - uw prepared

task('uw:migration-status', function () {
    $result = run('{{bin/console}} doctrine:migrations:status');
    writeln($result);
});

// fixtures related
task('database:fixtures', function () {
    if (get('stage') === 'demo') {  // Spustit pouze pro demo prostředí
        try {
            $result = run('{{bin/console}} list | grep doctrine:fixtures:load');
            if ($result) {
                writeln('Loading fixtures for demo environment...');
                $result = run('{{bin/console}} doctrine:fixtures:load --no-interaction');
                writeln($result);
            } else {
                writeln('Doctrine fixtures bundle not installed, skipping fixtures load.');
            }
        } catch (\Exception $e) {
            writeln('Error checking for fixtures command: ' . $e->getMessage());
            writeln('Skipping fixtures load.');
        }
    } else {
        writeln('Not a demo environment, skipping fixtures load.');
    }
});

// create git tag related
task('uw:create-git-tag', function () {
    date_default_timezone_set("Europe/Prague");
    $timestamp = date('Ymd-His');

    $branch = currentHost()->get('branch');
    $environment = currentHost()->get('alias');

    // Ověřit, že máme potřebné hodnoty
    if (empty($branch)) {
        warning('Branch není definována. Tag nebude vytvořen.');
        return;
    }

    try {
        // Pracujeme přímo v aktuálním adresáři pipeline
        $workDir = runLocally('pwd');
        info("Použiji pracovní adresář: $workDir");
        
        // Nastavení Git uživatele pro commit (nutné v pipeline)
        runLocally('git config --global user.email "<EMAIL>"');
        runLocally('git config --global user.name "Deployment Bot"');
        
        // Zjistíme aktuální větev a commit
        $currentBranch = runLocally('git rev-parse --abbrev-ref HEAD');
        info("Aktuální větev: $currentBranch");
        
        // Ověříme, že jsme na správné větvi nebo se na ni přepneme
        if ($currentBranch !== $branch) {
            info("Přepínám na větev $branch");
            runLocally("git checkout $branch");
        }
        
        // Získáme hash posledního commitu
        $gitHash = runLocally('git rev-parse --short HEAD');
        $tagName = "deploy-{$environment}-{$timestamp}-{$gitHash}";
        
        info("Vytvářím tag: {$tagName}");
        runLocally("git tag {$tagName}");
        
        // Pushujeme tag pomocí SSH klíče nakonfigurovaného v Bitbucket
        info("Odesílám tag do repozitáře...");
        runLocally("git push origin {$tagName}");
        
        info("Tag {$tagName} byl úspěšně vytvořen a odeslán do repozitáře");
    } catch (\Exception $e) {
        warning("Chyba při vytváření tagu: " . $e->getMessage());
        // Zachytíme výjimku, aby deployment mohl pokračovat i když tagging selže
    }
})->desc('Create git tag for successful deployment');


// test task to show host config 
task('uw:show-config', function () {

    $keys = currentHost()->config()->keys();
    foreach ($keys as $onekey) {
        writeln("key: " . $onekey);
        $value = currentHost()->get($onekey);
        writeln("value: " . $value);
    }
});
