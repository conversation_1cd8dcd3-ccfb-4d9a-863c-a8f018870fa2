{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.12", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.1", "knplabs/knp-paginator-bundle": "^6.3", "league/flysystem-bundle": "^3.3", "nelmio/api-doc-bundle": "^5.0", "phpdocumentor/reflection-docblock": "^5.3", "phpstan/phpdoc-parser": "^1.27", "symfony/asset": "7.2.*", "symfony/console": "7.2.*", "symfony/doctrine-messenger": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/flex": "^2", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/http-client": "7.2.*", "symfony/intl": "7.2.*", "symfony/mailer": "7.2.*", "symfony/mime": "7.2.*", "symfony/monolog-bundle": "^3.10", "symfony/notifier": "7.2.*", "symfony/process": "7.2.*", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/stimulus-bundle": "^2.16", "symfony/string": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/validator": "7.2.*", "symfony/web-link": "7.2.*", "symfony/webpack-encore-bundle": "^2.1", "symfony/yaml": "7.2.*", "symfonycasts/reset-password-bundle": "^1.21", "tales-from-a-dev/flowbite-bundle": "^0.6.0", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true, "phpstan/extension-installer": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "phpstan": "vendor/bin/phpstan analyse -l 6 src tests --xdebug", "validate-schema": "php bin/console doctrine:schema:validate", "deployer": "vendor/bin/dep"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "require-dev": {"deployer/deployer": "^7.5", "doctrine/doctrine-fixtures-bundle": "^3.6", "fakerphp/faker": "^1.24", "phpstan/extension-installer": "^1.3", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "^1.58", "symfony/phpunit-bridge": "^7.0", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*"}}